import { Base64 } from 'js-base64';

export default {
  isDoc: function(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg: function(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  random4: function() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  },
  createUUID: function() {
    return (
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4()
    );
  },
  viewerDocBase: function(path, filename) {
    var url = '';
    if (path.indexOf('http') >= 0) {
      url = path + '?fullfilename=' + filename;
    } else {
      url = `${this.$documentPreviewHost}${path}?fullfilename=${filename}`;
    }
    let a = document.createElement('a');
    a.target = '_blank';
    a.href =
      location.origin +
      '/ts-preview/onlinePreview?url=' +
      encodeURIComponent(Base64.encode(url));
    a.click();
    return false;
  },
  deepClone: function(source) {
    if (!source && typeof source !== 'object') {
      throw new Error('error arguments', 'deepClone');
    }
    const targetObj = source.constructor === Array ? [] : {};
    Object.keys(source).forEach(keys => {
      if (source[keys] && typeof source[keys] === 'object') {
        targetObj[keys] = this.deepClone(source[keys]);
      } else {
        targetObj[keys] = source[keys];
      }
    });
    return targetObj;
  }
};
