<template>
  <u-popup mode="bottom" :show="value" height="100%">
    <u-navbar title="已选人员" title-bold @leftClick="closePopup"></u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入姓名搜索"
        @search="search"
      ></u-search>
    </view>
    <view class="content-list">
      <u-swipe-action
        v-for="(item, index) in filterPersons"
        :key="item[key]"
        :show="item.show"
        :index="index"
        :options="swipeActionOptions"
        @click="click"
        @open="open"
      >
        <view class="u-border-bottom">
          <view class="title-wrap">
            <person-list-item-info
              :person="item"
              :personProp="infoProp"
            ></person-list-item-info>
          </view>
        </view>
      </u-swipe-action>
    </view>
  </u-popup>
</template>

<script>
import personListItemInfo from './person-list-item-info.vue';
export default {
  name: 'choose-person-popup',
  components: {
    personListItemInfo
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    infoProp: {
      type: Object,
      default: () => {
        return {
          name: 'name',
          describe: 'deptName',
          concatSymbol: '',
          key: 'userId'
        };
      }
    }
  },
  data() {
    return {
      keywords: '',
      swipeActionOptions: [
        {
          text: '删除',
          style: {
            backgroundColor: '#dd524d'
          }
        }
      ]
    };
  },
  computed: {
    filterPersons() {
      return this.list.filter(p => {
        p.show = false;
        return p[this.infoProp.name].indexOf(this.keywords) !== -1;
      });
    },
    key() {
      return (this.infoProp && this.infoProp.key) || 'userId';
    }
  },
  methods: {
    closePopup() {
      this.$emit('input', false);
    },
    open(index) {
      this.filterPersons[index].show = true;
      this.filterPersons.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    close(index) {
      this.filterPersons[index].show = false;
    },
    click(index) {
      let clickItem = this.filterPersons[index];
      let choosePersons = this.list.filter(
        i => i[this.key] != clickItem[this.key]
      );
      this.$emit('change', choosePersons);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
}
.title-wrap {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
/deep/ {
  .u-popup__content {
    height: calc(100vh - 44px);
    flex: unset !important;
  }
}
.content-list {
  flex: 1;
  overflow: auto;
}
</style>
