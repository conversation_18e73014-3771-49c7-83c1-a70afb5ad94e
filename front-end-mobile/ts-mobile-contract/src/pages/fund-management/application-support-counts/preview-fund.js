export default {
  data() {
    return {
      editData: {
        itemName: '',
        autograph: ''
      },

      formList: [
        {
          title: '项目名称',
          prop: 'itemName',
          propVal: 'entryItemId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择申请项目',
          optionList: [],
          elementProps: {
            keyName: 'itemName'
          },
          handleConfirm: this.handlePickeItem,
          required: true
        },
        {
          title: '项目负责人',
          prop: 'leaderUserName',
          propVal: 'leaderUserName',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '所在科室',
          prop: 'deptName',
          propVal: 'deptName',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '文件号',
          prop: 'fileNumber',
          propVal: 'fileNumber',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '联系电话',
          prop: 'telephone',
          propVal: 'telephone',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '经费类型',
          prop: 'fundTypeName',
          propVal: 'fundTypeName',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '项目核定经费',
          prop: 'approvedItemFund',
          propVal: 'approvedItemFund',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '附件',
          prop: 'itemFilesId',
          type: 'file',
          required: true,
          disabled: true
        }
      ],
      rules: {
        itemName: [
          {
            required: true,
            message: '请选择申请项目',
            trigger: ''
          }
        ]
      },

      // 配套经费信息保存数据
      supportFormList: [
        {
          title: '医院配套经费',
          prop: 'supportingFund',
          type: 'number',
          mode: 'input',
          placeholder: '请输入医院配套经费',
          required: true
        },
        {
          title: '相关职能科室审核人',
          prop: 'typeApproverName',
          propVal: 'typeApproverCode',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          placeholder: '请选择审核人',
          required: true
        },
        {
          title: '附件',
          prop: 'filesId',
          type: 'file',
          required: true
        },
        {
          title: '备注',
          prop: 'remark',
          propVal: 'remark',
          type: 'textarea',
          mode: 'input',
          placeholder: '请输入备注'
        }
      ],
      supportRules: {
        supportingFund: [
          {
            required: true,
            message: '请输入医院配套经费',
            trigger: ''
          }
        ],
        filesId: [
          {
            required: true,
            message: '请选择申请附件',
            trigger: ''
          }
        ]
      }
    };
  },
  methods: {
    /**@desc 获取负责项目 */
    getItemData() {
      this.ajax
        .getLeaderUserProjectDataList({
          pageNo: 1,
          pageSize: 9999,
          leaderUser: this.userInfo.employeeId
        })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.$set(this.formList[0], 'optionList', [res.rows]);
        });
    },
    /**@desc 选择项目数据 */
    handlePickeItem({ value = [] }) {
      let {
        leaderUserName,
        deptName,
        fileNumber,
        telephone,
        fundTypeName,
        approvedItemFund,
        filesId,
        itemName,
        id
      } = value[0] || {};

      this.$set(this.editData, 'entryItemId', id);
      this.$set(this.editData, 'itemName', itemName);
      this.$set(this.editData, 'leaderUserName', leaderUserName);
      this.$set(this.editData, 'deptName', deptName);
      this.$set(this.editData, 'fileNumber', fileNumber);
      this.$set(this.editData, 'telephone', telephone);
      this.$set(this.editData, 'fundTypeName', fundTypeName);
      this.$set(this.editData, 'approvedItemFund', approvedItemFund);
      this.$set(this.editData, 'itemFilesId', filesId);
    }
  }
};
