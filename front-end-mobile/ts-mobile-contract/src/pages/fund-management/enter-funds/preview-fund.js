export default {
  data() {
    return {
      editData: {
        itemName: '',
        leaderUserName: '',
        deptName: '',
        telephone: '',
        filesId: '',
        autograph: ''
      },

      formList: [
        {
          title: '项目名称',
          prop: 'itemName',
          propVal: 'itemName',
          type: 'text',
          mode: 'input',
          placeholder: '请填写项目名称',
          required: true
        },
        {
          title: '项目负责人',
          prop: 'leaderUserName',
          propVal: 'leaderUser',
          type: 'select',
          mode: 'person',
          placeholder: '请选择项目负责人',
          required: true,
          chooseType: 'radio',
          changeCallback: this.handleLeaderUserChange
        },
        {
          title: '所属科室',
          prop: 'deptName',
          propVal: 'deptCode',
          type: 'text',
          mode: 'input',
          placeholder: '请选择所属科室',
          required: true,
          disabled: true
        },
        {
          title: '联系电话',
          prop: 'telephone',
          propVal: 'telephone',
          type: 'number',
          mode: 'input',
          placeholder: '请输入联系电话',
          maxlength: 11,
          required: true
        },
        {
          title: '文件号',
          prop: 'fileNumber',
          propVal: 'fileNumber',
          type: 'text',
          mode: 'input',
          placeholder: '请输入文件号',
          maxlength: 50,
          required: true
        },
        {
          title: '经费类型',
          prop: 'fundTypeName',
          propVal: 'fundTypeId',
          type: 'select',
          mode: 'select',
          optionList: [],
          elementProps: {
            keyName: 'typeName'
          },
          placeholder: '请选择经费类型',
          required: true,
          handleConfirm: this.handleFundTypeConfirm
        },
        {
          title: '核定项目经费',
          prop: 'approvedItemFund',
          type: 'number',
          mode: 'input',
          placeholder: '请输入核定项目经费（元）',
          labelWidth: 120,
          required: true
        },
        {
          title: '到账时间',
          prop: 'paymentDate',
          type: 'select',
          mode: 'time',
          placeholder: '请选择到账时间',
          required: true
        },
        {
          title: '附件',
          prop: 'filesId',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          required: true,
          deletable: true
        },
        {
          title: '备注',
          prop: 'remark',
          type: 'textarea',
          elementProps: {
            placeholder: '请输入备注'
          }
        }
      ],
      rules: {
        itemName: [
          {
            required: true,
            message: '请填写项目名称',
            trigger: ''
          }
        ],
        leaderUserName: [
          {
            required: true,
            message: '请选择项目负责人',
            trigger: ''
          }
        ],
        fileNumber: [
          {
            required: true,
            message: '请输入文件号',
            trigger: ''
          }
        ],
        approvedItemFund: [
          {
            required: true,
            message: '请输入核定项目经费',
            transform(value) {
              return String(value);
            },
            trigger: ''
          }
        ],
        paymentDate: [
          {
            required: true,
            message: '请选择到账时间',
            trigger: ''
          }
        ],
        telephone: [
          {
            required: true,
            message: '请选择联系方式',
            trigger: ''
          },
          {
            message: '请输入正确的手机号',
            validator: (rule, value, callback) => {
              let reg = /^1[^1-2]\d{9}$/;
              if (reg.test(value)) {
                callback();
              } else {
                callback('请输入正确的手机号');
              }
            }
          }
        ],
        deptName: [
          {
            required: true,
            message: '请选择所属科室',
            trigger: ''
          }
        ],
        fundTypeName: [
          {
            required: true,
            message: '请选择经费类型',
            trigger: ''
          }
        ],
        filesId: [
          {
            required: true,
            message: '请上传附件',
            trigger: ''
          }
        ]
      }
    };
  },
  methods: {
    /**@desc 获取经费类型树 */
    getFundTypeTreeData() {
      this.ajax.getFundTypeTreeData().then(res => {
        if (res.success == false) {
          return;
        }
        this.fundTypeData = [res];
        this.fundTypeData.push(res.children || []);
        let index = this.formList.findIndex(
          item => item.propVal == 'fundTypeId'
        );
        this.$set(this.formList[index], 'optionList', this.fundTypeData);
      });
    },
    handleLeaderUserChange(data) {
      let person = data[0] || {},
        { empName, empCode, empDeptName, empDeptId, empPhone } = person;

      this.$set(this.editData, 'telephone', empPhone);
      this.$set(this.editData, 'leaderUserName', empName);
      this.$set(this.editData, 'leaderUser', empCode);
      this.$set(this.editData, 'deptName', empDeptName);
      this.$set(this.editData, 'deptCode', empDeptId);
    },
    /**@desc 经费类型选择 */
    handleFundTypeConfirm({ value = [] }) {
      let valueList = value.filter(item => item),
        { typeName, id } = valueList[valueList.length - 1] || {};
      this.$set(this.editData, 'fundTypeName', typeName);
      this.$set(this.editData, 'fundTypeId', id);
    }
  }
};
