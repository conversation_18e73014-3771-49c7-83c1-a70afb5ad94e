<template>
  <view class="trasen-container">
    <u-navbar :title="title" title-bold @leftClick="goBack"></u-navbar>
    <view class="trasen-content">
      <template v-if="['add', 'edit', 'enter'].includes(editType)">
        <base-form
          ref="baseForm"
          :formList="formList"
          :formData.sync="editData"
          :rules="rules"
          :showSubmitButton="false"
        >
          <template slot="itemName">
            <view class="item-name-slot-content">
              <u-input
                v-model="editData.itemName"
                placeholder="请输入项目名称"
                type="text"
                :border="$formInputBorder"
                input-align="right"
                :disabled="editType == 'enter'"
                @change="handleItemNameChange"
              ></u-input>
              <u-icon
                v-if="editType != 'enter'"
                name="search"
                size="18"
                @click="handleShowItemSelect"
              ></u-icon>
            </view>
          </template>
        </base-form>
      </template>
      <template v-else>
        <ts-subsection-control
          :list="actionList"
          mode="text"
          :current="active"
          :activeStyle="{ color: '#005bac' }"
          lineColor="#005bac"
          @change="handleSubChange"
        ></ts-subsection-control>

        <base-info-board
          v-show="active == 0"
          :data="editData"
          :dataList="formList"
          class="base-info-content"
        ></base-info-board>

        <view v-show="active == 1" class="log-content">
          <view>
            <u-steps
              :current="logsList.length - 1"
              :dot="true"
              direction="column"
            >
              <u-steps-item
                v-for="item of logsList"
                :key="item.id"
                :title="`[ ${item.operationContent} ]`"
              >
                <template slot="desc">
                  <view class="desc-content">
                    <view>
                      {{ item.operationContent }}人：
                      {{ item.operationUserName }}
                    </view>
                    <view v-if="item.approvalResult">
                      {{ item.operationContent }}意见：{{
                        item.approvalResult == 0 ? '不同意' : '同意'
                      }}
                    </view>
                    <view v-if="item.approvalRemark">
                      备注：{{ item.approvalRemark }}
                    </view>
                    <view>
                      {{ item.operationContent }}时间： {{ item.operationDate }}
                    </view>
                    <view v-if="item.autograph" style="display: flex;">
                      签字：
                      <view style="background-color: rgba(204, 204, 204, 0.2);">
                        <image
                          mode="scaleToFill"
                          :src="handleImgPath(item.autograph)"
                          style="width: 250rpx; height: 100rpx"
                        ></image>
                      </view>
                    </view>
                  </view>
                </template>
              </u-steps-item>
            </u-steps>
          </view>
        </view>
      </template>
    </view>
    <view class="action-content" v-if="status == 0 || editType == 'add'">
      <!-- 提交/编辑 -->
      <template v-if="['add', 'edit'].includes(editType)">
        <view class="action-item draft-btn" @click="handleSaveEdit(0)"
          >存草稿</view
        >
        <view class="action-item report-btn" @click="handleSaveEdit(1)">
          录入
        </view>
        <view class="action-item" @click="handleQuitEdit">取消</view>
      </template>
      <!-- 待提交 -->
      <template v-else-if="editType == 'preview' && editData.status == 0">
        <view
          class="action-item submit-btn"
          @click="handleOpenToast('提交后不可进行修改，确认提交吗？', 'report')"
        >
          提交
        </view>
        <view class="action-item report-btn" @click="handleChangeToEdit">
          编辑
        </view>
        <view
          class="action-item delete-btn"
          @click="handleOpenToast('删除后将无法恢复，是否确认删除？', 'delete')"
        >
          删除
        </view>
      </template>
      <!-- 待查阅 -->
      <template v-else-if="editType == 'preview' && editData.status == 1">
        <view class="action-item report-btn" @click="handleOpenConsult">
          查阅
        </view>
      </template>
      <!-- 待录入 -->
      <template v-else-if="editType == 'preview' && editData.status == 3">
        <view class="action-item report-btn" @click="editType = 'enter'">
          录入
        </view>
      </template>
      <!-- 录入 -->
      <template v-else-if="editType == 'enter' && editData.status == 3">
        <view class="action-item report-btn" @click="handleSaveEdit(1)">
          确定
        </view>
        <view class="action-item" @click="handleQuitEdit">取消</view>
      </template>
    </view>

    <!-- 项目名称选择 -->
    <item-picker
      :show="pickerShow"
      @close="pickerShow = false"
      @confirm="handlePickItem"
    ></item-picker>

    <ts-modal
      :show="show"
      :content="toast"
      :showCancelButton="true"
      @confirm="handleConfirm"
      @cancel="handleCancelModal"
    ></ts-modal>

    <!-- 查看 -->
    <ts-popup :show="checkModelShow" mode="bottom">
      <view class="consult-title">查阅信息</view>
      <view class="consult-content">
        <base-form
          :formList="consultFormList"
          :formData.sync="editData"
          :showSubmitButton="false"
        >
          <template slot="isRead">
            <ts-checkbox
              v-model="editData.isRead"
              :list="checkList"
            ></ts-checkbox>
          </template>
          <template slot="autograph">
            <view class="autograph-box">
              <template v-if="editData.autograph">
                <image
                  class="autograph-img"
                  mode="scaleToFill"
                  :src="handleImgPath(editData.autograph)"
                  style="width: 250rpx; height: 100rpx"
                ></image>
                <view
                  class="autograph-reset-text"
                  @click="showHandwrittenSignature = true"
                >
                  重置
                </view>
              </template>
              <view
                v-else
                class="autograph-text"
                @click="showHandwrittenSignature = true"
              >
                点击签字
              </view>
            </view>
          </template>
        </base-form>
      </view>
      <view class="consult-action">
        <view class="consult-btn report-btn" @click="handleConsult">查阅</view>
        <view class="consult-btn" @click="handleCancelConsultModal">取消</view>
      </view>
    </ts-popup>

    <!-- 手写签字 -->
    <base-signature
      v-model="showHandwrittenSignature"
      @ok="changeHandwrittenSignature"
    ></base-signature>
  </view>
</template>

<script>
import previewFundJs from './preview-fund';
import common from '@/util/common.js';

import itemPicker from './components/item-picker.vue';

export default {
  mixins: [previewFundJs],
  components: {
    itemPicker
  },
  data() {
    return {
      requestLock: false,
      baseData: {},
      editType: 'add',
      title: '查看详情',

      pickerShow: false,
      itemList: [],
      checkedItemName: '',

      show: false,
      toast: '',
      toastType: '',

      checkModelShow: false,
      checkList: [
        {
          label: '',
          name: '1'
        }
      ],

      actionList: [
        {
          name: '基本信息'
        },
        {
          name: '操作日志'
        }
      ],
      active: 0,

      consultFormList: [
        {
          title: '查阅',
          prop: 'isRead',
          rightSlot: true,
          required: true
        },
        {
          title: '备注',
          prop: 'approvalRemark',
          type: 'textarea',
          placeholder: '请输入备注'
        },
        {
          title: '签字',
          prop: 'autograph',
          rightSlot: true,
          required: true
        }
      ],
      showHandwrittenSignature: false
    };
  },
  computed: {
    status() {
      return this.editData.activeFundsType || 0;
    },
    logsList() {
      return this.editData.logsList || [];
    },
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  watch: {
    'editData.wfStepName'(val) {
      let index = this.consultFormList.findIndex(
        item => item.prop == 'messageName'
      );

      if (val == '行政办' && index == -1) {
        this.consultFormList.unshift({
          title: '消息推送院领导',
          prop: 'messageName',
          propVal: 'messageCode',
          type: 'select',
          mode: 'person',
          placeholder: '请选择消息推送院领导',
          required: true,
          chooseType: 'checkbox'
        });
      } else if (val != '行政办' && index >= 0) {
        this.consultFormList.shift();
      }
    },
    editType: {
      handler(val) {
        this.title = {
          add: '录入经费',
          edit: '编辑经费',
          preview: '查看详情'
        }[val || 'preview'];
      },
      immediate: true
    }
  },
  onLoad() {
    let editData = uni.getStorageSync('fundItem');
    if (!editData) {
      editData = {
        itemName: '',
        leaderUserName: '',
        deptName: '',
        telephone: '',
        filesId: ''
      };
    }
    this.editData = editData;
    this.baseData = common.deepClone(editData);
    this.editType = uni.getStorageSync('editType') || 'add';
    this.getFundTypeTreeData();
    this.getFundData();
  },
  methods: {
    handleImgPath(path) {
      return location.origin + path;
    },
    goBack() {
      uni.clearStorageSync('fundItem');
      uni.clearStorageSync('editType');
      uni.navigateTo({
        url: `/pages/fund-management/enter-funds/index`
      });
    },
    handleSubChange({ name, index }) {
      this.active = index;
    },
    /**@desc 获取详细信息 */
    getFundData() {
      this.ajax.handleGetFundDataDetail(this.editData.id).then(res => {
        if (!res.success) {
          return;
        }
        let editData = common.deepClone(res.object);
        editData.activeFundsType = this.editData.activeFundsType;
        this.editData = editData;
      });
    },
    /**@desc 显示项目选择弹窗 */
    handleShowItemSelect() {
      this.pickerShow = true;
    },
    handlePickItem(item) {
      let {
          leaderUser,
          leaderUserName,
          deptName,
          deptCode,
          telephone,
          fundTypeName,
          fundTypeId,
          filesId,
          itemEscalationId,
          itemName,
          id
        } = item,
        updateData = {
          leaderUser,
          leaderUserName,
          deptName,
          deptCode,
          telephone,
          fundTypeName,
          fundTypeId,
          filesId,
          itemEscalationId,
          itemName,
          id
        };
      Object.keys(updateData).map(key => {
        this.$set(this.editData, key, updateData[key]);
      });

      this.checkedItemName = itemName;
      this.pickerShow = false;
    },
    handleItemNameChange(val) {
      if (!this.checkedItemName || val == this.checkedItemName) {
        return;
      }

      let { itemName, id } = this.editData;

      Object.keys(this.editData).map(key => {
        this.$set(this.editData, key, '');
      });
      this.$set(this.editData, 'itemName', itemName);
      this.checkedItemName = '';
    },
    /**@desc 提交 / 存草稿 */
    async handleSaveEdit(status) {
      let validate = await this.$refs.baseForm.validate().catch(res => res);
      if (!validate) {
        return;
      }

      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      let data = { ...this.editData, status };
      this.ajax[
        this.editType == 'add' && !data.id
          ? 'handleEnterFundsData'
          : 'handleEditFundData'
      ](data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '录入失败',
              icon: 'none'
            });
            return;
          }
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 开始编辑 */
    handleChangeToEdit() {
      this.editType = 'edit';
    },
    /**@desc 取消编辑 */
    handleQuitEdit() {
      if (this.editType == 'add') {
        this.goBack();
        return;
      }
      this.editType = 'preview';
      this.editData = common.deepClone(this.baseData);
    },
    /**@desc 打开提示内容 */
    handleOpenToast(toast, type) {
      this.toast = toast;
      this.type = type;
      this.show = true;
    },
    /**@desc 确认操作分发 */
    handleConfirm() {
      switch (this.type) {
        case 'report':
          this.handleReport();
          break;
        case 'delete':
          this.handleDelete();
          break;
      }
      this.handleCancelModal();
    },
    /**@desc 关闭弹窗 */
    handleCancelModal() {
      this.show = false;
      this.toast = '';
      this.type = '';
    },
    /**@desc 提交 */
    handleReport() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleEditFundData({ ...this.editData, status: 1 })
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '提交失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '提交成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(res => {
          this.requestLock = false;
        });
    },
    /**@desc 删除 */
    handleDelete() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleDeleteFundTableData(this.editData.id)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '删除失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '删除成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 打开查阅弹窗 */
    handleOpenConsult() {
      this.ajax
        .handleGetFundDataDetailSpecial({
          id: this.editData.id,
          assigneeNo: this.userInfo.employeeNo
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              title: res.message || '详情获取失败',
              icon: 'none'
            });
            return;
          }
          let editData = common.deepClone(res.object);
          editData.activeFundsType = this.editData.activeFundsType;
          this.editData = editData;
          this.checkModelShow = true;
        });
    },
    /**@desc 关闭查阅弹窗*/
    handleCancelConsultModal() {
      this.checkModelShow = false;
      delete this.editData.isRead;
      delete this.editData.approvalRemark;
      delete this.editData.messageName;
      delete this.editData.messageCode;
    },
    changeHandwrittenSignature(path) {
      this.$set(this.editData, 'autograph', path);
    },
    /**@desc 查阅 */
    handleConsult() {
      if (
        this.editData.wf_step_name == '行政办' &&
        !this.editData.messageName
      ) {
        uni.showToast({
          title: '请选择消息通知领导',
          icon: 'none'
        });
        return;
      }
      let { isRead = [], autograph } = this.editData;
      if (!isRead.length) {
        uni.showToast({
          title: '必填信息，请勾选',
          icon: 'none'
        });
        return;
      }
      if (!autograph) {
        uni.showToast({
          title: '请签字',
          icon: 'none'
        });
        return;
      }
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      let data = {
        ...this.editData,
        isRead: true
      };
      this.ajax
        .hanleConsultFundData(data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
            return;
          }

          uni.showToast({
            title: '操作成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: 100vh;
  overflow: hidden;
  /deep/ .u-navbar {
    border-bottom: 1px solid #e9ecf1;
  }
}
.action-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #fff;
  box-shadow: 0 -1px 6px #ccc;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 9;
  height: 40px;
  .action-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $uni-text-color-grey;
    position: relative;
    &:not(:last-child)::after {
      content: ' ';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 15px;
      border-right: 1px solid #eee;
    }
    &.report-btn {
      color: $u-type-primary;
    }
    &.draft-btn {
      color: $ts-type-warning;
    }
    &.delete-btn {
      color: $ts-type-error;
    }
    &.submit-btn {
      color: $ts-type-warning;
    }
  }
}
.trasen-content {
  height: calc(100vh - 88px);
  margin-bottom: 44px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /deep/ .ts-subsection-control {
    flex-shrink: 0;
    background-color: #fff;
    margin-bottom: 8px;
  }
  /deep/ .base-info-board-container {
    flex: 1;
    overflow: auto;
    margin-bottom: 8px;
  }
}
.item-name-slot-content {
  display: flex;
  align-items: center;
  > .u-icon {
    margin-left: 8px;
  }
}
.consult-title {
  line-height: 40px;
  text-align: center;
  box-shadow: 0 1px 6px #ccc;
  margin-bottom: 8px;
}
.consult-content {
  padding: 16px;
  .consult-item {
    display: flex;
    margin-bottom: 8px;
    .item-title {
      width: 60px;
      position: relative;
      &.required::before {
        content: '*';
        position: absolute;
        left: -9px;
        color: red;
      }
    }
  }
  .message-item {
    align-items: center;
  }
  /deep/ .u-checkbox__icon-wrap {
    margin: 0;
  }
  .autograph-box {
    position: relative;
    background-color: rgba(204, 204, 204, 0.2);
    width: 125px;
    height: 50px;
    text-align: center;
    .autograph-text {
      line-height: 50px;
      color: #005bac;
    }
    .autograph-reset-text {
      position: absolute;
      bottom: 0;
      right: 0;
      font-size: 12px;
      border-radius: 40px 0 0 0;
      background-color: rgba(0, 91, 172, 0.2);
      width: 36px;
      height: 32px;
      line-height: 40px;
      text-align: right;
      padding-right: 4px;
      color: #005bac;
    }
  }
}
.consult-action {
  display: flex;
  border-top: 1px solid #eee;
  .consult-btn {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
    color: $uni-text-color-grey;
    position: relative;
    &:not(:last-child)::after {
      content: ' ';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 15px;
      border-right: 1px solid #eee;
    }
    &.report-btn {
      color: $u-type-primary;
    }
  }
}
.log-content {
  flex: 1;
  overflow: hidden;
  background: #fff;
  padding: 8px 10px;
  > view {
    height: 100%;
    overflow: auto;
  }

  /deep/ .u-steps-item__wrapper__dot {
    background-color: $u-type-primary !important;
  }
  /deep/ .u-text__value {
    font-weight: 600;
  }
  .u-steps-item:not(:last-child) .desc-content::after {
    content: ' ';
    border-left: 1px solid $u-type-primary;
    height: 100%;
    position: absolute;
    left: -13px;
    top: 0;
  }
  .desc-content {
    uni-view {
      color: #999;
    }
    position: relative;
  }
}
</style>
