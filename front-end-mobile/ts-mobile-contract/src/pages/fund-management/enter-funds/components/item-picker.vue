<template>
  <ts-popup :show="show" :round="10" mode="bottom">
    <view class="top-content">
      <view class="btn" @click="close">取消</view>

      <ts-search
        v-model="condition"
        :animation="true"
        @search="refresh"
        @custom="refresh"
      ></ts-search>
      <view class="btn confirm-btn" @click="confirm">确定</view>
    </view>
    <view class="picker-content">
      <view class="picker-indicator"></view>
      <view class="picker-mask"></view>
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          v-for="item of itemList"
          :key="item.id"
          class="item"
          :data-key="item.id"
          @click="handlePickItem(item, $event)"
        >
          {{ item.itemName }}
        </view>
      </mescroll>
    </view>
  </ts-popup>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  components: { mescroll },
  props: {
    show: Boolean
  },
  data() {
    return {
      condition: '',
      scrollTimer: null,
      isMoved: false,

      itemList: []
    };
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          this.$refs.mescroll?.downCallback();
          this.$nextTick(() => {
            let scrollContent = this.$refs.mescroll.$el.querySelector(
              '.uni-scroll-view .uni-scroll-view'
            );
            scrollContent.removeEventListener('scroll', this.handleScroll);
            scrollContent.removeEventListener(
              'touchend',
              this.handlePickerScroll
            );
            scrollContent.addEventListener('scroll', this.handleScroll);
            scrollContent.addEventListener('touchend', this.handlePickerScroll);
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    refresh() {
      let scrollContent = this.$refs.mescroll.$el.querySelector(
        '.uni-scroll-view .uni-scroll-view'
      );
      scrollContent.scrollTop = 0;
      this.$refs.mescroll?.downCallback();
    },
    close() {
      this.$emit('close');
    },
    confirm() {
      let scrollContent = this.$refs.mescroll.$el.querySelector(
          '.uni-scroll-view .uni-scroll-view'
        ),
        scrollTop = scrollContent.scrollTop,
        index = Math.ceil(scrollTop / 34);
      this.$emit('confirm', this.itemList[index]);
    },

    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getFundsTableDataList({
          pageNo: page.num,
          pageSize: page.size,
          condition: this.condition,
          index: 1,
          status: 3,
          sord: 'desc',
          sidx: 'a.create_date'
        })
        .then(res => {
          uni.hideLoading();
          successCallback(res.rows, res.totalCount);
        })
        .catch(err => {
          uni.hideLoading();
          errorCallback();
        });
    },
    setListData(rows) {
      this.itemList = this.itemList.concat(rows);
    },
    datasInit() {
      this.itemList = [];
    },
    handlePickItem(item, picker) {
      let { offsetTop } = picker.target,
        scrollContent = this.$refs.mescroll.$el.querySelector(
          '.uni-scroll-view .uni-scroll-view'
        ),
        contentHeight = scrollContent.offsetHeight / 2;
      scrollContent.scrollTo({
        top: offsetTop - contentHeight + 17
      });
      this.$emit('select', item);
    },
    handleScroll() {
      this.isMoved = true;
      this.scrollTimer && clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.handlePickerScrolla();
      }, 500);
    },
    handlePickerScrolla() {
      if (!this.isMoved) {
        return;
      }
      this.isMoved = false;
      let scrollContent = this.$refs.mescroll.$el.querySelector(
          '.uni-scroll-view .uni-scroll-view'
        ),
        scrollTop = scrollContent.scrollTop,
        index = Math.ceil(scrollTop / 34);
      scrollContent.scrollTo({
        top: index * 34
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.top-content {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  .ts-search {
    flex: 1;
  }
  .btn {
    margin: 0 10px;
  }
  .confirm-btn {
    color: $u-type-primary;
  }
  /deep/ .u-search__content__input {
    height: 28px !important;
  }
}
.picker-content {
  height: 266px;
  position: relative;
}
.picker-indicator {
  height: 34px;
  position: absolute;
  top: 50%;
  width: 100%;

  transform: translate(0, -50%);
  border-top: 0.5px solid #e5e5e5;
  border-bottom: 0.5px solid #e5e5e5;
}
.picker-mask {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 3;
  pointer-events: none;
  top: 0;
  height: 100%;
  margin: 0 auto;
  background: linear-gradient(
      180deg,
      hsla(0, 0%, 100%, 0.95),
      hsla(0, 0%, 100%, 0.6)
    ),
    linear-gradient(0deg, hsla(0, 0%, 100%, 0.95), hsla(0, 0%, 100%, 0.6));
  background-position: top, bottom;
  background-size: 100% 117px;
  background-repeat: no-repeat;
}
.item {
  line-height: 34px;
  text-align: center;
  font-size: 14px;
  &:nth-child(2) {
    padding-top: calc(133px - 17px);
  }
  &:last-child {
    padding-bottom: calc(133px - 17px);
  }
}
</style>
