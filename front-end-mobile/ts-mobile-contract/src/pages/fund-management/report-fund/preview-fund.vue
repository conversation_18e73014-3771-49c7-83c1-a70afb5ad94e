<template>
  <view class="trasen-container">
    <u-navbar :title="title" title-bold @leftClick="goBack"></u-navbar>
    <view class="trasen-content">
      <template v-if="title != '查看详情'">
        <base-form
          ref="baseForm"
          :formList="formList"
          :formData.sync="editData"
          :rules="rules"
          :showSubmitButton="false"
        ></base-form>
      </template>
      <template v-else>
        <base-info-board
          :data="editData"
          :dataList="formList"
          class="base-info-content"
        ></base-info-board>
      </template>

      <!-- 关闭提醒 -->
      <ts-modal
        :show="show"
        :showCancelButton="true"
        content="是否确认删除？"
        @confirm="handleDelete"
        @cancel="show = false"
      >
      </ts-modal>
      <!-- 提交提醒 -->
      <ts-modal
        :show="submitToast"
        :showCancelButton="true"
        content="提交后不可进行修改，确认提交吗？"
        @confirm="handleSubmit"
        @cancel="submitToast = false"
      >
      </ts-modal>
    </view>

    <view class="action-content" v-if="!editData.status">
      <template v-if="title == '项目上报'">
        <view class="action-item draft-btn" @click="handleReport(0)">
          存草稿
        </view>
        <view class="action-item report-btn" @click="handleReport(1)">
          上报
        </view>
        <view class="action-item" @click="goBack">取消</view>
      </template>
      <template v-if="editData.status == 0 && title == '查看详情'">
        <view class="action-item submit-btn" @click="submitToast = true">
          提交
        </view>
        <view class="action-item report-btn" @click="handleChangeToEdit">
          编辑
        </view>
        <view class="action-item delete-btn" @click="show = true">
          删除
        </view>
      </template>
      <template v-if="title == '编辑'">
        <view class="action-item report-btn" @click="handleUpdate(1)">
          确定
        </view>
        <view class="action-item" @click="handleCancelEdit">取消</view>
      </template>
    </view>
  </view>
</template>

<script>
import previewFundJs from './preview-fund';
export default {
  mixins: [previewFundJs],
  data() {
    return {
      title: '查看详情',
      show: false,
      submitToast: false,

      requestLock: false
    };
  },
  onLoad() {
    let editData = uni.getStorageSync('fundItem'),
      title = '查看详情';
    if (!editData) {
      editData = {
        itemName: '',
        leaderUserName: '',
        deptName: '',
        telephone: '',
        filesId: ''
      };
      title = '项目上报';
    }
    this.title = title;
    this.editData = editData;
    this.basicData = JSON.parse(JSON.stringify(editData));
    this.getFundTypeTreeData();
  },
  methods: {
    goBack() {
      uni.clearStorageSync('fundItem');
      uni.navigateTo({
        url: `/pages/fund-management/report-fund/report-fund`
      });
    },
    handleChangeToEdit() {
      this.title = '编辑';
    },
    /**@desc 存草稿/提交 */
    async handleReport(status) {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      let validate = await this.$refs.baseForm.validate().catch(res => res);
      this.requestLock = false;
      if (!validate) {
        return;
      }
      let data = { ...this.editData, status };
      this.ajax.handleAddReportFundsData(data).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '上报失败',
            icon: 'none'
          });
          return;
        }
        uni.showToast({
          title: '上报成功',
          icon: 'none'
        });
        this.goBack();
      });
    },
    /**@desc 编辑 */
    async handleUpdate() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      let validate = await this.$refs.baseForm.validate().catch(res => res);
      this.requestLock = false;
      if (!validate) {
        return;
      }
      this.ajax.handleEditReportFundsData(this.editData).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '编辑失败',
            icon: 'none'
          });
          return;
        }
        uni.showToast({
          title: '编辑成功',
          icon: 'none'
        });
        this.title = '查看详情';
      });
    },
    /**@desc 提交 */
    handleSubmit() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleEditReportFundsData({
          ...this.editData,
          status: 1
        })
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '提交失败',
              icon: 'none'
            });
            return;
          }
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 删除 */
    handleDelete() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleDeleteReportFundData(this.editData.id)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '删除失败',
              icon: 'none'
            });
            return;
          }
          this.goBack();
        })
        .catch(() => {
          this.requestLock = false;
        });
    },
    /**@desc 取消编辑 */
    handleCancelEdit() {
      this.title = '查看详情';
      this.editData = JSON.parse(JSON.stringify(this.basicData));
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.action-content {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #fff;
  box-shadow: 0 -1px 6px #ccc;
  width: 100%;
  height: 40px;
}
.trasen-content {
  flex: 1;
  overflow: auto;
}
.action-item {
  flex: 1;
  text-align: center;
  color: $uni-text-color-grey;
  position: relative;
  &:not(:last-child)::after {
    content: ' ';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 15px;
    border-right: 1px solid #eee;
  }
  &.report-btn {
    color: $u-type-primary;
  }
  &.draft-btn {
    color: $ts-type-warning;
  }
  &.delete-btn {
    color: $ts-type-error;
  }
  &.submit-btn {
    color: $ts-type-warning;
  }
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.base-info-content {
  padding-top: 10px;
}
</style>
