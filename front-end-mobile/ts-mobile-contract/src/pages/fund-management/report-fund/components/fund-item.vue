<template>
  <view class="fund-item-content">
    <view class="title-content">
      <view class="fund-type">{{ data.fundTypeName }}</view>
      <view class="flex-grow fund-name">{{ data.itemName }}</view>
      <view
        class="fund-status"
        :class="['draft-status', 'submit-status', ''][data.status]"
      >
        {{ status }}
      </view>
    </view>
    <view class="item-info">
      {{ data.leaderUserName }}-{{ data.deptName }}
    </view>
  </view>
</template>

<script>
export default {
  props: {
    data: Object
  },
  computed: {
    status() {
      return ['待提交', '已提交', '已录入'][this.data.status] || '';
    }
  }
};
</script>

<style lang="scss" scoped>
.fund-item-content {
  background: #fff;
  padding: 8px 15px;
}
.title-content {
  display: flex;
  overflow: hidden;
  margin-bottom: 6px;

  .fund-type {
    font-size: 10px;
    padding: 2px 3px;
    color: #fff;
    background: #5aaaea;
    border-radius: 0 4px 0 4px;
  }
  .fund-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 600;
    margin: 0 4px;
  }
  .fund-status {
    font-weight: 600;
    color: $uni-text-color-grey;
  }
  .draft-status {
    color: $ts-type-warning;
  }
  .submit-status {
    color: $u-type-primary;
  }
}
.item-info {
  color: #666;
  font-size: 12px;
  line-height: 18px;
}
</style>
