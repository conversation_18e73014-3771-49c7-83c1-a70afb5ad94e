<template>
  <view class="trasen-container">
    <u-navbar title="项目上报" title-bold @leftClick="goBack"></u-navbar>
    <view class="trasen-content">
      <ts-search
        v-model="condition"
        :animation="true"
        placeholder="项目名称/项目负责人"
        @search="refresh"
        @custom="refresh"
        @clear="refresh"
      >
        <u-icon
          class="add-btn"
          name="plus"
          color="#005bac"
          size="18"
          @click="handleShowDetail"
        ></u-icon>
      </ts-search>

      <view style="flex: 1; position: relative;">
        <mescroll
          ref="mescroll"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <fund-item
            v-for="item of list"
            :key="item.id"
            :data="item"
            class="fund-item"
            @click.native="handleShowDetail(item)"
          ></fund-item>
        </mescroll>
      </view>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import fundItem from './components/fund-item.vue';

export default {
  components: { mescroll, fundItem },
  data() {
    return {
      condition: '',

      list: []
    };
  },
  methods: {
    refresh() {
      this.$refs.mescroll?.downCallback();
    },
    goBack() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getReportFundsTableDatas({
          pageNo: page.num,
          pageSize: page.size,
          condition: this.condition,
          sord: 'desc',
          sidx: 'a.create_date'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    handleShowDetail(item) {
      uni.setStorageSync('fundItem', item);
      uni.navigateTo({
        url: `/pages/fund-management/report-fund/preview-fund`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: 100vh;
  overflow: hidden;
}
.trasen-content {
  height: calc(100vh - 44px);
  display: flex;
  flex-direction: column;
}
.ts-search {
  padding: 6px 15px;
  margin-bottom: 8px;
  background-color: #fff;
}
.add-btn {
  margin-left: 16px;
}

.fund-item {
  margin-bottom: 8px;
  &:last-child {
    margin: 0;
  }
}
</style>
