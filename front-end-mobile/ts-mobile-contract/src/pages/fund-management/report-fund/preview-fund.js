export default {
  data() {
    return {
      editData: {
        itemName: '',
        leaderUserName: '',
        deptName: '',
        telephone: '',
        filesId: ''
      },
      basicData: {},

      formList: [
        {
          title: '项目名称',
          prop: 'itemName',
          propVal: 'itemName',
          type: 'text',
          mode: 'input',
          placeholder: '请填写项目名称',
          required: true
        },
        {
          title: '项目负责人',
          prop: 'leaderUserName',
          propVal: 'leaderUser',
          type: 'select',
          mode: 'person',
          placeholder: '请选择项目负责人',
          required: true,
          chooseType: 'radio',
          changeCallback: this.handleLeaderUserChange
        },
        {
          title: '所属科室',
          prop: 'deptName',
          propVal: 'deptCode',
          type: 'text',
          mode: 'input',
          placeholder: '请选择所属科室',
          required: true,
          disabled: true
        },
        {
          title: '联系电话',
          prop: 'telephone',
          propVal: 'telephone',
          type: 'number',
          mode: 'input',
          placeholder: '请输入联系电话',
          maxlength: 11,
          required: true
        },
        {
          title: '经费类型',
          prop: 'fundTypeName',
          propVal: 'fundTypeId',
          type: 'select',
          mode: 'select',
          optionList: [],
          elementProps: {
            keyName: 'typeName'
          },
          placeholder: '请选择经费类型',
          required: true,
          handleConfirm: this.handleFundTypeConfirm
        },
        {
          title: '附件',
          prop: 'filesId',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          required: true,
          deletable: true
        }
      ],
      rules: {
        itemName: [
          {
            required: true,
            message: '请填写项目名称',
            trigger: ''
          }
        ],
        leaderUserName: [
          {
            required: true,
            message: '请选择项目负责人',
            trigger: ''
          }
        ],
        telephone: [
          {
            required: true,
            message: '请填写联系电话',
            trigger: ''
          },
          {
            message: '请输入正确的手机号',
            validator: (rule, value, callback) => {
              let reg = /^1[^1-2]\d{9}$/;
              if (reg.test(value)) {
                callback();
              } else {
                callback('请输入正确的手机号');
              }
            }
          }
        ],
        deptName: [
          {
            required: true,
            message: '请选择所属科室',
            trigger: ''
          }
        ],
        fundTypeName: [
          {
            required: true,
            message: '请选择经费类型',
            trigger: ''
          }
        ],
        filesId: [
          {
            required: true,
            message: '请上传附件',
            trigger: ''
          }
        ]
      }
    };
  },
  methods: {
    /**@desc 获取经费类型树 */
    getFundTypeTreeData() {
      this.ajax.getFundTypeTreeData().then(res => {
        if (res.success == false) {
          return;
        }
        this.fundTypeData = [res];
        this.fundTypeData.push(res.children || []);

        this.$set(this.formList[4], 'optionList', this.fundTypeData);
      });
    },
    handleLeaderUserChange(data) {
      let person = data[0] || {},
        { empName, empCode, empDeptName, empDeptId, empPhone } = person;

      this.$set(this.editData, 'telephone', empPhone);
      this.$set(this.editData, 'leaderUserName', empName);
      this.$set(this.editData, 'leaderUser', empCode);
      this.$set(this.editData, 'deptName', empDeptName);
      this.$set(this.editData, 'deptCode', empDeptId);
    },
    /**@desc 经费类型选择 */
    handleFundTypeConfirm({ value = [] }) {
      let valueList = value.filter(item => item),
        { typeName, id } = valueList[valueList.length - 1] || {};
      this.$set(this.editData, 'fundTypeName', typeName);
      this.$set(this.editData, 'fundTypeId', id);
    }
  }
};
