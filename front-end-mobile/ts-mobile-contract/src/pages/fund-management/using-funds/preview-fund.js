export default {
  data() {
    return {
      basicData: {
        itemName: ''
      },

      formList: [
        {
          title: '项目名称',
          prop: 'itemName',
          propVal: 'entryItemId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择申请项目',
          optionList: [],
          elementProps: {
            keyName: 'itemName'
          },
          handleConfirm: this.handlePickeItem,
          required: true
        },
        {
          title: '项目负责人',
          prop: 'leaderUserName',
          propVal: 'leaderUserName',
          type: 'text',
          mode: 'input',
          required: true,
          disabled: true
        },
        {
          title: '核定项目经费',
          prop: 'approvedItemFund',
          itemClass: 'approval-fund-content',
          rightSlot: true,
          required: true
        },
        {
          title: '相关职能科室审核人',
          prop: 'typeApproverName',
          propVal: 'typeApproverCode',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          placeholder: '请选择审核人',
          required: true
        }
      ],
      rules: {
        itemName: [
          {
            required: true,
            message: '请选择申请项目',
            trigger: ''
          }
        ],
        typeApproverName: [
          {
            required: true,
            message: '请选择相关职能科室审核人'
          }
        ]
      },

      balanceData: {}
    };
  },
  computed: {
    actionFormList() {
      let list = [...this.formList];
      if (this.detailData.supportingFund) {
        list.splice(3, 0, {
          title: '医院配套经费',
          prop: 'supportingFund',
          itemClass: 'approval-fund-content',
          rightSlot: true,
          required: true
        });
      }
      return list;
    },
    balanceList() {
      let {
          approvedItemFund = 0,
          supportingFund = 0,
          actualAmount = 0
        } = this.detailData,
        approvalBalance = approvedItemFund - actualAmount,
        supportBalance = 0;

      if (approvalBalance < 0) {
        supportBalance = supportingFund + approvalBalance;
        approvalBalance = 0;
      } else {
        supportBalance = supportingFund;
      }

      approvalBalance && (approvalBalance = approvalBalance.toFixed(2));
      supportBalance && (supportBalance = supportBalance.toFixed(2));

      return [approvalBalance, supportBalance];
    }
  },
  methods: {
    /**@desc 获取负责项目 */
    getItemData() {
      this.ajax
        .getLeaderUserProjectDataList({
          pageNo: 1,
          pageSize: 9999,
          leaderUser: this.userInfo.employeeId
        })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.$set(this.formList[0], 'optionList', [res.rows]);
        });
    },
    /**@desc 选择项目数据 */
    handlePickeItem({ value = [] }) {
      let {
        approvedItemFund = null,
        supportingFund = null,
        actualAmount = null,
        itemName,
        leaderUserName,
        id
      } = value[0] || {};

      this.$set(this.basicData, 'entryItemId', id);
      this.$set(this.basicData, 'itemName', itemName);
      this.$set(this.basicData, 'leaderUserName', leaderUserName);
      this.$set(this.basicData, 'approvedItemFund', approvedItemFund);
      this.$set(this.basicData, 'supportingFund', supportingFund);

      let supportIndex = this.formList.findIndex(
        item => item.prop == 'supportingFund'
      );
      if (supportIndex == -1 && supportingFund) {
        let approvalIndex = this.formList.findIndex(
          item => item.prop == 'approvedItemFund'
        );
        this.formList.splice(approvalIndex + 1, 0, {
          title: '医院配套经费',
          prop: 'supportingFund',
          itemClass: 'approval-fund-content',
          rightSlot: true,
          required: true
        });
      } else if (supportIndex >= 0 && !supportingFund) {
        this.formList.splice(supportIndex, 1);
      }

      this.computeBalanceData(value[0]);
    },
    computeBalanceData(data = {}) {
      let {
        approvedItemFund = null,
        supportingFund = null,
        actualAmount = null
      } = data;

      let approvalBalance = approvedItemFund - actualAmount,
        supportBalance = 0;
      if (approvalBalance < 0) {
        supportBalance = supportingFund + approvalBalance;
        approvalBalance = 0;
      } else {
        supportBalance = supportingFund;
      }

      this.balanceData = {
        approvalBalance,
        supportBalance
      };
    }
  }
};
