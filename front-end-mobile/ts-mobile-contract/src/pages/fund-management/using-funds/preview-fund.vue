<template>
  <view class="trasen-container">
    <u-navbar :title="title" title-bold @leftClick="goBack"></u-navbar>
    <view
      class="trasen-content"
      :class="{
        preview:
          status == 0 ||
          editType != 'preview' ||
          (status == 2 && basicData.status == 3)
      }"
    >
      <template v-if="['add', 'edit'].includes(editType)">
        <base-form
          ref="baseForm"
          :formList="formList"
          :formData.sync="basicData"
          :rules="rules"
          :showSubmitButton="false"
        >
          <template slot="approvedItemFund">
            <view style="width: 100%;">
              <ts-input
                v-model="basicData.approvedItemFund"
                disabled
                :border="$formInputBorder"
                placeholder="请输入核定项目经费"
                input-align="right"
              ></ts-input>
              <view class="balance-content">
                余额：{{ (balanceData.approvalBalance || 0).toFixed(2) }}元
              </view>
            </view>
          </template>

          <template slot="supportingFund">
            <view style="width: 100%;">
              <ts-input
                v-model="basicData.supportingFund"
                disabled
                :border="$formInputBorder"
                placeholder="请输入医院配套经费"
                input-align="right"
              ></ts-input>
              <view class="balance-content">
                余额：{{ (balanceData.supportBalance || 0).toFixed(2) }}元
              </view>
            </view>
          </template>
        </base-form>

        <view class="section-title">预算金额</view>

        <using-fund-board
          ref="usingBoard"
          v-model="basicData.fundBudgetList"
          :editType="editType"
          :status="basicData.status"
          :balanceData="balanceData"
        ></using-fund-board>
      </template>
      <template v-else>
        <ts-subsection-control
          v-if="editType != 'change'"
          :list="actionList"
          :current="active"
          :activeStyle="{ color: '#005bac' }"
          lineColor="#005bac"
          @change="handleSubChange"
          mode="text"
        ></ts-subsection-control>

        <!-- 基本信息 -->
        <view v-show="active == 0" class="base-info-content">
          <preview-form
            v-if="!shorActionList"
            :basicData="basicData"
            :detailData="allDetailData"
          >
          </preview-form>

          <template v-else>
            <base-info-board :data="detailData" :dataList="actionFormList">
              <view slot="approvedItemFund" class="cost-detail">
                <view>
                  {{ detailData.approvedItemFund }}
                </view>
                <view class="balance">余额：{{ balanceList[0] }}</view>
              </view>
              <view slot="supportingFund" class="cost-detail">
                <view>
                  {{ detailData.supportingFund }}
                </view>
                <view class="balance">余额：{{ balanceList[1] }}</view>
              </view>
            </base-info-board>

            <view class="section-title">
              预算信息{{ editType == 'change' ? '(变更前)' : '' }}
            </view>

            <using-fund-board
              v-if="editType != 'reSubmit'"
              :value="staticFundBudgetList"
              editType="preview"
            ></using-fund-board>

            <template v-if="editType == 'reSubmit'">
              <using-fund-board
                ref="reSubmitBoard"
                v-model="detailData.fundBudgetList"
                editType="reSubmit"
                :status="detailData.status"
              ></using-fund-board>

              <base-form
                :formList="popupFormList"
                :formData.sync="detailData"
                :showSubmitButton="false"
              ></base-form>
            </template>

            <template v-if="editType == 'change'">
              <view class="section-title">
                预算信息(变更后)
              </view>
              <using-fund-board
                ref="changeBoard"
                v-model="detailData.fundBudgetList"
                :editType="editType"
                :status="detailData.status"
                :balanceData="balanceData"
              ></using-fund-board>
              <base-form
                :formList="popupFormList"
                :formData.sync="detailData"
                :showSubmitButton="false"
              ></base-form>
            </template>
          </template>
        </view>

        <!-- 经费使用详情 -->
        <using-detail
          v-show="active == 1 && !shorActionList"
          :detailData="allDetailData"
        ></using-detail>

        <achievement-log
          v-show="active == 2"
          :data="achiveFundDataList"
        ></achievement-log>

        <!-- 操作日志 -->
        <view
          v-show="active == 3 || (active == 1 && shorActionList)"
          class="log-content"
        >
          <view>
            <u-steps
              :current="logsList.length - 1"
              :dot="true"
              direction="column"
            >
              <u-steps-item
                v-for="item of logsList"
                :key="item.id"
                :title="`[ ${item.operationContent} ]`"
              >
                <template slot="desc">
                  <view class="desc-content">
                    <view>
                      {{ item.operationContent }}人：
                      {{ item.operationUserName }}
                    </view>
                    <view v-if="item.approvalResult">
                      {{ item.operationContent }}意见：{{
                        item.approvalResult == 0 ? '不同意' : '同意'
                      }}
                    </view>
                    <view v-if="item.approvalRemark">
                      备注：{{ item.approvalRemark }}
                    </view>
                    <view>
                      {{ item.operationContent }}时间： {{ item.operationDate }}
                    </view>
                    <view v-if="item.autograph" style="display: flex;">
                      签字：
                      <view style="background-color: rgba(204, 204, 204, 0.2);">
                        <image
                          mode="scaleToFill"
                          :src="handleImgPath(item.autograph)"
                          style="width: 250rpx; height: 100rpx"
                        ></image>
                      </view>
                    </view>
                  </view>
                </template>
              </u-steps-item>
            </u-steps>
          </view>
        </view>
      </template>
    </view>

    <view class="action-content" v-if="status == 0 || editType == 'add'">
      <!-- 提交/编辑 -->
      <template v-if="['add', 'edit'].includes(editType)">
        <view
          v-if="editType == 'add'"
          class="action-item draft-btn"
          @click="handleSaveEdit(0)"
        >
          存草稿
        </view>
        <view class="action-item report-btn" @click="handleSaveEdit(1)"
          >确定</view
        >
        <view class="action-item" @click="handleQuitEdit">取消</view>
      </template>
      <!-- 待提交 -->
      <template v-else-if="editType == 'preview' && basicData.status == 0">
        <view
          class="action-item submit-btn"
          @click="handleOpenToast('提交后不可进行修改，确认提交吗？', 'report')"
        >
          提交
        </view>
        <view class="action-item report-btn" @click="handleChangeToEdit"
          >编辑</view
        >
        <view
          class="action-item delete-btn"
          @click="handleOpenToast('删除后将无法恢复，是否确认删除？', 'delete')"
        >
          删除
        </view>
      </template>
      <!-- 待审批 -->
      <template
        v-else-if="
          editType == 'preview' && [1, 3, 5].includes(basicData.status)
        "
      >
        <view
          class="action-item report-btn"
          @click="handleChangeEditType('approval')"
        >
          审批
        </view>
      </template>

      <!-- 审批完成待报销 -->
      <template v-else-if="editType == 'preview' && basicData.status == 2">
        <view
          class="action-item draft-btn"
          @click="handleChangeEditType('remibur')"
        >
          报销经费
        </view>
        <view
          class="action-item report-btn"
          @click="handleChangeEditType('change')"
        >
          经费变更
        </view>
      </template>

      <!-- 报销经费 -->
      <template v-else-if="editType == 'remibur'">
        <view class="action-item report-btn" @click="showPopup = true">
          报销信息
        </view>
      </template>

      <template v-else-if="editType == 'approval'">
        <view class="action-item report-btn" @click="showPopup = true">
          审批信息
        </view>
      </template>

      <template v-else-if="editType == 'change'">
        <view class="action-item report-btn" @click="handleChangeUsingFund">
          确定
        </view>
        <view class="action-item" @click="editType = 'preview'">
          取消
        </view>
      </template>
    </view>

    <!-- 已驳回 -->
    <view class="action-content" v-if="status == 2 && basicData.status == 9">
      <template v-if="editType == 'preview'">
        <view
          class="action-item report-btn"
          @click="handleChangeEditType('reSubmit')"
        >
          重新提交
        </view>
        <view
          class="action-item delete-btn"
          @click="handleOpenToast('删除后将无法恢复，是否确认删除？', 'delete')"
        >
          删除
        </view>
      </template>
      <template v-if="editType == 'reSubmit'">
        <view class="action-item report-btn" @click="handleConfirmPopup"
          >确定</view
        >
        <view class="action-item" @click="handleQuitEdit">取消</view>
      </template>
    </view>

    <!-- 确认提示框 -->
    <ts-modal
      :show="show"
      :content="toast"
      :showCancelButton="true"
      @confirm="handleConfirm"
      @cancel="handleCancelModal"
    ></ts-modal>

    <!-- 上拉操作弹窗 -->
    <ts-popup :show="showPopup" mode="bottom">
      <view class="consult-title">{{ popupTitle }}</view>
      <view class="popup-content">
        <template v-if="editType == 'remibur'">
          <using-fund-board
            ref="remiburBoard"
            :value="detailData.fundBudgetList"
            :editType="editType"
            :status="detailData.status"
            :balanceData="balanceData"
          ></using-fund-board>
        </template>
        <template v-if="editType == 'approval' && detailData.status == 5">
          <base-info-board
            :data="detailData"
            :dataList="[
              { title: '报销附件', prop: 'reimbursementFileids', type: 'file' }
            ]"
          ></base-info-board>
        </template>
        <view class="consult-content">
          <base-form
            ref="changeForm"
            :formList="popupFormList"
            :formData.sync="detailData"
            :showSubmitButton="false"
          >
            <template slot="type">
              <ts-radio v-model="detailData.type" :list="checkList"></ts-radio>
            </template>

            <template slot="autograph">
              <view class="autograph-box">
                <template v-if="detailData.autograph">
                  <image
                    class="autograph-img"
                    mode="scaleToFill"
                    :src="handleImgPath(detailData.autograph)"
                    style="width: 250rpx; height: 100rpx"
                  ></image>
                  <view
                    class="autograph-reset-text"
                    @click="showHandwrittenSignature = true"
                  >
                    重置
                  </view>
                </template>
                <view
                  v-else
                  class="autograph-text"
                  @click="showHandwrittenSignature = true"
                >
                  点击签字
                </view>
              </view>
            </template>
          </base-form>
        </view>
      </view>
      <view class="consult-action">
        <view class="consult-btn report-btn" @click="handleConfirmPopup">
          确定
        </view>
        <view class="consult-btn" @click="handleCancelConsultModal">取消</view>
      </view>
    </ts-popup>

    <!-- 手写签字 -->
    <base-signature
      v-model="showHandwrittenSignature"
      @ok="changeHandwrittenSignature"
    ></base-signature>
  </view>
</template>

<script>
import previewFundJs from './preview-fund.js';
import common from '@/util/common.js';

import usingFundBoard from './components/using-fund-board.vue';
import previewForm from './components/preview-form.vue';
import usingDetail from './components/using-detail.vue';
import achievementLog from './components/achievement-log.vue';

export default {
  mixins: [previewFundJs],
  components: {
    usingFundBoard,
    previewForm,
    usingDetail,
    achievementLog
  },
  data() {
    return {
      requestLock: false,
      basicData: {}, // 列表基础数据
      allDetailData: {}, // 全部的详情数据
      detailData: {}, // 当前详情数据
      achiveFundDataList: [], // 绩效考核详情

      editType: 'add',
      title: '查看详情',

      pickerShow: false,
      itemList: [],
      checkedItemName: '',

      show: false,
      toast: '',
      toastType: '',

      checkList: [
        {
          name: '1',
          label: '同意'
        },
        {
          name: '0',
          label: '不同意'
        }
      ],

      active: 0,
      showPopup: false,
      staticFundBudgetList: [],
      showHandwrittenSignature: false
    };
  },
  computed: {
    status() {
      return this.basicData.activeFundsType || 0;
    },
    logsList() {
      return this.allDetailData.logsList || [];
    },
    userInfo() {
      return this.$store.state.common.userInfo;
    },
    shorActionList() {
      return ['remibur', 'reSubmit', 'approval', 'change'].includes(
        this.editType
      );
    },
    actionList() {
      let list = [
        {
          name: '基本信息'
        },
        {
          name: '查看明细'
        },
        {
          name: '绩效考核'
        },
        {
          name: '操作日志'
        }
      ];
      if (this.shorActionList) {
        list = [
          {
            name: '基本信息'
          },
          {
            name: '操作日志'
          }
        ];
      }
      return list;
    },
    popupTitle() {
      return {
        remibur: '报销信息',
        approval: '审批信息'
      }[this.editType];
    },
    popupFormList() {
      let approval = [
        {
          title: '审批意见',
          prop: 'type',
          labelWidth: '100',
          rightSlot: true,
          required: true
        },
        {
          title: '备注',
          prop: 'approvalRemark',
          propVal: 'approvalRemark',
          type: 'textarea',
          mode: 'input',
          elementProps: {
            placeholder: '请输入备注',
            maxlength: 200,
            count: true
          }
        },
        {
          title: '签字',
          prop: 'autograph',
          propVal: 'autograph',
          rightSlot: true,
          required: true
        }
      ];
      this.detailData.wfStepName == '财务科主任' &&
        approval.unshift({
          title: '分管领导审批人',
          prop: 'leaderApproverName',
          propVal: 'leaderApproverCode',
          type: 'select',
          mode: 'person',
          placeholder: '请选择分管领导审批人',
          required: true,
          chooseType: 'checkbox'
        });
      return {
        remibur: [
          {
            title: '附件（发票）',
            prop: 'reimbursementFileids',
            type: 'file',
            mode: 'file',
            placeholder: '上传附件',
            required: true,
            deletable: true
          },
          {
            title: '备注',
            prop: 'reimbursementRemark',
            type: 'textarea',
            elementProps: {
              placeholder: '请输入备注'
            }
          }
        ],
        change: [
          {
            title: '变更事由',
            prop: 'changesReason',
            propVal: 'changesReason',
            type: 'textarea',
            mode: 'input',
            elementProps: {
              placeholder: '请输入变更事由',
              maxlength: 300,
              count: true
            },
            required: true
          },
          {
            title: '备注',
            prop: 'changesRemark',
            propVal: 'changesRemark',
            type: 'textarea',
            mode: 'input',
            elementProps: {
              placeholder: '请输入备注',
              maxlength: 200,
              count: true
            }
          }
        ],
        approval: approval,
        reSubmit: [
          {
            title: '附件（发票）',
            prop: 'reimbursementFileids',
            type: 'file',
            mode: 'file',
            placeholder: '上传附件',
            required: true,
            deletable: true
          },
          {
            title: '备注',
            prop: 'reimbursementRemark',
            type: 'textarea',
            elementProps: {
              placeholder: '请输入备注'
            }
          }
        ]
      }[this.editType];
    }
  },
  watch: {
    editType: {
      handler(val) {
        this.title = {
          add: '使用经费',
          edit: '编辑',
          approval: '审批',
          remibur: '经费报销',
          reSubmit: '重新提交',
          change: '预算变更',
          preview: '查看详情'
        }[val || 'preview'];
      },
      immediate: true
    }
  },
  onLoad() {
    let basicData = uni.getStorageSync('fundItem');
    if (!basicData) {
      basicData = {
        itemName: '',
        leaderUserName: '',
        deptName: '',
        telephone: '',
        filesId: '',
        supportingFund: ''
      };
    }
    this.basicData = basicData;
    this.editType = uni.getStorageSync('editType') || 'add';
    this.getItemData(); // 获取当前负责人 负责项目
    this.basicData.id && this.getFundDetail(); // 获取详细信息
  },
  methods: {
    handleImgPath(path) {
      return location.origin + path;
    },
    goBack() {
      uni.clearStorageSync('fundItem');
      uni.clearStorageSync('editType');
      uni.redirectTo({
        url: `/pages/fund-management/using-funds/using-funds`
      });
    },
    handleSubChange({ name, index }) {
      this.active = index;
    },
    /**@desc 处理操作跳转 */
    async handleChangeEditType(type) {
      if (this.title == '使用经费') {
        this.editData = {
          userName: this.userInfo.employeeName
        };
      } else {
        let res = {};
        if (type == 'approval') {
          res = await this.ajax.getUsingFundsDataDetailSpecial({
            id: this.basicData.id,
            assigneeNo: this.userInfo.employeeNo
          });
        } else {
          res = await this.ajax.getUsingFundsDataDetail(this.basicData.id);
        }

        if (!res.success) {
          uni.showToast({
            title: res.message || '经费使用详情获取失败',
            icon: 'none'
          });
          return;
        }

        this.detailData = res.object;
        this.detailData.fundBudgetList.forEach((item, index) => {
          item.rowIndex = index;
        });
        this.staticFundBudgetList = this.detailData.fundBudgetList.map(
          item => ({ ...item })
        );

        if (type == 'reSubmit') {
          this.detailData.isResubmit = 1;
        }

        if (['change'.includes(type)]) {
          // 重新计算余额
          let optionList = this.formList[0].optionList[0] || [],
            pickerItem = optionList.find(
              option => option.id == this.detailData.entryItemId
            );
          if (pickerItem) {
            this.computeBalanceData(pickerItem);
          }
        }

        this.editType = type;
        this.active = 0;
      }
    },
    /**@desc 获取 全部 详细信息 */
    getFundDetail() {
      //获取详情
      this.ajax
        .handleGetAllUsingFundData({
          id: this.basicData.id,
          entryItemId: this.basicData.entryItemId
        })
        .then(res => {
          if (!res.success) {
            return;
          }
          let allDetailData = common.deepClone(res.object);
          allDetailData.fundBudgetList.forEach((item, index) => {
            item.rowIndex = index;
          });
          this.allDetailData = allDetailData;
        });
      //获取绩效考核信息
      this.ajax
        .getAchiveFundDataDetail(this.basicData.entryItemId)
        .then(res => {
          if (!res.success) {
            return;
          }
          this.achiveFundDataList = (res.object || {}).fundChecklist || [];
        });
    },
    /**@desc 显示项目选择弹窗 */
    handleShowItemSelect() {
      this.pickerShow = true;
    },
    handlePickItem(item) {
      let {
          leaderUser,
          leaderUserName,
          deptName,
          deptCode,
          telephone,
          fundTypeName,
          fundTypeId,
          filesId,
          itemEscalationId,
          itemName,
          id
        } = item,
        updateData = {
          leaderUser,
          leaderUserName,
          deptName,
          deptCode,
          telephone,
          fundTypeName,
          fundTypeId,
          filesId,
          itemEscalationId,
          itemName,
          id
        };
      Object.keys(updateData).map(key => {
        this.$set(this.basicData, key, updateData[key]);
      });

      this.checkedItemName = itemName;
      this.pickerShow = false;
    },
    handleItemNameChange(val) {
      if (!this.checkedItemName || val == this.checkedItemName) {
        return;
      }

      let { itemName, id } = this.basicData;

      Object.keys(this.basicData).map(key => {
        this.$set(this.basicData, key, '');
      });
      this.$set(this.basicData, 'itemName', itemName);
      this.checkedItemName = '';
    },
    /**@desc 提交 / 存草稿 */
    async handleSaveEdit(status) {
      let validate = await this.$refs.baseForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let usingVlidate = this.$refs.usingBoard.validate();
      if (!usingVlidate) {
        return;
      }
      let data = { ...this.basicData };

      this.editType == 'add' && (data.status = status);
      this.ajax[
        this.editType == 'add'
          ? 'handleAddFundUsingData'
          : 'handleUpdateUsingFundData'
      ](data).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
          return;
        }
        this.goBack();
      });
    },
    /**@desc 开始编辑 */
    async handleChangeToEdit() {
      // 重新计算余额
      let optionList = this.formList[0].optionList[0] || [],
        pickerItem = optionList.find(
          option => option.id == this.basicData.entryItemId
        );
      if (pickerItem) {
        this.computeBalanceData(pickerItem);
      }
      // 获取 单条 详情信息
      let res = await this.ajax.getUsingFundsDataDetail(this.basicData.id);
      if (!res.success) {
        uni.showToast({
          title: res.message || '服务异常',
          icon: 'none'
        });
        return;
      }
      let basicData = res.object;
      basicData.fundBudgetList.forEach(
        (item, index) => (item.rowIndex = index)
      );
      this.basicData = basicData;

      this.editType = 'edit';
    },
    /**@desc 取消编辑 */
    handleQuitEdit() {
      if (this.editType == 'add') {
        this.goBack();
        return;
      }
      this.editType = 'preview';
      this.getFundDetail();
    },
    /**@desc 打开提示内容 */
    handleOpenToast(toast, type) {
      this.toast = toast;
      this.type = type;
      this.show = true;
    },
    /**@desc 确认操作分发 */
    handleConfirm() {
      switch (this.type) {
        case 'report':
          this.handleReport();
          break;
        case 'delete':
          this.handleDelete();
          break;
      }
      this.handleCancelModal();
    },
    /**@desc 关闭弹窗 */
    handleCancelModal() {
      this.show = false;
      this.toast = '';
      this.type = '';
    },
    /**@desc 处理popup弹窗确认操作 */
    handleConfirmPopup() {
      switch (this.editType) {
        case 'remibur':
          this.handleRemiburData();
          break;
        case 'approval':
          this.handleApproval();
          break;
        case 'reSubmit':
          this.handleReSubmit();
          break;
      }
    },
    /**@desc 确定签名 */
    changeHandwrittenSignature(path) {
      this.$set(this.detailData, 'autograph', path);
    },
    /**@desc 提交 */
    async handleReport() {
      let res = await this.ajax.getUsingFundsDataDetail(this.basicData.id);
      if (!res.success) {
        this.$message.error(res.message || '经费使用情况详情获取失败');
        return;
      }
      this.detailData = res.object;
      let data = {
        ...res.object,
        status: 1
      };

      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleUpdateUsingFundData(data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '提交失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '提交成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 删除 */
    handleDelete() {
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleDeleteUsingFundData(this.basicData.id)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '删除失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '删除成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 报销经费 */
    handleRemiburData() {
      let boardValidate = this.$refs.remiburBoard.validate();
      if (!boardValidate) {
        return;
      }
      if (!this.detailData.reimbursementFileids) {
        uni.showToast({
          title: '请上传附件',
          icon: 'none'
        });
        return;
      }
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;

      this.ajax
        .handleUpdateUsingFundData({
          ...this.detailData,
          status: 5
        })
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '报销失败',
              icon: 'none'
            });
            return;
          }

          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 经费变更 */
    async handleChangeUsingFund() {
      let validate = this.$refs.changeBoard.validate();
      if (!validate) {
        return;
      }
      if (!this.detailData.changesReason) {
        uni.showToast({
          title: '请输入变更事由',
          icon: 'none'
        });
        return;
      }
      let data = {
        ...this.detailData,
        fundBudgetChangeList: this.staticFundBudgetList,
        status: 3
      };
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;

      this.ajax
        .handleUpdateUsingFundData(data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '变更失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '变更成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 重新提交 */
    handleReSubmit() {
      let validate = this.$refs.reSubmitBoard.validate();
      if (!validate) {
        return;
      }
      if (!this.detailData.reimbursementFileids) {
        uni.showToast({
          title: '请上传附件',
          icon: 'none'
        });
        return;
      }
      if (this.requesting) {
        return;
      }

      let data = {
        ...this.detailData,
        status: this.detailData.status == 9 ? 5 : 1
      };
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleUpdateUsingFundData(data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '提交失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '提交成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    },
    /**@desc 关闭底部弹窗*/
    handleCancelConsultModal() {
      this.showPopup = false;
      // this.getFundDetail();
    },
    /**@desc 审批 */
    handleApproval() {
      if (
        this.detailData.wfStepName == '财务科主任' &&
        !this.detailData.leaderApproverName
      ) {
        uni.showToast({
          title: '请选择分管领导审批人',
          icon: 'none'
        });
        return;
      }
      let { type, autograph } = this.detailData;

      if (!type) {
        uni.showToast({
          title: '必填信息，请勾选',
          icon: 'none'
        });
        return;
      }
      if (!autograph) {
        uni.showToast({
          title: '请签字',
          icon: 'none'
        });
        return;
      }
      let data = {
        ...this.detailData,
        type
      };
      if (this.requestLock) {
        return;
      }
      this.requestLock = true;
      this.ajax
        .handleApprovalUsingFundData(data)
        .then(res => {
          this.requestLock = false;
          if (!res.success) {
            uni.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
            return;
          }

          uni.showToast({
            title: '操作成功',
            icon: 'none'
          });
          this.goBack();
        })
        .catch(() => (this.requestLock = false));
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: 100vh;
  overflow: hidden;
  /deep/ .u-navbar {
    border-bottom: 1px solid #e9ecf1;
  }
}
.action-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #fff;
  box-shadow: 0 -1px 6px #ccc;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 9;
  height: 40px;
  .action-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $uni-text-color-grey;
    position: relative;
    &:not(:last-child)::after {
      content: ' ';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 15px;
      border-right: 1px solid #eee;
    }
    &.report-btn {
      color: $u-type-primary;
    }
    &.draft-btn {
      color: $ts-type-warning;
    }
    &.delete-btn {
      color: $ts-type-error;
    }
    &.submit-btn {
      color: $ts-type-warning;
    }
  }
}
.trasen-content {
  height: calc(100vh - 44px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /deep/ .ts-subsection-control {
    flex-shrink: 0;
    background-color: #fff;
    margin-bottom: 8px;
  }
  /deep/ .base-info-content {
    flex: 1;
    overflow: auto;
    margin-bottom: 8px;
  }
  &.preview {
    height: calc(100vh - 88px);
    overflow: auto;
  }
  .basic-form-content {
    margin-top: 8px;
    ::v-deep .form-container {
      margin-top: 0;
    }
  }
  .section-title {
    font-weight: 600;
    font-size: 16px;
    padding: 2px 15px;
  }
  .cost-detail {
    flex: 1;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    .balance {
      color: $u-type-primary;
      font-size: 10px;
    }
  }
}
.item-name-slot-content {
  display: flex;
  align-items: center;
  > .u-icon {
    margin-left: 8px;
  }
}
.consult-title {
  line-height: 40px;
  text-align: center;
  box-shadow: 0 1px 6px #ccc;
  margin-bottom: 8px;
}
.consult-content {
  padding: 16px;
  .consult-item {
    display: flex;
    margin-bottom: 8px;
    .item-title {
      width: 60px;
      position: relative;
      &.required::before {
        content: '*';
        position: absolute;
        left: -9px;
        color: red;
      }
    }
  }
  .message-item {
    align-items: center;
  }
  /deep/ .u-checkbox__icon-wrap {
    margin: 0;
  }
  .autograph-box {
    position: relative;
    background-color: rgba(204, 204, 204, 0.2);
    width: 125px;
    height: 50px;
    text-align: center;
    .autograph-text {
      line-height: 50px;
      color: #005bac;
    }
    .autograph-reset-text {
      position: absolute;
      bottom: 0;
      right: 0;
      font-size: 12px;
      border-radius: 40px 0 0 0;
      background-color: rgba(0, 91, 172, 0.2);
      width: 36px;
      height: 32px;
      line-height: 40px;
      text-align: right;
      padding-right: 4px;
      color: #005bac;
    }
  }
}
.consult-action {
  display: flex;
  border-top: 1px solid #eee;
  .consult-btn {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
    color: $uni-text-color-grey;
    position: relative;
    &:not(:last-child)::after {
      content: ' ';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 15px;
      border-right: 1px solid #eee;
    }
    &.report-btn {
      color: $u-type-primary;
    }
  }
}
.log-content {
  flex: 1;
  overflow: hidden;
  background: #fff;
  padding: 8px 10px;
  > view {
    height: 100%;
    overflow: auto;
  }

  /deep/ .u-steps-item__wrapper__dot {
    background-color: $u-type-primary !important;
  }
  /deep/ .u-text__value {
    font-weight: 600;
  }
  .u-steps-item:not(:last-child) .desc-content::after {
    content: ' ';
    border-left: 1px solid $u-type-primary;
    height: 100%;
    position: absolute;
    left: -13px;
    top: 0;
  }
  .desc-content {
    uni-view {
      color: #999;
    }
    position: relative;
  }
}
/deep/ .approval-fund-content {
  .balance-content {
    color: $u-type-primary;
    text-align: right;
  }
  .u-form-item__body {
    align-items: baseline;
  }
  .u-form-item__body__right__content__slot {
    flex: 0;
  }
  .item__body__right__content__icon {
    flex: 1;
    overflow: hidden;
  }
}
/deep/ .bottom-popup .u-popup__content {
  max-height: 80vh;
  .popup-content {
    flex: 1;
    overflow: auto;
  }
}
</style>
