<template>
  <view class="trasen-container">
    <u-navbar title="使用经费" title-bold @leftClick="goBack"></u-navbar>
    <view class="trasen-content">
      <ts-search
        v-model="condition"
        :animation="true"
        placeholder="项目名称/项目负责人"
        @search="refresh"
        @custom="refresh"
        @clear="refresh"
      >
        <u-icon
          class="add-btn"
          name="plus"
          color="#005bac"
          size="18"
          @click="handleShowDetail"
        ></u-icon>
      </ts-search>

      <ts-subsection-control
        :list="actionList"
        mode="text"
        :current="activeFundsType"
        :activeStyle="{ color: '#005bac' }"
        lineColor="#005bac"
        @change="handleSubChange"
      ></ts-subsection-control>

      <view style="flex: 1; position: relative;">
        <mescroll
          ref="mescroll"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <fund-item
            v-for="(item, index) of list"
            :key="index"
            :data="item"
            class="fund-item"
            @click.native="handleShowDetail(item, 'preview')"
          ></fund-item>
        </mescroll>
      </view>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import fundItem from './components/fund-item.vue';

export default {
  components: { mescroll, fundItem },
  data() {
    return {
      condition: '',
      activeFundsType: 0,
      actionList: [
        {
          name: '待办'
        },
        {
          name: '处理中'
        },
        {
          name: '已完成'
        }
      ],

      list: []
    };
  },
  onLoad() {
    this.ajax
      .getUsingFundsDataList({
        pageNo: 1,
        pageSize: 15,
        index: 2,
        sord: 'desc',
        sidx: 'b.create_date'
      })
      .then(res => {
        this.$set(this.actionList[1], 'name', `处理中(${res.totalCount})`);
      });
  },
  methods: {
    goBack() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    refresh() {
      uni.showLoading({
        title: '加载中...'
      });
      try {
        this.$refs.mescroll.downCallback();
      } catch (e) {
        console.log(e);
        uni.hideLoading();
      }
    },
    handleShowDetail(item, type) {
      uni.setStorageSync('fundItem', {
        ...item,
        activeFundsType: this.activeFundsType
      });
      uni.setStorageSync('editType', type);
      uni.navigateTo({
        url: `/pages/fund-management/using-funds/preview-fund`
      });
    },
    handleSubChange({ name, index }) {
      this.activeFundsType = index;
      this.refresh();
    },
    getListData(page, successCallback, errorCallback) {
      let data = {
        pageNo: page.num,
        pageSize: page.size,
        index: this.activeFundsType + 1,
        sord: 'desc',
        sidx: 'b.create_date'
      };
      this.condition && (data.condition = this.condition);
      this.ajax
        .getUsingFundsDataList(data)
        .then(res => {
          uni.hideLoading();
          let rows = res.rows.map(item => ({
            ...item,
            statusLabel:
              [
                '待提交',
                '预算待审批',
                '预算审批完成待报销',
                '预算变更待审批',
                '预算变更完成',
                '报销待审批',
                '报销已完成',
                '预算驳回',
                '变更驳回',
                '报销驳回'
              ][item.status] || ''
          }));
          if (this.activeFundsType < 2) {
            let newName = ['待办', '处理中'][this.activeFundsType];
            newName += `(${res.totalCount})`;
            this.$set(this.actionList[this.activeFundsType], 'name', newName);
          }
          successCallback(rows, res.totalCount);
        })
        .catch(err => {
          uni.hideLoading();
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: 100vh;
  overflow: hidden;
}
.trasen-content {
  height: calc(100vh - 44px);
  display: flex;
  flex-direction: column;
}
.ts-search {
  padding: 6px 15px;
  margin-bottom: 8px;
  background-color: #fff;
}
.add-btn {
  margin-left: 16px;
}
.ts-subsection-control {
  background-color: #fff;
  margin-bottom: 10px;
}
.fund-item {
  margin-bottom: 8px;
  &:last-child {
    margin: 0;
  }
}
</style>
