<template>
  <view class="achievement-log-container">
    <view v-for="(item, index) of data" :key="index" class="achievement-item">
      <view class="section-title">
        <view><view class="icon"></view>绩效信息</view>
        <text>{{ item.createDate }}</text>
      </view>
      <base-info-board :data="item" :dataList="formList"></base-info-board>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    data: Array
  },
  data() {
    return {
      formList: [
        {
          title: '运行监控表',
          prop: 'monitoringTableFileids',
          type: 'file'
        },
        {
          title: '运行监控报告',
          prop: 'monitoringReportFileids',
          type: 'file'
        },
        {
          title: '备注',
          prop: 'remark'
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.achievement-log-container {
  background-color: #fff;
  padding: 10px 0;
}
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  font-size: 12px;
  color: $uni-text-color-grey;
  > view {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    color: $uni-text-color;
    &::before {
      content: ' ';
      height: 16px;
      width: 4px;
      background-color: $u-type-primary;
      margin-right: 4px;
    }
  }
}
</style>
