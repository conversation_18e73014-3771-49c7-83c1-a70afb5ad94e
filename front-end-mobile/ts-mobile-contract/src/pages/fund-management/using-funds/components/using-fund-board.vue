<template>
  <view ref="boardContainer" class="using-fund-board-container">
    <view class="toltip-container">
      <view class="toltip-item flex-col-center">
        <view class="dot-icon budget-icon"></view>
        预算金额
      </view>
      <view class="toltip-item flex-col-center">
        <view class="dot-icon acture-icon"></view>
        实际执行金额
      </view>
      <view class="toltip-item flex-col-center">
        <view class="dot-icon precent-icon"></view>
        预算执行率
      </view>
    </view>
    <table border="1" cellspacing="0" class="table">
      <colgroup>
        <col width="80" />
        <col width="165" />
        <col width="100" />
      </colgroup>
      <tr>
        <th>
          <view class="cell">类别</view>
        </th>
        <th>
          <view class="cell">项目</view>
        </th>
        <th>
          <view class="cell">情况</view>
        </th>
      </tr>
      <template v-for="item of value">
        <tr
          :key="item.rowIndex * 3"
          @click="handleRowClick({ $event, row: item })"
          :class="{
            active:
              currentRow.rowIndex == item.rowIndex && editType != 'preview'
          }"
          class="fund-table-row"
        >
          <td rowspan="3">
            <view class="cell">
              <u-textarea
                v-model="item.type"
                :disabled="!isEdit && status != 7"
                placeholder="请输入类别"
                border="bottom"
                autoHeight
              ></u-textarea>
            </view>
          </td>
          <td rowspan="3">
            <view class="cell">
              <u-textarea
                v-model="item.smallItem"
                :disabled="!isEdit && status != 7"
                placeholder="请输入项目"
                border="bottom"
                autoHeight
              ></u-textarea>
            </view>
          </td>
          <td>
            <view class="cell flex-col-center">
              <view class="dot-icon budget-icon"></view>
              <ts-input
                v-model="item.budgetAmount"
                :disabled="!isEdit && status != 7 && editType != 'change'"
                placeholder="预算金额"
                border="none"
                type="number"
              ></ts-input>
            </view>
          </td>
        </tr>
        <tr
          :key="item.rowIndex * 3 + 1"
          :class="{
            active:
              currentRow.rowIndex == item.rowIndex && editType != 'preview'
          }"
          class="fund-table-row"
        >
          <td>
            <view class="cell flex-col-center">
              <view class="dot-icon acture-icon"></view>
              <ts-input
                v-model="item.actualAmount"
                :disabled="
                  !lastTwoColEditable || (editType == 'reSubmit' && status != 9)
                "
                placeholder="实际执行金额"
                border="none"
                type="number"
              ></ts-input>
            </view>
          </td>
        </tr>
        <tr
          :key="item.rowIndex * 3 + 2"
          :class="{
            active:
              currentRow.rowIndex == item.rowIndex && editType != 'preview'
          }"
          class="fund-table-row"
        >
          <td>
            <view
              class="cell flex-col-center percent-cell"
              :class="{ 'no-val': !item.actualAmount }"
            >
              <view class="dot-icon precent-icon"></view>
              {{ computePercent(item) }}
            </view>
          </td>
        </tr>
      </template>

      <tr>
        <td rowspan="3">
          <view class="cell flex-col-center">合计</view>
        </td>
        <td rowspan="3">
          <view class="cell"> </view>
        </td>
        <td>
          <view class="cell flex-col-center percent-cell">
            <view class="dot-icon budget-icon"></view>
            {{ totalData.budgetTotal.toFixed(2) }}
          </view>
        </td>
      </tr>
      <tr>
        <td>
          <view class="cell flex-col-center percent-cell">
            <view class="dot-icon acture-icon"></view>
            {{ totalData.actureTotal.toFixed(2) }}
          </view>
        </td>
      </tr>
      <tr>
        <td>
          <view class="cell flex-col-center percent-cell">
            <view class="dot-icon precent-icon"></view>
            {{ totalData.percentTotal.toFixed(2) }}%
          </view>
        </td>
      </tr>
    </table>

    <view
      v-if="['edit', 'add', 'change'].includes(editType)"
      class="action-row"
    >
      <u-icon name="plus-circle" size="24" @click="handleAddRow"></u-icon>
      <u-icon name="minus-circle" size="24" @click="handleDeleteRow"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    editType: {
      type: String,
      default: () => 'edit'
    },
    status: [String, Number],
    balanceData: Object
  },
  data() {
    return {
      currentRow: {}
    };
  },
  computed: {
    isEdit() {
      return ['edit', 'add', 'change'].includes(this.editType);
    },
    lastTwoColEditable() {
      return ['remibur', 'reSubmit'].includes(this.editType);
    },
    // 总计数据
    totalData() {
      let budgetTotal = 0,
        actureTotal = 0,
        percentTotal = 0;

      this.value.map(({ budgetAmount = 0, actualAmount = 0 }) => {
        let budget = Number(budgetAmount),
          actual = Number(actualAmount);

        !isNaN(budget) && (budgetTotal += budget * 100);
        !isNaN(actual) && (actureTotal += actual * 100);
      });

      if (actureTotal > 0) {
        percentTotal = (actureTotal / budgetTotal) * 100;
      }
      return {
        budgetTotal: budgetTotal / 100,
        actureTotal: actureTotal / 100,
        percentTotal
      };
    }
  },
  mounted() {
    window.addEventListener('click', this.handleWindowClick);
  },
  methods: {
    validate() {
      let fundBudgetList = this.value || [],
        failedKey = '',
        failedIndex = 0,
        validateKeyList = ['type', 'smallItem', 'budgetAmount'];

      if (!fundBudgetList.length) {
        uni.showToast({
          title: '请至少填写一条预算信息',
          icon: 'none'
        });
        return false;
      }

      ['remibur', 'reSubmit'].includes(this.editType) &&
        [9, 2].includes(this.status) &&
        (validateKeyList = ['actualAmount']);
      // 校验是否存在预算非必填情况
      if (
        fundBudgetList.some((item, index) => {
          return validateKeyList.some(key => {
            if (item[key] == null || item[key] == '') {
              failedKey = key;
              failedIndex = index;
              return true;
            }
          });
        })
      ) {
        this.focusTableRow(failedIndex);
        uni.showToast({
          title:
            {
              type: '类别不能为空',
              smallItem: '项目名称不能为空',
              budgetAmount: '预算金额不能为空',
              actualAmount: '实际执行金额不能为空'
            }[failedKey] || '必填数据不能为空',
          icon: 'none'
        });
        return false;
      }

      //计算是否超出预算
      if (['edit', 'add', 'change'].includes(this.editType)) {
        let sum = 0,
          { approvalBalance = null, supportBalance = null } =
            this.balanceData || {},
          balance = (approvalBalance * 100 + supportBalance * 100) / 100;

        fundBudgetList.map(item => {
          sum += item.budgetAmount * 100;
        });
        sum /= 100;
        if (sum > balance) {
          uni.showToast({
            title: '预算超出剩余经费，请重新分配',
            icon: 'none'
          });
          return false;
        }
      }
      return true;
    },
    focusTableRow(index) {
      requestAnimationFrame(() => {
        this.currentRow = this.value[index];
      });
    },
    handleWindowClick(event) {
      let el = this.$refs.boardContainer.$el;
      !el.contains(event.target) && (this.currentRow = {});
    },
    handleAddRow() {
      this.$emit(
        'change',
        this.value.concat([
          {
            rowIndex: this.value.length
          }
        ])
      );
    },
    handleDeleteRow() {
      if (!Object.keys(this.currentRow).length) {
        uni.showToast({
          title: '请选择需要删除的行',
          icon: 'none'
        });
        return;
      }

      this.value.splice(this.currentRow.rowIndex, 1);
      this.value.map((item, index) => (item.rowIndex = index));
      this.currentRow = {};
    },
    handleRowClick({ row }) {
      if (['preview'].includes(this.editType)) {
        return;
      }
      this.currentRow = row;
    },
    computePercent(row) {
      if (!row.actualAmount) {
        return '预算执行率';
      }

      return (
        (((row.actualAmount * 100) / (row.budgetAmount * 100)) * 100).toFixed(
          2
        ) + '%'
      );
    }
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleWindowClick);
  }
};
</script>

<style lang="scss" scoped>
.flex-col-center {
  display: flex;
  align-items: center;
}
.using-fund-board-container {
  padding: 8px;
  background-color: #fff;
}
.toltip-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4px;
  .toltip-item {
    margin-left: 8px;
  }
}

.dot-icon {
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background-color: $sexman-color;
  margin-right: 4px;
  flex-shrink: 0;
  &.acture-icon {
    background-color: $uni-color-warning;
  }
  &.precent-icon {
    background-color: $u-type-primary;
  }
}

.action-row {
  display: flex;
  justify-content: flex-end;
  padding: 4px 8px;
  .u-icon {
    margin-left: 8px;
  }
}
.table {
  width: 100%;
  border-color: #eee;
  overflow: hidden;
  table-layout: fixed;
  word-break: break-all;
  .cell {
    padding: 2px 8px;
    font-size: 14px;
    height: 100%;
    &.percent-cell {
      font-size: 12px;
      line-height: 24px;
      &.no-val {
        color: #c0c4cc;
      }
    }
  }
  tr td {
    height: 1px;
  }

  .fund-table-row {
    &.active {
      background-color: mix($u-type-primary, #fff, 8%);
    }
  }
}
/deep/ {
  .u-textarea {
    height: 100%;
    background-color: transparent;
    padding: 0;
    .uni-textarea-wrapper {
      height: 100% !important;
    }
  }
  .u-input {
    background-color: transparent !important;
    .uni-input-wrapper {
      font-size: 12px;
    }
  }
}
</style>
