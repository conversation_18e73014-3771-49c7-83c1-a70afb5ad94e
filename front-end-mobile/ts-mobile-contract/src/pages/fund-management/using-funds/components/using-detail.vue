<template>
  <view class="using-detail-container">
    <template v-for="(item, index) of previewList">
      <view :key="index * 2" class="section-title">
        预算信息({{ item.time }})
        <view
          v-if="index == 0"
          class="show-all-btn"
          @click="showAll = !showAll"
        >
          {{ showAll ? '查看当前' : '查看全部' }}
        </view>
      </view>
      <using-fund-board
        :key="index * 2 + 1"
        v-model="item.list"
        editType="preview"
      ></using-fund-board>
    </template>
  </view>
</template>

<script>
import usingFundBoard from './using-fund-board.vue';
export default {
  components: {
    usingFundBoard
  },
  // model: {
  //   prop: 'showAll',
  //   event: 'change'
  // },
  props: {
    // showAll: Boolean,
    detailData: Object
  },
  data() {
    return {
      showAll: false
    };
  },
  computed: {
    fundBudgetList() {
      let { fundBudgetList = [] } = this.detailData || {},
        timeList = fundBudgetList.map(item => item.updateDate),
        fundList = [];

      timeList = Array.from(new Set(timeList));
      timeList.map(time => {
        let list = fundBudgetList.filter(item => item.updateDate == time);
        fundList.push({
          time,
          list: list.map((item, index) => {
            return {
              ...item,
              rowIndex: index
            };
          })
        });
      });

      return fundList;
    },
    previewList() {
      if (this.showAll) {
        return this.fundBudgetList;
      } else {
        return this.fundBudgetList.filter(
          item => item.list[0].fundUseId == this.detailData.id
        );
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.using-detail-container {
  background-color: #fff;
  .section-title {
    padding: 8px;
    padding-bottom: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .show-all-btn {
      font-weight: normal;
      border: 1px solid $u-type-primary;
      color: $u-type-primary;
      padding: 2px 8px;
      border-radius: 16px;
      font-size: 12px;
    }
  }
}
</style>
