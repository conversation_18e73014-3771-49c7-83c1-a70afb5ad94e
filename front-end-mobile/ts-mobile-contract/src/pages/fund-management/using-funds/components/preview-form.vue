<template>
  <base-info-board :data="data" :dataList="formList">
    <template v-slot:progress="{ cell }">
      <view class="right-container">
        <view class="progress-container">
          <ts-line-progress :percentage="cell" height="8"></ts-line-progress>
        </view>
      </view>
    </template>
    <view slot="approvedItemFund" class="cost-detail">
      <view>
        {{ data.approvedItemFund }}
      </view>
      <view class="balance">余额：{{ balanceList[0] }}</view>
    </view>
    <view slot="supportingFund" class="cost-detail">
      <view>
        {{ data.supportingFund }}
      </view>
      <view class="balance">余额：{{ balanceList[1] }}</view>
    </view>
  </base-info-board>
</template>

<script>
export default {
  props: {
    basicData: Object,
    detailData: Object
  },
  data() {
    return {
      formList: [
        {
          title: '项目名称',
          prop: 'itemName'
        },
        {
          title: '项目负责人',
          prop: 'leaderUserName'
        },
        {
          title: '进度',
          prop: 'progress'
        },
        {
          title: '核定项目经费',
          prop: 'approvedItemFund'
        },
        {
          title: '医院配套经费',
          prop: 'supportingFund'
        },
        {
          title: '预算总金额',
          prop: 'budgetAmount'
        },
        {
          title: '实际执行总额',
          prop: 'actualAmount'
        },
        {
          title: '实际执行率',
          prop: 'percent'
        }
      ]
    };
  },
  computed: {
    data() {
      let {
          approvedItemFund = 0,
          supportingFund = 0,
          itemActualAmount = 0,
          budgetAmount = 1,
          actualAmount = 0
        } = this.basicData || {},
        progress = itemActualAmount / (approvedItemFund + supportingFund),
        percent = ((actualAmount / budgetAmount) * 100).toFixed(2);
      progress = progress * 100;
      progress = parseFloat(progress?.toFixed(2));

      return {
        ...this.detailData,
        progress,
        actualAmount: this.basicData.actualAmount,
        budgetAmount: this.basicData.budgetAmount,
        percent: percent + '%'
      };
    },
    balanceList() {
      let { approvedItemFund = 0, supportingFund = 0, itemActualAmount = 0 } =
          this.basicData || {},
        approvalBalance = approvedItemFund - itemActualAmount,
        supportBalance = 0;

      if (approvalBalance < 0) {
        supportBalance = supportingFund + approvalBalance;
        approvalBalance = 0;
      } else {
        supportBalance = supportingFund;
      }

      approvalBalance && (approvalBalance = approvalBalance.toFixed(2));
      supportBalance && (supportBalance = supportBalance.toFixed(2));
      return [approvalBalance, supportBalance];
    }
  }
};
</script>

<style lang="scss" scoped>
.progress-container {
  width: 150px;
  height: 8px;
}
.right-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.cost-detail {
  flex: 1;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  .balance {
    color: $u-type-primary;
    font-size: 10px;
  }
}
</style>
