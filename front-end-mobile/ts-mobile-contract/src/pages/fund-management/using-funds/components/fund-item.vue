<template>
  <view class="fund-item-content">
    <view class="title-content">
      <view class="fund-type">{{ data.fundTypeName }}</view>
      <view class="flex-grow fund-name">{{ data.itemName }}</view>
      <view
        class="fund-status"
        :class="
          ['draft-status', 'submit-status', '', 'submit-status'][data.status]
        "
      >
        {{ data.statusLabel }}
      </view>
    </view>
    <view class="item-info fund-cost-info" v-if="data.approvedItemFund">
      <view>
        核定项目经费：<text>{{ data.approvedItemFund.toFixed(2) }}</text>
      </view>
      <view class="flex-align-center">
        <view class="progress">
          <view
            class="percent"
            :style="{ width: projectPercent + '%' }"
            :class="{ warning: projectPercent >= 100 }"
          ></view>
        </view>
        <view class="percent-label">
          {{ projectPercent + '%' }}
        </view>
      </view>
    </view>
    <view class="item-info fund-cost-info" v-if="data.supportingFund">
      <view>
        医院配套经费：<text>{{ data.supportingFund.toFixed(2) }}</text>
      </view>
      <view class="flex-align-center">
        <view class="progress">
          <view
            class="percent"
            :style="{ width: hospitalPercent + '%' }"
            :class="{ warning: hospitalPercent >= 100 }"
          ></view>
        </view>
        <view class="percent-label">
          {{ hospitalPercent + '%' }}
        </view>
      </view>
    </view>
    <view class="item-info">
      {{ data.leaderUserName }}-{{ data.deptName }}
    </view>
  </view>
</template>

<script>
export default {
  props: {
    data: Object
  },
  computed: {
    projectPercent() {
      let { approvedItemFund = 0, itemActualAmount = 0 } = this.data;
      if (!itemActualAmount || !approvedItemFund) {
        return 0;
      }
      if (itemActualAmount >= approvedItemFund) {
        return 100;
      }
      let percent = (itemActualAmount / approvedItemFund) * 100;
      return percent.toFixed(2);
    },
    hospitalPercent() {
      let {
        approvedItemFund = 0,
        supportingFund = 0,
        itemActualAmount = 0
      } = this.data;
      if (!supportingFund || approvedItemFund >= itemActualAmount) {
        return 0;
      }
      let percent =
        ((itemActualAmount - approvedItemFund) / supportingFund) * 100;

      return percent.toFixed(2);
    }
  }
};
</script>

<style lang="scss" scoped>
.fund-item-content {
  background: #fff;
  padding: 8px 15px;
}
.title-content {
  display: flex;
  overflow: hidden;
  margin-bottom: 6px;

  .fund-type {
    font-size: 10px;
    padding: 2px 3px;
    color: #fff;
    background: #5aaaea;
    border-radius: 0 4px 0 4px;
  }
  .fund-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 600;
    margin: 0 4px;
  }
  .fund-status {
    font-weight: 600;
    color: $uni-text-color-grey;
  }
  .draft-status {
    color: $ts-type-warning;
  }
  .submit-status {
    color: $u-type-primary;
  }
}
.item-info {
  color: #666;
  font-size: 12px;
  line-height: 18px;
}
.fund-cost-info {
  color: $uni-text-color;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text {
    color: $ts-type-warning;
  }
}
.progress {
  height: 8px;
  width: 80px;
  background-color: $uni-text-color-disable;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 8px;
  .percent {
    height: 8px;
    background-color: $u-type-primary;
    &.warning {
      background-color: $uni-color-error;
    }
  }
}
.flex-align-center {
  display: flex;
  align-items: center;
  width: 130px;
  .percent-label {
    flex: 1;
    text-align: right;
  }
}
</style>
