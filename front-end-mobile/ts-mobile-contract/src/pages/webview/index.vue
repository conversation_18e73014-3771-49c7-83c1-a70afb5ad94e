<template>
  <view class="ts-container">
    <u-navbar @leftClick="returnBack"></u-navbar>
    <view class="web-view">
      <web-view :src="url"></web-view>
    </view>
  </view>
</template>

<script>
var wv;
export default {
  data() {
    return {
      url: '',
      path: '',
      fromPage: null,
      index: null
    };
  },
  onLoad(e) {
    // #ifdef APP-PLUS
    let currentWebview = this.$mp.page.$getAppWebview();
    setTimeout(function() {
      wv = currentWebview.children()[0];
      wv.setStyle({ scalable: true });
    }, 500);
    // #endif
    // #ifdef H5
    this.setViewport(
      'width=device-width, user-scalable=yes, initial-scale=1.0'
    );
    // #endif
    if (e.path) this.path = e.path;
    if (e.fromPage) this.fromPage = e.fromPage;
    if (e.index) this.index = e.index;
    // 获取传递过来的链接
    this.url = `${e.url}&_copyable=false&_printable=false`;
  },
  onUnload() {
    this.setViewport(
      'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0'
    );
  },
  methods: {
    setViewport(content) {
      const meta = document.querySelector('meta[name=viewport]');
      if (!meta) return;
      meta.setAttribute('content', content);
    },
    returnBack() {
      if (this.path) {
        uni.redirectTo({
          url: `${this.path}${
            this.fromPage || this.index
              ? '?fromPage=' + this.fromPage + '&index=' + this.index
              : ''
          }`
        });
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.tra-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .web-view {
    flex: 1;
    position: relative;
  }
}
</style>
