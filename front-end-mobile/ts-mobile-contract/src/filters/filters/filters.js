export default {
  headImgFilter(val) {
    return window.location.origin + val;
  },
  sexClassFilter(val) {
    return val == 1 ? 'sex-woman' : 'sex-man';
  },
  firstNameFilter(val = '') {
    return val.substring(val.length - 2);
  },
  indexTimeFilter: function(time) {
    if (time) {
      let current = new Date(),
        cY = current.getFullYear(),
        cM = current.getMonth() + 1,
        cD = current.getDate(),
        currentStr = `${cY}年${cM}月${cD}日`,
        getDate = new Date(time.replace(/-/g, '/')),
        gY = getDate.getFullYear(),
        gM = getDate.getMonth() + 1,
        gD = getDate.getDate(),
        ghh = getDate.getHours(),
        gmm =
          getDate.getMinutes() < 10
            ? `0${getDate.getMinutes()}`
            : getDate.getMinutes(),
        getDateStr = `${gY}年${gM}月${gD}日`;
      if (currentStr === getDateStr) {
        return `今天 ${ghh}:${gmm}`;
      } else if (gY === cY) {
        return `${gM}月${gD}日 ${ghh}:${gmm}`;
      } else {
        return `${gY}年${gM}月${gD}日 ${ghh}:${gmm}`;
      }
    }
    return '';
  }
};
