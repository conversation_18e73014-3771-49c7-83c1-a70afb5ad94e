<template>
  <view class="base-info-board-container">
    <view v-for="item of dataList" :key="item.prop" class="base-info-item">
      <view class="base-info-title">{{ item.title }}</view>
      <slot :name="item.prop" :prop="item" :data="data" :cell="data[item.prop]">
        <template v-if="item.type == 'file'">
          <base-upload v-model="data[item.prop]" disabled></base-upload>
        </template>
        <template v-else>
          <view class="base-info-content">{{ data[item.prop] }}</view>
        </template>
      </slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'base-info-board',
  props: {
    data: Object,
    dataList: Array
  }
};
</script>

<style lang="scss" scoped>
.base-info-item {
  display: flex;
  background-color: #fff;
  padding: 12px 15px;
  font-size: 14px;
  position: relative;
  flex-wrap: wrap;
  color: #333;
  & + &::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    left: 15px;
    height: 1px;
    transform: scaleY(0.5);
    background-color: #eee;
  }
  .base-info-title {
    flex-shrink: 0;
    margin-right: 8px;
  }
  .base-info-content {
    flex: 1;
    text-align: right;
  }
}
</style>
