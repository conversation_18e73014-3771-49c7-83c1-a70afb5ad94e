<template>
  <view class="base-upload-container">
    <view class="upload-icon" v-if="deletable" @click="upload">
      <u-icon name="plus-circle" size="18"></u-icon>
    </view>
    <view class="file-list-content">
      <view
        v-for="(file, index) of fileList"
        :key="file.fileId"
        class="file-item"
      >
        <view class="file-icon">
          <u-icon name="attach" size="18"></u-icon>
        </view>
        <view class="file-title">{{ file.fileName }}</view>
        <view class="file-action-btn" @click="handleDownLoad(file)">下载</view>
        <view class="file-action-btn" @click="previewFile(file)">预览</view>
        <view
          v-if="deletable"
          @click="handleDelete({ index }, 'fileList')"
          class="file-action-btn delete-btn"
        >
          删除
        </view>
      </view>
    </view>
    <u-upload
      v-bind="elementProps"
      :fileList="picList"
      :deletable="deletable"
      @delete="handleDelete"
    >
      <slot>
        <view></view>
      </slot>
    </u-upload>
  </view>
</template>

<script>
import common from '@/util/common.js';
import Base64 from '@/assets/js/base64.min.js';

export default {
  name: 'base-upload',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    disabled: Boolean,
    chooseProps: {
      type: Object,
      default: () => ({})
    },
    elementProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      fileList: [],
      picList: []
    };
  },
  computed: {
    deletable() {
      let deletable = true;
      if (this.chooseProps.deletable == false || this.disabled) {
        deletable = false;
      }
      return deletable;
    },
    fileMinLength() {
      return this.fileList.length || this.picList.length;
    }
  },
  methods: {
    /**@desc 区分文件 */
    handleSortingFiles(list = []) {
      let files = [],
        imgs = [];
      list.map(file => {
        let newFileItem = {
          ...file,
          fileId: file.fileId || file.id
        };
        common.isDoc(file.fileName) && files.push(newFileItem);
        common.isImg(file.fileName) && imgs.push(newFileItem);
      });
      this.fileList.push(...files);
      this.picList.push(...imgs);
    },
    /**@desc 获取已经上传了的文件 */
    getFiles() {
      this.ajax.getFileListByBusinessId(this.value).then(res => {
        this.fileList = [];
        this.picList = [];
        this.handleSortingFiles(
          res.object.map(item => ({
            ...item,
            fileId: item.id,
            url: location.origin + item.realPath
          }))
        );
        if (!this.fileList && !this.picList) {
          this.$emit('change', '');
        }
      });
    },
    /**@desc 上传文件 */
    async upload() {
      // 选择文件
      let chooseFiles = await new Promise((resolve, reject) => {
        uni.chooseFile({
          count: 1,
          ...this.chooseProps,
          success: ({ tempFiles }) => {
            let useFullFiles = [],
              errorFiles = [];
            for (let i = 0; i < tempFiles.length; i++) {
              let file = tempFiles[i];

              if (
                common.isDoc(file.name) ||
                common.isImg(file.name) ||
                file.name.toLowerCase().indexOf('.mhtml') >= 0
              ) {
                useFullFiles.push(file);
              } else {
                errorFiles.push(file);
              }
            }

            if (errorFiles.length) {
              uni.showToast({
                title:
                  '暂不支持的文件类型！' +
                  errorFiles.map(file => file.name).join('，'),
                icon: 'none',
                duration: 3000
              });
            }
            resolve(useFullFiles);
          },
          fail() {
            resolve([]);
          }
        });
      });

      if (!chooseFiles.length) {
        return;
      }

      let businessId = this.value;
      if (!businessId) {
        businessId = common.createUUID();
      }

      //文件上传
      let uploadList = await Promise.all(
          chooseFiles.map(file =>
            this.ajax
              .handleUploadFile({
                businessId,
                file: file
              })
              .catch(res => res)
          )
        ),
        successFiles = [],
        errorFiles = [];
      uploadList.map((res, index) => {
        if (!res.success) {
          errorFiles.push({
            res,
            file: chooseFile[index]
          });
        } else {
          successFiles.push(res.object[0]);
        }
      });

      //提示上传失败文件
      if (errorFiles.length) {
        let title = '';
        if (errorFiles.length == 1) {
          let { res, file } = errorFiles[0];
          title = file.name + '，' + (res.message || '上传失败');
        } else {
          title =
            errorFiles.map(item => item.file.name).join('，') + '，上传失败';
        }

        uni.showToast({
          title,
          icon: 'none'
        });
      }
      if (!successFiles.length) {
        return;
      }

      //区分 doc/img
      this.handleSortingFiles(
        successFiles.map(item => ({
          ...item,
          fileName: item.fileRealName,
          url: location.origin + item.filePath
        }))
      );

      if ((this.fileList.length || this.picList.length) && !this.value) {
        this.$emit('change', businessId);
      }
    },
    /**@desc 删除附件 */
    handleDelete({ index }, type = 'picList') {
      let deleteFile = this[type][index] || {};
      this.ajax.handleDeleteFile(deleteFile.fileId).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '删除失败',
            icon: 'none'
          });
          return;
        }
        uni.showToast({
          title: '删除成功',
          icon: 'none'
        });
        this[type].splice(index, 1);
        if (!this.fileList && !this.picList) {
          this.$emit('change', '');
        }
      });
    },
    /**@desc 下载 */
    handleDownLoad(file) {
      let fileUrl = file.url;
      if (file.realPath.indexOf('ts-basics-bottom') == -1) {
        fileUrl =
          location.origin +
          '/ts-document/attachment/downloadFile/' +
          file.fileId;
      }
      // #ifdef H5
      let ua = navigator.userAgent.toLowerCase();
      if (ua.indexOf('micromessenger') >= 0 && ua.match(/ipad|iphone|ipod/)) {
        uni.showModal({
          title: '提示',
          content: '请在浏览器中打开该页面进行下载',
          showCancel: false
        });
      } else {
        let el = document.createElement('a');
        el.href = fileUrl;
        el.target = '_blank';
        el.download = file.fileName;
        document.body.appendChild(el);
        el.click();
        el.remove();
      }
      // #endif
      // #ifndef H5
      uni.downloadFile({
        url: file.filePath,
        success: res => {
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePaths,
              success: function(data) {
                this.$u.toast('下载完成');
              }
            });
          }
        }
      });
      // #endif
    },
    /**@desc 预览 */
    previewFile(e) {
      let filePath = `${this.$documentPreviewHost}${e.realPath}?fullfilename=${
        e.fileId
      }.${e.fileSuffix || e.fileName.split('.')[1]}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
      });
    }
  },
  watch: {
    value: {
      handler(val) {
        val && this.getFiles();
        if (!val) {
          this.fileList = [];
          this.picList = [];
        }
      },
      immediate: true
    },
    fileMinLength(val) {
      val == 0 && this.$emit('change', null);
    }
  }
};
</script>

<style lang="scss" scoped>
.base-upload-container {
  width: 100%;
  position: relative;
  padding-top: 4px;
  .upload-icon {
    position: absolute;
    right: 0;
    top: -19px;
  }
  .file-list-content + .u-upload {
    margin-top: 4px;
  }
}
.file-item {
  display: flex;
  align-items: baseline;
  width: 100%;
  overflow: hidden;
  .file-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
}
.file-action-btn {
  flex-shrink: 0;
  color: $u-type-primary;
  & + & {
    margin-left: 8px;
  }
  &.delete-btn {
    color: $uni-color-error;
  }
}
/deep/ .u-upload__deletable {
  background-color: $uni-color-error;
}
</style>
