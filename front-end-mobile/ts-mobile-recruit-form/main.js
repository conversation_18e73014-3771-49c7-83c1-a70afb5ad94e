import App from "./App";

// #ifndef VUE3
import Vue from "vue";

//uview-ui
import uView from "uview-ui";
Vue.use(uView);

//trasen-ui-mobile
import trasenuUiMobile from "@trasen-oa/trasen-ui-mobile/index";
Vue.use(trasenuUiMobile);

import baseConfig from "config/config.js";
Object.keys(baseConfig).forEach((item) => {
  Vue.prototype[`$${item}`] = baseConfig[item];
});

Vue.config.productionTip = false;
App.mpType = "app";

//dayjs插件
import dayjs from 'dayjs';
Vue.prototype.$dayjs = dayjs;

const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
export function createApp() {
  const app = createSSRApp(App);
  return {
    app,
  };
}
// #endif
