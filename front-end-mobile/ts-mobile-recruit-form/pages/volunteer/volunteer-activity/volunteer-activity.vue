<template>
  <view class="content" v-if="JSON.stringify(details) !== '{}'">
    <view class="activity-info">
      <view class="tactivity-title"> {{ details.activityName }} </view>
      <view class="activity-day"> 发布日期：{{ details.createDate }} </view>
    </view>

    <view class="activity-time-container">
      <view class="title">活动时间</view>

      <view class="activity-time-item">
        <view class="top">
          <view
            class="status"
            :class="{
              [activityStatusDic[details.activityStatus]['className']]: true,
            }"
          >
            {{ activityStatusDic[details.activityStatus].label }}
          </view>
          <view>活动时长:{{ details.activityDuration }}h</view>
          <view class="report-pseron-num">
            报名人数:{{ details.recordNumbers || 0 }}人
          </view>
        </view>
        <view>活动时间:{{ details.activityTime }}</view>
        <view>报名截止日期:{{ details.activityEnrollTime }}</view>
        <view>活动地点:{{ details.activityAddress }}</view>
        <view>主办单位:{{ details.activityOrganizer }}</view>
        <view class="bottom">
          <view class="external-swc">
            社工证:{{ details.activityNeedSwc == "0" ? "无" : "有" }}
          </view>
          <view class="report-total-person"
            >活动人数:{{ details.activityNumbers }}人</view
          >
        </view>
      </view>
    </view>

    <view class="activity-content">
      <view class="title"> 活动介绍 </view>
      <view class="content-font">
        {{ details.activityRemark }}
      </view>
    </view>

    <view class="bottom-submit">
      <ts-button
        text="立即报名"
        size="primary"
        @click="handleOpenFrom"
      ></ts-button>
    </view>

    <wyb-popup
      ref="popup"
      type="bottom"
      height="700"
      :zIndex="99"
      radius="2"
      :showCloseIcon="false"
    >
      <view class="popup-content">
        <view class="top">
          <ts-button
            class="validate-btn"
            text="外部志愿者报名"
            size="warning"
            type="mini"
            @click="handleGoToReportPage"
          />
          <image
            class="close-img"
            @click="handleCloseFrom"
            src="/static/close.svg"
          />
        </view>
        <view class="content">
          <base-form
            ref="baseForm"
            :formList="formList"
            :formData.sync="form"
            :rules="rules"
            :showSubmitButton="false"
          >
            <template slot="idCard">
              <view class="id-card-container">
                <ts-input
                  class="select-input-contaier"
                  v-model="form.idCard"
                  placeholder="请输入身份证"
                  type="text"
                  readonly
                  :border="$formInputBorder"
                  input-align="right"
                  @click.native="handleClickIdentityNumber()"
                ></ts-input>

                <ts-button
                  class="validate-btn"
                  text="验证"
                  size="success"
                  type="mini"
                  @click="handleVaildateIdCard"
                />
              </view>
            </template>
          </base-form>

          <view
            class="submit-btn"
            :class="!isCheckIdCard ? 'info' : ''"
            @click="handleSubmitReport"
          >
            确定报名</view
          >
        </view>
      </view>
    </wyb-popup>

    <keyboard-package
      :type="2"
      @input="inputVal"
      :safeAreaInsetBottom="true"
      ref="KeyboardPackage"
    />
  </view>
</template>

<script>
import KeyboardPackage from "components/keyboard-package/keyboard-package.vue";
import WybPopup from "components/wyb-popup/wyb-popup.vue";
import BasisForm from "components/base-form/base-form.vue";
import {
  voluntariesActivityInfo,
  voluntariesActivityGetPersonByIdCard,
  voluntariesActivityRecordSave,
} from "api/volunteer/index.js";
export default {
  components: { WybPopup, BasisForm, KeyboardPackage },
  data() {
    return {
      isCheckIdCard: false,
      details: {},
      activityStatusDic: {
        0: {
          label: "未开始",
          className: "padding",
        },
        1: {
          label: "已开始",
          className: "start",
        },
        2: {
          label: "已完成",
          className: "end",
        },
      },

      formList: [
        {
          title: "身份证号码",
          rightSlot: true,
          prop: "idCard",
          propVal: "idCard",
          labelWidth: "80",
          required: true,
        },
        {
          title: "姓名",
          prop: "name",
          propVal: "name",
          placeholder: "请输入",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "联系方式",
          prop: "phone",
          propVal: "phone",
          placeholder: "请输入",
          type: "number",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        // {
        //   title: "集团短码",
        //   prop: "recordShortCode",
        //   propVal: "recordShortCode",
        //   placeholder: "请输入",
        //   type: "number",
        //   mode: "input",
        //   maxlength: 50,
        //   required: true,
        // },
      ],
      form: {
        name: "",
        idCard: "",
        phone: "",
        recordType: "",
        // recordShortCode: "",
      },
      rules: {
        idCard: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.idCard(value);
            },
            message: "请输入正确身份证号码",
            trigger: "",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "",
          },
        ],
        phone: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "请输入正确的联系方式",
            trigger: "",
          },
        ],
        // recordShortCode: [
        //   {
        //     required: true,
        //     message: "请输入集团短号",
        //     trigger: "",
        //   },
        // ],
      },
    };
  },
  async onLoad(opt) {
    this.form.activityId = opt.id;

    const res = await voluntariesActivityInfo(this.form.activityId);
    if (res.statusCode === 200 && res.success) {
      let { activityStartTime, activityEndTime } = res.object;
      let ymd = activityStartTime.slice(0, 10),
        start = activityStartTime.slice(11),
        end = activityEndTime.slice(11);
      res.object.activityTime = ymd + " " + start + "-" + end;

      this.details = res.object;
    } else {
      uni.showToast({
        title: res.message || "获取活动信息失败",
        icon: "none",
        duration: 2000,
      });
    }
  },
  methods: {
    async handleVaildateIdCard() {
      let { idCard } = this.form;
      let isVaildateIdCard = uni.$u.test.idCard(idCard);

      if (isVaildateIdCard) {
        uni.showLoading({
          mask: true,
        });
        const res = await voluntariesActivityGetPersonByIdCard(idCard);
        if (res.statusCode === 200 && res.success) {
          if (res.object === null) {
            uni.showToast({
              title: res.message || "未查询志愿者信息，请前往外部志愿者报名",
              icon: "none",
              duration: 2000,
            });
          } else {
            let { name, phone, recordType } = res.object;
            this.$set(this.form, "name", name);
            this.$set(this.form, "phone", phone);
            this.$set(this.form, "recordType", recordType);
            this.isCheckIdCard = true;
          }
        } else {
          uni.showToast({
            title: res.message || "获取志愿者信息失败,请联系管理员",
            icon: "none",
            duration: 2000,
          });
        }
        uni.hideLoading();
      } else {
        uni.showToast({
          title: "请输入正确的身份证进行验证",
          icon: "none",
          duration: 2000,
        });
      }
    },

    handleGoToReportPage() {
      uni.navigateTo({
        url: `/pages/volunteer/outside-report-volunteer-form/outside-report-volunteer-form`,
      });
    },

    async handleSubmitReport() {
      if (!this.isCheckIdCard) {
        uni.showToast({
          title: "请先验证身份证号码获取个人信息",
          icon: "none",
          duration: 2000,
        });
        return false;
      }
      const vaildate = await this.$refs.baseForm.validate();
      if (!vaildate) {
        return;
      }
      let data = Object.assign({}, this.form);
      data.recordIdcard = data.idCard;
      delete data.idCard;

      uni.showLoading({
        mask: true,
      });
      const res = await voluntariesActivityRecordSave(data);
      if (res.statusCode === 200 && res.success) {
        uni.showToast({
          title: "报名成功",
          icon: "none",
          duration: 2000,
        });
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/volunteer/success/success?title=报名成功`,
          });
        }, 2000);
      } else {
        uni.showToast({
          title: res.message || "报名失败,请联系管理员",
          icon: "none",
          duration: 2000,
        });
      }
      uni.hideLoading();
    },

    handleOpenFrom() {
      this.$refs.popup.show();
    },

    handleCloseFrom() {
      this.form = {};
      this.isCheckIdCard = false;
      this.$refs.popup.hide();
    },

    handleClickIdentityNumber() {
      this.$refs.KeyboardPackage.open();
    },

    inputVal(val) {
      const writeValue = (key, maxlength) => {
        let len = _this.form[key].length;
        if (!val) {
          _this.form[key] = _this.form[key].substring(0, len - 1);
          return;
        }
        if (len === maxlength) return;
        _this.form[key] += val;
      };

      val = val.toString();
      let key = "idCard";
      let _this = this;

      writeValue(key, 18);
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding: 8px;
  padding-bottom: 63px;
  .activity-info {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eee;
    .tactivity-title {
      font-weight: 700;
      font-size: 18px;
      color: #333;
    }
    .activity-day {
      margin: 4px 0;
      color: #000;
    }
  }
  .activity-time-container {
    display: flex;
    flex-direction: column;
    .title {
      font-weight: 800;
      margin: 4px 0;
    }
    .activity-time-item {
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid #eee;
      padding-bottom: 4px;
      margin-bottom: 4px;
      font-size: 18px;
      view {
        margin-bottom: 4px;
      }
      .top,
      .bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .status {
          border-radius: 20px;
          padding: 4px 8px;
          color: #fff;
          &.padding {
            background-color: #63d28c;
          }
          &.start {
            background-color: $ts-type-primary;
          }
          &.end {
            background-color: #999999;
          }
        }
      }
    }
  }
  .activity-content {
    font-size: 18px;
    .title {
      font-weight: 800;
      margin: 4px 0;
    }
  }
  .bottom-submit {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    position: fixed;
    bottom: 0;
    height: 55px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    padding-top: 8px;

    .ts-button {
      width: 30%;
      margin: 0 8px;
      /deep/ .u-button__text {
        color: #fff !important;
      }
    }
  }

  .popup-content {
    height: 300px;
    .content {
      padding-top: 0px;
      /deep/ .item__body__right__content__icon {
        width: 100%;
      }
    }
    .submit-btn {
      background-color: $ts-type-primary;
      padding: 8px 0;
      text-align: center;
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      &.info {
        background-color: #eee;
        color: #333;
      }
    }
    .id-card-container {
      display: flex;
      align-items: center;
      /deep/ .select-input-contaier {
        .uni-input-input {
          pointer-events: none !important;
        }
      }
      .ts-input {
        flex: 1;
      }
      .validate-btn {
        width: 50px;
        display: inline-block;
        color: #000;
      }
    }
    .top {
      display: flex;
      height: 35px;
      padding: 0 8px;
      align-items: center;
      position: relative;
      .close-img {
        width: 20px;
        height: 20px;
        line-height: 40px;
        position: absolute;
        right: 8px;
        top: 8px;
        font-size: 18px;
        font-weight: 800;
        color: #c1c1c1;
        cursor: pointer;
      }
    }
  }
}
</style>
