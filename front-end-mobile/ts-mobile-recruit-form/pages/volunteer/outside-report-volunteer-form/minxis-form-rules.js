export default {
  data() {
    return {
      rules: {
        externalName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "",
          },
        ],
        externalIdcard: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.idCard(value);
            },
            message: "请输入正确身份证号码",
            trigger: "",
          },
        ],
        externalBirthday: [
          {
            required: true,
            message: "请选择出生年月",
            trigger: "",
          },
        ],
        externalGenderLabel: [
          {
            required: true,
            message: "请选择性别",
            trigger: "",
          },
        ],
        age: [
          {
            required: true,
            message: "请输入年龄",
            trigger: "",
          },
        ],
        externalMobile: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "请输入正确的联系方式",
            trigger: "",
          },
        ],
        externalServiceType: [
          {
            required: true,
            message: "请选择服务类型",
            trigger: "",
          },
        ],
        externalSwc: [
          {
            required: true,
            message: "请选择是否有社工证",
            trigger: "",
          },
        ],
        externalSwcFile: [
          {
            required: true,
            message: "请上传社工证",
            trigger: "",
          },
        ],
      },
    };
  },
};
