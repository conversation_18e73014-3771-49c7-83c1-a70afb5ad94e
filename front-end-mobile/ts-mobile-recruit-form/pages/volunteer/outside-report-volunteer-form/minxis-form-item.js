import dic from "../config/dic";
export default {
  data() {
    return {
      dic,
      formList: [
        {
          title: "姓名",
          prop: "externalName",
          propVal: "externalName",
          placeholder: "请输入",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "身份证号码",
          rightSlot: true,
          prop: "externalIdcard",
          propVal: "externalIdcard",
          labelWidth: "80",
          itemClass: "all-width",
          maxlength: 18,
          required: true,
        },
        {
          title: "出生年月",
          prop: "externalBirthday",
          type: "select",
          mode: "time",
          placeholder: "请选择出生年月",
          required: true,
        },
        {
          title: "性别",
          prop: "externalGenderLabel",
          propVal: "externalGenderLabel",
          type: "select",
          mode: "select",
          placeholder: "请选择性别",
          optionList: [dic.sexList],
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.form.externalGender = itemNameValue;
            this.form.externalGenderLabel = itemName;
          },
          required: true,
        },
        {
          title: "年龄",
          prop: "age",
          propVal: "age",
          placeholder: "请输入",
          type: "number",
          mode: "input",
          maxlength: 50,
          required: true,
          callback: (e) => e?.replace(/\D/g, ""),
        },
        {
          title: "联系方式",
          prop: "externalMobile",
          propVal: "externalMobile",
          placeholder: "请输入",
          type: "number",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "爱心类型",
          labelWidth: 150,
          prop: "externalLoveType",
          propVal: "externalLoveType",
          rightSlot: true,
          required: true,
        },
        // {
        //   title: "爱心类型",
        //   prop: "externalLoveType",
        //   propVal: "externalLoveType",
        //   type: "select",
        //   mode: "select",
        //   placeholder: "请选择爱心类型",
        //   optionList: [dic.loveType],
        //   elementProps: {
        //     keyName: "itemName",
        //   },
        //   handleConfirm: ({ value: [{ itemName }] }) => {
        //     this.form.externalLoveType = itemName;
        //   },
        //   required: true,
        // },
        {
          title: "服务类型",
          prop: "externalServiceType",
          propVal: "externalServiceType",
          type: "select",
          mode: "select",
          placeholder: "请选择服务类型",
          optionList: [dic.serviesType],
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName }] }) => {
            this.form.externalServiceType = itemName;
          },
          required: true,
        },
        {
          title: "可服务日期",
          prop: "serviseTime",
          propVal: "serviseTime",
          required: true,
          rightSlot: true,
        },
        {
          title: "",
          prop: "dayType",
          propVal: "dayType",
          itemClass: "all-width",
          rightSlot: true,
        },
        {
          title: "",
          prop: "serviceJson",
          propVal: "serviceJson",
          itemClass: "all-width",
          rightSlot: true,
          require: true,
        },
        {
          title: "社工证",
          prop: "externalSwc",
          type: "radio",
          placeholder: "",
          radioCheckWrap: false,
          required: true,
          radioList: [
            {
              label: "没有",
              value: "0",
            },
            {
              label: "有",
              value: "1",
            },
          ],
        },
        {
          title: "职业",
          prop: "externalCareer",
          propVal: "externalCareer",
          placeholder: "请输入职业",
          type: "text",
          mode: "input",
          maxlength: 50,
        },
        {
          title: "单位",
          prop: "externalUnit",
          propVal: "externalUnit",
          placeholder: "请输入单位",
          type: "text",
          mode: "input",
          maxlength: 50,
        },
        {
          title: "特长专业",
          prop: "externalSpeciality",
          propVal: "externalSpeciality",
          placeholder: "请输入特长专业",
          type: "text",
          mode: "input",
          maxlength: 50,
        },
        {
          title: "电子邮箱",
          prop: "externalEmail",
          propVal: "externalEmail",
          placeholder: "请输入电子邮箱",
          type: "text",
          mode: "input",
          maxlength: 50,
        },
        {
          title: "服务区域",
          prop: "externalServiceArea",
          propVal: "externalServiceArea",
          placeholder: "请选择",
          type: "select",
          mode: "select",
          optionList: [dic.areaType],
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName }] }) => {
            this.form.externalServiceArea = itemName;
          },
        },
        {
          title: "居住地址",
          prop: "externalAddress",
          propVal: "externalAddress",
          placeholder: "请输入居住地址",
          type: "text",
          mode: "input",
          maxlength: 50,
        },
      ],
    };
  },
};
