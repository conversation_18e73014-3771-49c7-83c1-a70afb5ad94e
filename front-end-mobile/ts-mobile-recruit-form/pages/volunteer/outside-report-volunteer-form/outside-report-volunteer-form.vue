<template>
  <view class="content">
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    >
      <template slot="externalIdcard">
        <ts-input
          class="select-input-contaier"
          v-model="form.externalIdcard"
          placeholder="请输入身份证"
          type="text"
          readonly
          :border="$formInputBorder"
          input-align="right"
          @click.native="handleClickIdentityNumber()"
        ></ts-input>
      </template>

      <template slot="externalLoveType">
          <ld-select
			      class="select-input-contaier"
            :multiple="true"
            :list="dic.loveType"
            label-key="itemName"
            value-key="itemNameValue"
            placeholder="请选择"
            clearable
            v-model="form.externalLoveType"
          ></ld-select>
        </template>

      <template slot="serviseTime">
        <view @click="selectShow = true">
          <u-input
			      class="select-input-contaier"
            v-model="form.serviseTimeLabel"
            disabled
            placeholder="请选择"
          />
        </view>
      </template>

      <template slot="dayType">
        <view class="servise-time-contarner">
          <u-radio-group v-model="form.dayType" :wrap="false">
            <u-radio
              shape="circle"
              v-for="(item, index) in dayTimeTypeRadio"
              :key="index"
              :name="item.value"
            >
              {{ item.label }}
            </u-radio>
          </u-radio-group>

          <ts-button
            class="servise-time-save"
            text="保存"
            type="primary"
            size="mini"
            @click="handleSubmitTime"
          />
        </view>
      </template>

      <template slot="serviceJson">
        <view class="service-json">
          <view
            class="json-item"
            v-for="(item, index) in form.serviceJson"
            :index="index"
            @click="handleDeletJsonItem(item)"
          >
            {{ item.label }}
          </view>
        </view>
      </template>
    </base-form>

    <view class="bottom-submit">
      <ts-button text="提交" size="primary" @click="handleSubmit"></ts-button>
    </view>

    <ts-select
      ref="SelectTime"
      v-model="form.serviseTime"
      multiple
      :show="selectShow"
      :options="selectOptions"
      :filterable="false"
      @change="handleSelectConfirm"
      @close="selectShow = false"
    >
    </ts-select>

    <keyboard-package
      :type="2"
      @input="inputVal"
      :safeAreaInsetBottom="true"
      ref="KeyboardPackage"
    />
  </view>
</template>

<script>
import KeyboardPackage from "components/keyboard-package/keyboard-package.vue";
import BasisForm from "components/base-form/base-form.vue";
import ldSelect from "components/ld-select/ld-select.vue";
import common from "util/common.js";

import formRules from "../outside-report-volunteer-form/minxis-form-rules";
import formItem from "../outside-report-volunteer-form/minxis-form-item";

import unitFn from "../config/unitFn";

import { voluntariesExternalSave } from "api/volunteer/index.js";

export default {
  mixins: [formRules, formItem],
  components: {
    BasisForm,
    KeyboardPackage,
    ldSelect
  },
  data() {
    return {
      dayTimeTypeRadio: [
        {
          label: "上午",
          value: "上午",
        },
        {
          label: "下午",
          value: "下午",
        },
        {
          label: "全天",
          value: "全天",
        },
      ],
      // form: {
      //   externalName: "litongde",
      //   externalIdcard: "******************",
      //   externalBirthday: "",
      //   externalGender: "",
      //   externalGenderLabel: "",

      //   age: "",
      //   externalMobile: "13217481779",
      //   externalLoveType: ["康复陪伴"],
      //   externalServiceType: "失能老人",

      //   serviseTime: "",
      //   serviseTimeLabel: "",
      //   dayType: "全天",
      //   serviceJson: [],

      //   externalSwc: "1",
      //   externalSwcFile: "",
      //   externalCareer: "职业",
      //   externalUnit: "单位",
      //   externalSpeciality: "特长专业",
      //   externalEmail: "电子邮箱",
      //   externalServiceArea: "雨花区",
      //   externalAddress: "居住地址",
      // },

      form: {
        externalName: "",
        externalIdcard: "",
        externalBirthday: "",
        externalGender: "",
        externalGenderLabel: "",

        age: "",
        externalMobile: "",
        externalLoveType: [],
        externalServiceType: "",

        serviseTime: "",
        serviseTimeLabel: "",
        dayType: "全天",
        serviceJson: [],

        externalSwc: "",
        externalSwcFile: "",
        externalCareer: "",
        externalUnit: "",
        externalSpeciality: "",
        externalEmail: "",
        externalServiceArea: "",
        externalAddress: "",
      },
      selectShow: false,
      selectOptions: [
        { label: "周一", value: "周一" },
        { label: "周二", value: "周二" },
        { label: "周三", value: "周三" },
        { label: "周四", value: "周四" },
        { label: "周五", value: "周五" },
        { label: "周六", value: "周六" },
        { label: "周日", value: "周日" },
      ],
    };
  },
  watch: {
    "form.externalIdcard"(val) {
      if (uni.$u.test.idCard(val)) {
        this.$nextTick(() => {
          this.$set(this.form, "age", String(unitFn.getAgeFromIdCard(val)));
          this.$set(
            this.form,
            "externalBirthday",
            String(unitFn.getBrithDayFromIdCard(val))
          );

          let externalGenderLabel = unitFn.getSexFromIdCard(val);
          this.$set(this.form, "externalGenderLabel", externalGenderLabel);
          this.$set(
            this.form,
            "externalGender",
            externalGenderLabel === "女" ? "1" : "0"
          );
        });
      }
    },
    "form.externalSwc": {
      handler(val) {
        const isHasFile = this.formList.some(
          (item) => item.prop === "externalSwcFile"
        );
        const radioIndex = this.formList.findIndex(
          (item) => item.prop === "externalSwc"
        );

        let fileItem = {
          title: "社工证上传",
          prop: "externalSwcFile",
          imgVal: "externalSwcFile",
          type: "file",
          name: "externalSwcFile",
          required: true,
          placeholder: "社工证上传",
        };

        if (val == "1") {
          if (!isHasFile) {
            fileItem.required = true;
            this.formList.splice(radioIndex + 1, 0, fileItem);
          }
        } else {
          if (isHasFile) {
            fileItem.required = false;
            this.formList.splice(radioIndex + 1, 1);
            this.form.externalSwcFile = "";
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClickIdentityNumber() {
      this.$refs.KeyboardPackage.open();
    },
    inputVal(val) {
      const writeValue = (key, maxlength) => {
        let len = _this.form[key].length;
        if (!val) {
          _this.form[key] = _this.form[key].substring(0, len - 1);
          return;
        }
        if (len === maxlength) return;
        _this.form[key] += val;
      };

      val = val.toString();
      let key = "externalIdcard";
      let _this = this;
      writeValue(key, 18);
    },
    // 下拉提交
    handleSelectConfirm(e) {
      this.$set(
        this.form,
        "serviseTimeLabel",
        e.map((item) => item.label).join(",")
      );
    },
    // 删除
    handleDeletJsonItem(item) {
      let index = this.form.serviceJson.indexOf(item);
      if (index !== -1) {
        this.form.serviceJson.splice(index, 1);
      }
    },
    // 服务时间保存
    handleSubmitTime() {
      if (this.form.serviseTime && this.form.serviseTime.length > 0) {
        this.form.serviceJson.push({
          label: this.form.serviseTime.join(",") + " " + this.form.dayType,
        });

        this.$set(this.form, "serviseTime", []);
        this.$set(this.form, "serviseTimeLabel", "");
        this.$refs.SelectTime.valueList = [];
        this.$set(this.form, "dayType", "全天");
      } else {
        uni.showToast({
          title: "请选择可服务日期(周)",
          icon: "none",
          duration: 2000,
        });
        return false;
      }
    },
    async handleSubmit() {
      const vaildate = await this.$refs.baseForm.validate();
      if (!vaildate) {
        return;
      }

      if (this.form.serviceJson.length === 0) {
        uni.showToast({
          title: "请选择可服务日期",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      if (this.form.externalLoveType.length === 0) {
        uni.showToast({
          title: "请选择爱心类型",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      let formData = common.deepClone(this.form);
      formData.serviceTimeList = [];
      formData.serviceJson.forEach(({ label }) => {
        let [week = "", serviceTime = ""] = label.split(" "),
          weekArr = week.split(",");

        weekArr.forEach((serviceWeek) => {
          formData.serviceTimeList.push({
            serviceWeek,
            serviceTime,
          });
        });
      });
      formData.serviceJson = JSON.stringify(formData.serviceJson);
      formData.externalLoveType = formData.externalLoveType.join(',');

      delete formData.serviseTime;
      delete formData.dayType;
      delete formData.serviseTimeLabel;

      const res = await voluntariesExternalSave(formData);
      if (res.success && res.statusCode === 200) {
        uni.showToast({
          title: "报名成功!",
          icon: "none",
          duration: 1500,
        });
        setTimeout(() => {
          uni.navigateTo({
            url: `/pages/volunteer/success/success?title=报名成功`,
          });
        }, 1500);
      } else {
        uni.showToast({
          title: res.message,
          icon: "none",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-bottom: 63px;
  /deep/ .select-input-contaier {
    .uni-input-input {
      pointer-events: none !important;
    }
  }
  .servise-time-contarner {
    display: flex;
    align-items: center;
    .servise-time-save {
      margin-left: 8px;
      color: #fff;
      border-radius: 4px;
    }
  }

  .service-json {
    display: flex;
    flex-wrap: wrap;
    min-height: 60px;
    border-radius: 4px;
    padding: 8px;
    box-sizing: border-box;
    background-color: #eee;
    .json-item {
      height: 20px;
      border: 1px solid $ts-type-primary;
      color: $ts-type-primary;
      padding: 2px 4px;
      border-radius: 4px;
      margin-right: 8px;
      margin-bottom: 8px;
      cursor: pointer;
    }
  }
  /deep/ .u-form {
    .all-width {
      .item__body__right__content__icon {
        width: 100%;
      }
    }
    .u-radio {
      margin-left: 8px;
    }
  }
  .bottom-submit {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    position: fixed;
    bottom: 0;
    height: 55px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    padding-top: 8px;

    .ts-button {
      width: 30%;
      margin: 0 8px;
      /deep/ .u-button__text {
        color: #fff !important;
      }
    }
  }
}
</style>
