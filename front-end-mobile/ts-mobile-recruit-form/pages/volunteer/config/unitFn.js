let getAgeFromIdCard = (idCard) => {
  var birthday = idCard.substr(6, 8);
  var year = birthday.substr(0, 4);
  var month = birthday.substr(4, 2);
  var day = birthday.substr(6, 2);
  var age = new Date().getFullYear() - parseInt(year);

  // 如果当前月份小于出生月份，或者当前月份等于出生月份但是当前日期小于出生日期，则年龄减一
  if (
    new Date().getMonth() + 1 < parseInt(month) ||
    (new Date().getMonth() + 1 === parseInt(month) &&
      new Date().getDate() < parseInt(day))
  ) {
    age--;
  }

  return age;
};

let getBrithDayFromIdCard = (idCard) => {
  // 提取出生日日期
  var year = idCard.substring(6, 10);
  var month = idCard.substring(10, 12);
  var day = idCard.substring(12, 14);

  return year + "-" + month + "-" + day;
};

let getSexFromIdCard = (idCard) => {
  const genderDigit = parseInt(idCard.charAt(idCard.length - 2));
  return genderDigit % 2 === 0 ? "女" : "男";
};
export default {
  getAgeFromIdCard,
  getBrithDayFromIdCard,
  getSexFromIdCard,
};
