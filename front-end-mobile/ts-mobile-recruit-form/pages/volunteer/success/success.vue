<template>
  <view class="content">
    <image class="success-img" src="/static/success.svg" />
    <h3>{{ title }}</h3>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: "",
    };
  },
  onLoad(opt) {
    this.title = opt.title;
  },
};
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .success-img {
    width: 50px;
    height: 50px;
  }
}
</style>
