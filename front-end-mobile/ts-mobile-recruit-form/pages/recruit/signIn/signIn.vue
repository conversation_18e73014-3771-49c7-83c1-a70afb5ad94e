<template>
  <div class="sign-in-content">
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    />

    <ts-button
      class="button-sign-in"
      text="签到"
      size="primary"
      @click="submit"
    ></ts-button>
  </div>
</template>

<script>
import common from "util/common.js";
import { zpglInterviewMessageSignIn } from "api/data.js";
export default {
  data() {
    return {
      formList: [
        {
          title: "身份证号码",
          prop: "identityNumber",
          propVal: "identityNumber",
          placeholder: "请输入",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
      ],
      rules: {
        identityNumber: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.idCard(value);
            },
            message: "请输入正确身份证号码",
            trigger: "",
          },
        ],
      },
      form: {
        identityNumber: "",
      },
    };
  },
  methods: {
    async submit() {
      let validate = await this.$refs.baseForm.validate();
      if (!validate) {
        return false;
      }
      let data = common.deepClone(this.form);
      const res = await zpglInterviewMessageSignIn(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || "操作失败",
          icon: "none",
          duration: 2000,
        });
        return;
      } else {
        uni.showToast({
          title: "签到成功！",
          icon: "none",
          duration: 2000,
        });

        setTimeout(() => {
          uni.redirectTo({
            url: "/pages/recruit/signIn-success/signIn-success",
          });
        }, 2000);
        return;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sign-in-content {
  padding: 30px 0 0 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  .button-sign-in {
    width: 80%;
    margin-top: 10px;
    /deep/ .u-button {
      color: #fff !important;
    }
  }
}
</style>
