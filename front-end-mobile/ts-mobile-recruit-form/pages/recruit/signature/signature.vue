<template>
  <view class="ts-content">
    <view class="header-box">
      <text @click="returnBack">返回</text>
      <view class="title-top">签章设置</view>
      <view></view>
    </view>
    <view class="signature-box">
      <view class="signature-img-box" v-if="hasSignatureImg">
        <image
          class="signature-img"
          mode="scaleToFill"
          :src="signatureImg"
          style="width: 100%; height: 100%"
        ></image>
      </view>
      <view class="signature-canvas-box" v-else>
        <canvas
          class="signature-canvas"
          :id="canvasId"
          :canvas-id="canvasId"
          @touchmove="move"
          @touchstart="start($event)"
          @touchend="end"
          @touchcancel="cancel"
          @longtap="tap"
          disable-scroll="true"
          @error="error"
          style="width: 100%; height: 100%"
        ></canvas>
      </view>
    </view>
    <view class="operation-box">
      <view></view>
      <view class="btn-box">
        <view class="btn-item" @tap="clear" v-if="hasSignatureImg">
          重新签字
        </view>
        <template v-else>
          <view class="btn-item" @tap="clear">清除</view>
          <view class="btn-item primary-btn" @tap="save">提交</view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasId: "signature",

      hasSignatureImg: null,
      signatureImg: "",

      content: null,
      touchs: [],
      canvasw: 0,
      canvash: 0,

      hasDh: true,
      isPseudoHorizontal: null,
    };
  },
  watch: {
    hasSignatureImg: function (val) {
      this.getCanvase();
    },
  },
  async onLoad() {
    const signatureBase64 = uni.getStorageSync("signatureBase64");
    if (typeof signatureBase64 === "string" && signatureBase64) {
      this.hasSignatureImg = true;
      this.signatureImg = signatureBase64;
    } else {
      this.hasSignatureImg = false;
    }
    this.$nextTick(() => {
      this.getSignatureBox();
    });
    window.addEventListener("resize", this.reInitCanvase, false);
  },
  methods: {
    reInitCanvase() {
      this.$nextTick(() => {
        this.getSignatureBox();
        if (this.hasSignatureImg) return false;
        //清除画布
        if (this.isPseudoHorizontal) {
          this.content.clearRect(0, 0, this.canvash, this.canvasw);
        } else {
          this.content.clearRect(0, 0, this.canvasw, this.canvash);
        }
        this.content.draw(true);
        this.hasDh = false;
        uni.removeStorageSync("signatureBase64");
        this.getCanvase();
      });
    },
    getSignatureBox() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll(".signature-box")
        .boundingClientRect((data) => {
          this.canvasw = data[0].width - 30;
          this.canvash = data[0].height - 30;
          this.isPseudoHorizontal = this.canvasw < this.canvash;
        })
        .exec();
    },
    getCanvase() {
      this.content = null;
      //获得Canvas的上下文
      this.content = uni.createCanvasContext(this.canvasId, this);
      //设置线的颜色
      this.content.setStrokeStyle("#333");
      //设置线的宽度
      this.content.setLineWidth(5);
      //设置线两端端点样式更加圆润
      this.content.setLineCap("round");
      //设置两条线连接处更加圆润
      this.content.setLineJoin("round");
    },
    // 画布的触摸移动开始手势响应
    start(e) {
      let point = {};
      if (this.isPseudoHorizontal) {
        point = {
          x: e.touches[0].y,
          y: this.canvasw - e.touches[0].x,
        };
      } else {
        point = {
          x: e.touches[0].x,
          y: e.touches[0].y,
        };
      }
      this.touchs.push(point);
      this.hasDh = true;
    },
    // 画布的触摸移动手势响应
    move(e) {
      let point = {};
      if (this.isPseudoHorizontal) {
        point = {
          x: e.touches[0].y,
          y: this.canvasw - e.touches[0].x,
        };
      } else {
        point = {
          x: e.touches[0].x,
          y: e.touches[0].y,
        };
      }
      this.touchs.push(point);
      if (this.touchs.length >= 2) {
        this.draw(this.touchs);
      }
    },
    // 画布的触摸移动结束手势响应
    end(e) {
      //清空轨迹数组
      for (let i = 0; i < this.touchs.length; i++) {
        this.touchs.pop();
      }
    },
    // 画布的触摸取消响应
    cancel(e) {
      // console.log('触摸取消' + e)
    },
    // 画布的长按手势响应
    tap(e) {
      // console.log('长按手势' + e)
    },
    error(e) {
      // console.log('画布触摸错误' + e)
    },
    //绘制
    draw(touchsVal) {
      let point1 = touchsVal[0];
      let point2 = touchsVal[1];
      this.content.moveTo(point1.x, point1.y);
      this.content.lineTo(point2.x, point2.y);
      this.content.stroke();
      this.content.draw(true);
      touchsVal.shift();
    },
    //清除操作
    clear() {
      if (this.hasSignatureImg) {
        this.hasSignatureImg = false;
      } else {
        //清除画布
        if (this.isPseudoHorizontal) {
          this.content.clearRect(0, 0, this.canvash, this.canvasw);
        } else {
          this.content.clearRect(0, 0, this.canvasw, this.canvash);
        }
        this.content.draw(true);
      }
      this.hasDh = false;
      uni.removeStorageSync("signatureBase64");
    },
    //提交保存
    save() {
      if (!this.hasDh) {
        uni.showToast({ title: "请先签字", icon: "none" });
        return;
      }
      uni.showLoading();
      uni.canvasToTempFilePath(
        {
          canvasId: this.canvasId,
          quality: 1,
          destWidth: 400,
          destHeight: 160,
          success: (res) => {
            let base64 = res.tempFilePath;
            uni.setStorageSync("signatureBase64", base64);
            this.returnBack();
          },
          fail: (err) => {
            uni.hideLoading();
          },
        },
        this
      );
    },
    //返回
    returnBack() {
      let pages = getCurrentPages();
      let prevPage = pages[pages.length - 2];
      prevPage.$refs.form7.handleSignatureCallback();
      // 4. 返回上一页面
      uni.navigateBack({
        delta: 1, // 返回的页面数
      });
    },
  },
};
</script>

<style lang="scss">
.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16rpx;
  view {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica,
      Segoe UI, Arial, Roboto, PingFang SC, miui, Hiragino Sans GB,
      Microsoft Yahei, sans-serif;
  }
  .title-top {
    font-weight: 700;
  }
}
.signature-box {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  .signature-img-box {
    background-color: #f2f2f2;
    border-radius: 10px;
    border: 1px dashed #d8d8d8;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
  .signature-canvas-box {
    // background: url(../../../static/img/signatureArea.png) no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    border: 1px dashed #d8d8d8;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
}
.operation-box {
  padding: 20rpx 60rpx;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  .signature-switch-box {
    color: #333333;
    .signature-switch {
      transform: scale(0.8);
    }
  }
  .btn-box {
    display: flex;
    .btn-item {
      border-radius: 8rpx;
      line-height: 70rpx;
      height: 70rpx;
      width: 160rpx;
      margin-right: 30rpx;
      text-align: center;
      background: #ffffff;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      border: 1px solid #ddd;
      box-sizing: border-box;
      color: #333333;
      &:last-child {
        margin-right: 0;
      }
      &.primary-btn {
        background-color: $uni-color-primary;
        color: #ffffff;
        border-color: $uni-color-primary;
      }
    }
  }
}

@media screen and (orientation: portrait) {
  /*竖屏 css*/
  .header-box,
  .signature-box,
  .operation-box {
    position: absolute;
    width: calc(100vh); /*设置元素的宽为视图窗口高度*/
    transform: rotate(90deg); /*设置元素顺时针旋转正90度*/
    transform-origin: 0 100%; /*设置元素以右上角为原点进行旋转*/
    /*因为元素旋转后宽高是相互调换的，旋转后的元素的宽 =  未旋转时整个视图的高 */
  }
  .header-box {
    top: -44px;
    left: calc(100vw - 44px);
  }
  .signature-box {
    top: calc(-100vw + 44px + 110rpx);
    left: 120rpx;
    height: calc(100vw - 44px - 110rpx);
  }
  .operation-box {
    top: -110rpx;
    left: 0;
  }
}

@media screen and (orientation: landscape) {
  /*横屏 css*/
  .signature-box {
    height: calc(100vh - 44px - 110rpx);
  }
}
</style>
