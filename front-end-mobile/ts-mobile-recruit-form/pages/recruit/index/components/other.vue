<template>
  <view class="children-box" id="OtherPage">
    <view class="title">8、其他情况</view>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    />

    <ts-button
      v-if="!signatureBase64"
      text="手写签名"
      size="normal"
      type="warning"
      @click="handleGoSignature"
    ></ts-button>
    <view class="img-box" v-else>
      <image
        @click="handleGoSignature"
        class="signature-img"
        mode="scaleToFill"
        :src="signatureBase64"
        style="width: 100%; height: 100px"
      />
    </view>
  </view>
</template>

<script>
import BaseForm from "components/base-form/base-form.vue";
import { getDictionaries, fileAttachmentOpenUpload } from "api/data.js";
import common from "util/common.js";

export default {
  components: {
    BaseForm,
  },
  watch: {
    hasSignatureImg: function () {
      this.getCanvase();
    },
    "form.isShoushu": {
      handler(val) {
        let index = this.formList.findIndex(
          (item) => item.prop === "isShoushu"
        );

        if (val === "是") {
          const pengdingArr = this.formList.splice(index + 1);
          this.formList.push({
            title: "请说明",
            prop: "isShoushutext",
            propVal: "isShoushutext",
            placeholder: "请说明",
            type: "text",
            mode: "input",
            maxlength: 25,
            required: true,
          });
          this.formList.push(...pengdingArr);
          this.rules.isShoushutext[0].required = true;
        }
        if (val === "否") {
          const find = this.formList.findIndex(
            (item) => item.prop === "isShoushutext"
          );
          if (find !== -1) {
            this.formList.splice(find, 1);
          }
          this.$set(this.form, 'isShoushutext', '');
          this.rules.isShoushutext[0].required = false;
        }
      },
      immediate: true,
    },
    "form.isChufeng": {
      handler(val) {
        let index = this.formList.findIndex(
          (item) => item.prop === "isChufeng"
        );

        if (val === "是") {
          const pengdingArr = this.formList.splice(index + 1);
          this.formList.push({
            title: "请说明",
            prop: "isChufengtext",
            propVal: "isChufengtext",
            placeholder: "请说明",
            type: "text",
            mode: "input",
            maxlength: 25,
            required: true,
          });
          this.formList.push(...pengdingArr);
          this.rules.isChufengtext[0].required = true;
        }
        if (val === "否") {
          const find = this.formList.findIndex(
            (item) => item.prop === "isChufengtext"
          );
          if (find !== -1) {
            this.formList.splice(find, 1);
          }
          this.$set(this.form, 'isChufengtext', '');
          this.rules.isChufengtext[0].required = false;
        }
      },
      immediate: true,
    },
    "form.jianlilaiyuan": {
      handler(val) {
        let index = this.formList.findIndex(
          (item) => item.prop === "jianlilaiyuan"
        );
        const label = this.jianlilaiyuanList.find(
          (item) => item.value === val
        )?.label;

        if (label === "内部推荐") {
          const pengdingArr = this.formList.splice(index + 1);
          this.formList.push({
            title: "请填写推荐人",
            prop: "referrerName",
            propVal: "referrerName",
            placeholder: "请说明",
            type: "text",
            mode: "input",
            maxlength: 25,
            required: true,
          });
          this.formList.push(...pengdingArr);
          this.rules.referrerName[0].required = true;
        } else {
          const find = this.formList.findIndex(
            (item) => item.prop === "referrerName"
          );
          if (find !== -1) {
            this.formList.splice(find, 1);
          }
          this.$set(this.form, 'referrerName', '');
          this.rules.referrerName[0].required = false;
        }
      },
      immediate: true,
    },
  },
  async created() {
    const res = await getDictionaries("JINALILAIYUAN");
    const find =
      this.formList.find((item) => item?.prop === "jianlilaiyuan") || {};
    find.radioList = res.map((item) => {
      return {
        label: item.itemName,
        value: item.itemNameValue,
      };
    });

    this.jianlilaiyuanList = find.radioList;
  },
  data() {
    return {
      signature: false,
      signatureBase64: "",

      jianlilaiyuanList: [],

      formList: [
        {
          title: "你是否有重大疾病/手术记录？",
          prop: "isShoushu",
          propVal: "isShoushu",
          type: "radio",
          radioList: [
            { label: "是", value: "是" },
            { label: "否", value: "否" },
          ],
          required: true,
          radioCheckWrap: true,
          itemClass: "column",
        },
        // isShoushutext
        {
          title: "你是否受到其它单位记过、察看、开除或其他严重处分？",
          prop: "isChufeng",
          propVal: "isChufeng",
          type: "radio",
          radioList: [
            { label: "是", value: "是" },
            { label: "否", value: "否" },
          ],
          required: true,
          radioCheckWrap: true,
          itemClass: "column",
        },
        // isChufengtext
        {
          title: "你从何处得知本招聘信息？",
          prop: "jianlilaiyuan",
          propVal: "jianlilaiyuan",
          type: "radio",
          radioList: [],
          required: true,
          radioCheckWrap: true,
          itemClass: "column",
        },
        // referrerName
        {
          title: "你是否接受公司以考察你个人素质为目的相关测试（结果保密）?",
          prop: "isXiangguanceshi",
          propVal: "isXiangguanceshi",
          type: "radio",
          radioList: [
            { label: "是", value: "是" },
            { label: "否", value: "否" },
          ],
          required: true,
          radioCheckWrap: true,
          itemClass: "column",
        },
        {
          title: "是否接受工作调配？",
          prop: "isGongzuodaodong",
          propVal: "isGongzuodaodong",
          type: "radio",
          radioList: [
            { label: "是", value: "是" },
            { label: "否", value: "否" },
          ],
          required: true,
          radioCheckWrap: true,
          itemClass: "column",
        },
        {
          title: "附件",
          prop: "fujian",
          imgVal: "fujian",
          type: "file",
          name: "file",
          placeholder: "上传附件",
        },
      ],
      form: {
        isShoushu: "",
        isShoushutext: "",
        isChufeng: "",
        isChufengtext: "",
        jianlilaiyuan: "",
        referrerName: "",
        isXiangguanceshi: "",
        isGongzuodaodong: "",
        fujian: common.guid(),
        signaturePath: "",
      },
      rules: {
        isShoushu: [
          {
            required: true,
            message: "请选择你是否有重大疾病/手术记录？",
            trigger: "",
          },
        ],
        isShoushutext: [
          {
            required: false,
            message: "请说明重大疾病/手术记录",
            trigger: "",
          },
        ],
        isChufeng: [
          {
            required: true,
            message: "请选择你是否受到其它单位记过、察看、开除或其他严重处分？",
            trigger: "",
          },
        ],
        isChufengtext: [
          {
            required: false,
            message: "请说明其它单位记过、察看、开除或其他严重处分",
            trigger: "",
          },
        ],
        jianlilaiyuan: [
          {
            required: true,
            message: "请选择你从何处得知本招聘信息？",
            trigger: "",
          },
        ],
        referrerName: [
          {
            required: false,
            message: "请填写推荐人",
            trigger: "",
          },
        ],
        isXiangguanceshi: [
          {
            required: true,
            message:
              "请选择你是否接受公司以考察你个人素质为目的相关测试（结果保密）?",
            trigger: "",
          },
        ],
        isGongzuodaodong: [
          {
            required: true,
            message: "请选择是否接受工作调配？",
            trigger: "",
          },
        ],
      },
      radioList: [],
      radioList1: [
        {
          label: "是",
          name: "是",
        },
        {
          label: "否",
          name: "否",
        },
      ],
    };
  },
  methods: {
    handleGoSignature() {
      uni.navigateTo({
        url: "/pages/recruit/signature/signature",
      });
    },
    handleSignatureCallback() {
      const signatureBase64 = uni.getStorageSync("signatureBase64");
      this.signatureBase64 = signatureBase64;
    },
    //1,先将base64转换为blob
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    //2,再将blob转换为file
    blobToFile(theBlob, fileName) {
      theBlob.lastModifiedDate = new Date(); // 文件最后的修改日期
      theBlob.name = fileName; // 文件名
      return new File([theBlob], fileName, {
        type: theBlob.type,
        lastModified: Date.now(),
      });
    },
    async submit() {
      try {
        let signaturePath = "";
        if (!this.signatureBase64) {
          uni.showToast({
            title: "请进行个人签名",
            icon: "none",
            duration: 2000,
          });
          throw new Error("请进行个人签名");
        } else {
          let blob = this.dataURLtoBlob(this.signatureBase64);
          let name = "签名.png";
          let file = this.blobToFile(blob, name);

          let data = new FormData();
          data.append("file", file);
          data.append("moduleName", "zp");

          const res = await fileAttachmentOpenUpload(data);
          if (!res.success) {
            uni.showToast({
              title: "个人签名上传失败",
              icon: "none",
              duration: 2000,
            });
          } else {
            signaturePath = res.object.id;
          }
        }

        let v = await this.$refs.baseForm.validate();
        if (this.form.isShoushu === "否") {
          this.$set(this.form, 'isShoushutext', '');
        }
        if (this.form.isChufeng === "否") {
          this.$set(this.form, 'isChufengtext', '');
        }

        const label = this.jianlilaiyuanList.find(
          (item) => item.value === this.form.jianlilaiyuan
        )?.label;
        if (label !== "内部推荐") {
          this.$set(this.form, 'referrerName', '');
        }

        this.form.signaturePath = signaturePath;
        uni.removeStorageSync("signatureBase64");
        return v;
      } catch (error) {}
    },
    handleEcho(otherData) {
      let {
        isShoushu,
        isShoushutext,
        isChufeng,
        isChufengtext,
        jianlilaiyuan,
        referrerName,
        isXiangguanceshi,
        isGongzuodaodong,
        fujian,
      } = otherData;

      this.$set(this.form, "isShoushu", isShoushu);
      this.$set(this.form, "isShoushutext", isShoushutext);
      setTimeout(() => {
        this.$set(this.form, "isChufeng", isChufeng);
        this.$set(this.form, "isChufengtext", isChufengtext);
      }, 300);
      setTimeout(() => {
        this.$set(this.form, "jianlilaiyuan", jianlilaiyuan);
        this.$set(this.form, "referrerName", referrerName);
      }, 500);
      this.$set(this.form, "isXiangguanceshi", isXiangguanceshi);
      this.$set(this.form, "isGongzuodaodong", isGongzuodaodong);
      this.$set(this.form, "fujian", fujian);
    },
  },
};
</script>

<style lang="scss" scoped>
.children-box {
  padding: 8px;
  .bottom-popup {
    ::v-deep .u-popup__content {
      max-height: 100vh !important;
    }
  }
  .img-box {
    border: 1px solid #eee;
    border-radius: 5px;
  }
  ::v-deep .column {
    .u-form-item__body {
      display: flex;
      flex-direction: column !important;
      .u-form-item__body__left {
        width: 100% !important;
      }
      .u-radio-group {
        display: flex;
        flex-direction: column !important;
      }
    }
  }

  ::v-deep {
    .u-radio {
      margin-left: 8px;
    }
  }

  .title {
    font-size: 16px;
    color: #000;
    font-weight: 700;
    margin-bottom: 8px;
  }
}
</style>
