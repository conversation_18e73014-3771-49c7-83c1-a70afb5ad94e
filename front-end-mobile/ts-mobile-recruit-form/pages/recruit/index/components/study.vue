<template>
  <div class="children-box">
    <view class="title">
      <span>2、学习经历（由高到低）</span>
      <ts-button text="新增" size="mini" type="primary" @click="handleAdd"
        >新增</ts-button
      >
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :rules="item.rules"
        :showSubmitButton="false"
      />
      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import { getDictionaries } from "api/data.js";
import BaseUpload from "components/base-upload/base-upload.vue";
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "开始时间",
        prop: "start",
        type: "select",
        mode: "time",
        disabled: true,
		    required: true,
        placeholder: "请选择开始时间",
      },
      {
        title: "结束时间",
        prop: "end",
        type: "select",
        mode: "time",
        disabled: true,
		    required: true,
        placeholder: "请选择结束时间",
      },
      {
        title: "毕业院校",
        prop: "biyeyuanxiao",
        propVal: "biyeyuanxiao",
        placeholder: "请输入毕业院校",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
      {
        title: "专业",
        prop: "zhuanye",
        propVal: "zhuanye",
        placeholder: "请输入专业",
        type: "text",
        mode: "input",
        maxlength: 10,
        required: true,
      },
      {
        title: "学历",
        prop: "xueliLabel",
        propVal: "xueliLabel",
        placeholder: "请选择学历",
        type: "select",
        mode: "select",
        disabled: true,
        elementProps: {
          keyName: "itemName",
        },
        handleConfirm: ({ value = [] }) => {
          this.handleEducationConfirm(value, id);
        },
        optionList: [this.educationList],
      },
      {
        title: "是否全日制",
        prop: "fullTime",
        propVal: "fullTime",
        type: "select",
        mode: "select",
        disabled: true,
        placeholder: "请选择",
        elementProps: {
          keyName: "itemName",
        },
        handleConfirm: ({ value = [] }) => {
          this.handleFullTimeConfirm(value, id);
        },
        optionList: [
          [
            {
              itemNameValue: "是",
              itemName: "是",
            },
            {
              itemNameValue: "否",
              itemName: "否",
            },
          ],
        ],
      },
      {
        title: "附件",
        prop: "fujian",
        imgVal: "fujian",
        type: "file",
        name: "file",
        placeholder: "上传附件",
      },
    ],
    form: {
      start: "",
      end: "",
      biyeyuanxiao: "",
      zhuanye: "",
      xueli: "",
      xueliLabel: "",
      fullTime: "",
      fujian: id,
    },
    rules: {
      start: [
        {
          required: true,
          message: "请填写开始时间",
          trigger: "",
        },
      ],
      end: [
        {
          required: true,
          message: "请填写结束时间",
          trigger: "",
        },
      ],
      biyeyuanxiao: [
        {
          required: true,
          message: "请填写毕业院校",
          trigger: "",
        },
      ],
      zhuanye: [
        {
          required: true,
          message: "请填写专业",
          trigger: "",
        },
      ],
    },
  };
}

export default {
  components: {
    BaseForm,
    BaseUpload,
  },
  data() {
    return {
      list: [],
      educationList: [],
      returnItem,
    };
  },
  async created() {
    await this.handleGetDictionaries();
    this.handleAdd();
  },
  methods: {
    async handleGetDictionaries() {
      const res = await getDictionaries("education_type");
      if (res) {
        this.educationList = res || [];
      }
    },
    handleEducationConfirm(value = [], id) {
      let valueList = value.filter((item) => item),
        { itemName, itemNameValue } = valueList[valueList.length - 1] || {};

      const index = this.list.findIndex((item) => item.id === id);
      if (index !== -1) {
        this.$set(this.list[index]["form"], "xueli", itemNameValue);
        this.$set(this.list[index]["form"], "xueliLabel", itemName);
      }
    },
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    handleFullTimeConfirm(value = [], id) {
      let valueList = value.filter((item) => item),
        { itemNameValue } = valueList[valueList.length - 1] || {};

      const index = this.list.findIndex((item) => item.id === id);
      if (index !== -1) {
        this.$set(this.list[index]["form"], "fullTime", itemNameValue);
      }
    },
    async submit() {
      let result = [];
      for (var i = 0; i < this.list.length; i++) {
        let v = await this.$refs[`baseForm${i}`][0].validate();
        result.push(v);
      }

      if (result.every((item) => item)) {
        return this.list.map((item) => item.form);
      } else {
        return false;
      }
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();
        let [start = "", end = ""] =
          item.qizhishijian && item.qizhishijian.split("~");
        item.start = start;
        item.end = end;
        delete item.qizhishijian;

        if (item.xueli) {
          item.xueliLabel = this.educationList.filter(
            ({ itemNameValue }) => itemNameValue == item.xueli
          )[0].itemName;
        }

        renderListItem.form = item;

        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss">
.children-box {
  padding: 8px 0;
  width: 100%;
  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
