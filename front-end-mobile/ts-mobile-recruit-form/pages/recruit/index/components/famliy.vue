<template>
  <div class="children-box">
    <view class="title">
      <span>7、家庭关系</span>
      <ts-button text="新增" size="mini" type="primary" @click="handleAdd"
        >新增</ts-button
      >
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :rules="item.rules"
        :showSubmitButton="false"
      />
      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import { getDictionaries } from "api/data.js";
import BaseUpload from "components/base-upload/base-upload.vue";
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "关系",
        prop: "guanxiLabel",
        propVal: "guanxiLabel",
        placeholder: "请选择关系",
        type: "select",
        mode: "select",
        disabled: true,
        elementProps: {
          keyName: "itemName",
        },
        handleConfirm: ({ value = [] }) => {
          this.handleChangePersonnelList(value, id);
        },
        optionList: [this.personnelList],
        required: true,
      },
      {
        title: "姓名",
        prop: "xingming",
        propVal: "xingming",
        placeholder: "请输入姓名",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
      {
        title: "出生日期",
        prop: "chushengriqi",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择出生日期",
      },
      {
        title: "工作单位",
        prop: "gongzuodanwei",
        propVal: "gongzuodanwei",
        placeholder: "请输入工作单位",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "联系方式",
        prop: "phone",
        propVal: "phone",
        placeholder: "请输入联系方式",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
    ],
    form: {
      guanxiLabel: "",
      guanxi: "",
      xingming: "",
      chushengriqi: "",
      gongzuodanwei: "",
      phone: "",
    },
    rules: {
      guanxiLabel: [
        {
          required: true,
          message: "请选择关系",
          trigger: "",
        },
      ],
      xingming: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "",
        },
      ],
      phone: [
        {
          required: true,
          validator: (rule, value, callback) => {
            return uni.$u.test.mobile(value);
          },
          message: "请输入正确的联系方式",
          trigger: "",
        },
      ],
    },
  };
}

export default {
  components: {
    BaseForm,
    BaseUpload,
  },
  data() {
    return {
      list: [],
      personnelList: [],
      returnItem,
    };
  },
  async created() {
    await this.handleGetDictionaries();
    this.handleAdd();
  },
  methods: {
    async handleGetDictionaries() {
      const res = await getDictionaries("personnel_relationship");
      if (res) {
        this.personnelList = res || [];
      }
    },
    handleChangePersonnelList(value = [], id) {
      let valueList = value.filter((item) => item),
        { itemName, itemNameValue } = valueList[valueList.length - 1] || {};

      const index = this.list.findIndex((item) => item.id === id);
      if (index !== -1) {
        this.$set(this.list[index]["form"], "guanxi", itemNameValue);
        this.$set(this.list[index]["form"], "guanxiLabel", itemName);
      }
    },
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    async submit() {
      let result = [];
      for (var i = 0; i < this.list.length; i++) {
        let v = await this.$refs[`baseForm${i}`][0].validate();
        result.push(v);
      }

      if (result.every((item) => item)) {
        return this.list.map((item) => item.form);
      } else {
        return false;
      }
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();

        if (item.guanxi) {
          item.guanxiLabel = this.personnelList.filter(
            ({ itemNameValue }) => itemNameValue == item.guanxi
          )[0].itemName;
        }

        renderListItem.form = item;
        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss">
.children-box {
  padding: 8px 0;
  width: 100%;
  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
