export default {
  data() {
    return {
      rules: {
        avatar: [
          {
            required: true,
            message: "请上传头像",
            trigger: "",
          },
        ],
        applicantPost: [
          {
            required: true,
            message: "请选择应聘岗位",
            trigger: "",
          },
        ],
        employeeName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "",
          },
        ],
        genderLabel: [
          {
            required: true,
            message: "请选择性别",
            trigger: "",
          },
        ],
        identityNumber: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.idCard(value);
            },
            message: "请输入正确身份证号码",
            trigger: "",
          },
        ],
        age: [
          {
            required: true,
            message: "请输入年龄",
            trigger: "",
          },
          {
            validator: (rule, value, callback) => {
              return Number(value) < 80;
            },
            message: "年龄不得超过80岁",
            trigger: "",
          },
        ],
        iphone: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "请输入正确的联系方式",
            trigger: "",
          },
        ],
        personnelCategoryLabel: [
          {
            required: true,
            message: "请选择人员类别",
            trigger: "",
          },
        ],
        waiyudengjiLabel: [
          {
            required: true,
            message: "请选择外语等级",
            trigger: "",
          },
        ],
        entryDate: [
          {
            required: true,
            message: "请选择参加工作时间",
            trigger: "",
          },
        ],
        shenggao: [
          {
            required: true,
            message: "请输入身高",
            trigger: "",
          },
        ],
        tizhong: [
          {
            required: true,
            message: "请输入体重",
            trigger: "",
          },
        ],
        nationalityLabel: [
          {
            required: true,
            message: "请选择民族",
            trigger: "",
          },
        ],
        drivingLicence: [
          {
            required: true,
            message: "请选择是否有驾照",
            trigger: "",
          },
        ],
        drivingLicenceLevelLabel: [
          {
            required: true,
            message: "请选择驾驶证级别",
            trigger: "",
          },
        ],
        zhiyezhuangtai: [
          {
            required: true,
            message: "请选择目前职业状态",
            trigger: "",
          },
        ],
        qiwangyuexin: [
          {
            required: true,
            message: "请输入期望月薪",
            trigger: "",
          },
        ],
        zuikuairuzhi: [
          {
            required: true,
            message: "请选择最快入职日期",
            trigger: "",
          },
        ],
        birthplace: [
          {
            required: true,
            message: "请选择籍贯",
            trigger: "",
          },
        ],
        hujileixingLabel: [
          {
            required: true,
            message: "请选择户籍类型",
            trigger: "",
          },
        ],
        marriageStatus: [
          {
            required: true,
            message: "请选择婚育状况",
            trigger: "",
          },
        ],
        offspring: [
          {
            required: true,
            message: "请输入子女",
            trigger: "",
          },
        ],
        hukousuozaidi: [
          {
            required: true,
            message: "请选择户口所在地",
            trigger: "",
          },
        ],
        hukousuozaiditext: [
          {
            required: true,
            message: "请输入户口所在地详细地址",
            trigger: "",
          },
        ],
        address: [
          {
            required: true,
            message: "请选择户口所在地",
            trigger: "",
          },
        ],
        addresstext: [
          {
            required: true,
            message: "请输入通信地址详细",
            trigger: "",
          },
        ],
        politicalStatusLabel: [
          {
            required: true,
            message: "请选择政治面貌",
            trigger: "",
          },
        ],
        jinjilianxiren: [
          {
            required: true,
            message: "请输入紧急联络人",
            trigger: "",
          },
        ],
        renxirendianhua: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "请输入正确的紧急联络人电话",
            trigger: "",
          },
        ],
        email: [
          {
            required: true,
            validator: (rule, value, callback) => {
              return uni.$u.test.email(value);
            },
            message: "请输入正确的Email",
            trigger: "",
          },
        ],
        gerenzhuanchang: [
          {
            required: true,
            message: "请输入个人专长/业余爱好",
            trigger: "",
          },
        ],
        zaixiaohuojiang: [
          {
            required: true,
            message: "请输入在校期间获奖情况",
            trigger: "",
          },
        ],
        ziwopingjia: [
          {
            required: true,
            message: "请输入自我评价及业务擅长",
            trigger: "",
          },
        ],
      },
    };
  },
};
