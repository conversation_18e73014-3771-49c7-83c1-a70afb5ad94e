<template>
  <div class="children-box">
    <view class="title">
      <span>5、进修培训经历</span>
      <ts-button text="新增" size="mini" type="primary" @click="handleAdd"
        >新增</ts-button
      >
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :showSubmitButton="false"
      />
      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import BaseUpload from "components/base-upload/base-upload.vue";
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "开始时间",
        prop: "start",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择开始时间",
      },
      {
        title: "结束时间",
        prop: "end",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择结束时间",
      },
      {
        title: "培训项目",
        prop: "peixunxiangmu",
        propVal: "peixunxiangmu",
        placeholder: "请输入培训项目",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "培训机构/授课老师",
        prop: "peixunjigou",
        propVal: "peixunjigou",
        placeholder: "请输入培训机构/授课老师",
        type: "text",
        mode: "input",
        maxlength: 30,
      },
      {
        title: "取得职业资格或认证",
        prop: "zhiyezige",
        propVal: "zhiyezige",
        placeholder: "请输入取得职业资格或认证",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "附件",
        prop: "fujian",
        imgVal: "fujian",
        type: "file",
        name: "file",
        placeholder: "上传附件",
      },
    ],
    form: {
      start: "",
      end: "",
      peixunxiangmu: "",
      peixunjigou: "",
      laoshi: "",
      zhiyezige: "",
      fujian: id,
    },
  };
}

export default {
  components: {
    BaseForm,
    BaseUpload,
  },
  created() {
    this.handleAdd();
  },
  data() {
    return {
      list: [],
      returnItem,
    };
  },
  methods: {
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    async submit() {
      return this.list.map((item) => item.form);
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();
        let [start = "", end = ""] =
          item.qizhishijian && item.qizhishijian.split("~");
        item.start = start;
        item.end = end;
        delete item.qizhishijian;

        renderListItem.form = item;
        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss">
.children-box {
  padding: 8px 0;
  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
