export default {
  data() {
    return {
      getAgeFromIdCard: (idCard) => {
        var birthday = idCard.substr(6, 8);
        var year = birthday.substr(0, 4);
        var month = birthday.substr(4, 2);
        var day = birthday.substr(6, 2);
        var age = new Date().getFullYear() - parseInt(year);

        // 如果当前月份小于出生月份，或者当前月份等于出生月份但是当前日期小于出生日期，则年龄减一
        if (
          new Date().getMonth() + 1 < parseInt(month) ||
          (new Date().getMonth() + 1 === parseInt(month) &&
            new Date().getDate() < parseInt(day))
        ) {
          age--;
        }

        return age;
      },
      formList: [
        {
          title: "身份证号码",
          rightSlot: true,
          prop: "identityNumber",
          propVal: "identityNumber",
          labelWidth: "80",
          itemClass: "all-width",
          maxlength: 18,
          required: true,
        },
        {
          title: "个人头像",
          rightSlot: true,
          prop: "avatar",
          propVal: "avatar",
          labelWidth: "80",
          itemClass: "all-width",
          required: true,
        },
        {
          title: "应聘岗位",
          rightSlot: true,
          prop: "applicantPost",
          propVal: "applicantPost",
          labelWidth: "80",
          maxlength: 50,
          itemClass: "all-width",
          required: true,
        },
        {
          title: "人员类别",
          prop: "personnelCategoryLabel",
          propVal: "personnelCategoryLabel",
          placeholder: "请选择",
          type: "select",
          mode: "select",
          disabled: true,
          optionList: [],
          required: true,
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "personnelCategoryLabel", itemName);
            this.$set(this.form, "personnelCategory", itemNameValue);
          },
        },
        {
          title: "姓名",
          prop: "employeeName",
          propVal: "employeeName",
          placeholder: "请输入",
          type: "text",
          mode: "input",
          maxlength: 10,
          required: true,
        },
        {
          title: "性别",
          prop: "genderLabel",
          propVal: "genderLabel",
          type: "select",
          mode: "select",
          disabled: true,
          placeholder: "请选择",
          optionList: [],
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "genderLabel", itemName);
            this.$set(this.form, "gender", itemNameValue);
          },
          required: true,
        },
        {
          title: "联系方式",
          prop: "iphone",
          propVal: "iphone",
          placeholder: "请输入",
          type: "number",
          mode: "input",
          maxlength: 11,
          required: true,
        },
        {
          title: "年龄",
          prop: "age",
          propVal: "age",
          placeholder: "请输入",
          type: "number",
          mode: "input",
          maxlength: 2,
          required: true,
          callback: (e) => e?.replace(/\D/g, ""),
        },
        {
          title: "外语等级",
          prop: "waiyudengjiLabel",
          propVal: "waiyudengjiLabel",
          placeholder: "请选择",
          type: "select",
          mode: "select",
          disabled: true,
          optionList: [],
          required: true,
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "waiyudengjiLabel", itemName);
            this.$set(this.form, "waiyudengji", itemNameValue);
          },
        },
        {
          title: "参加工作时间",
          prop: "entryDate",
          type: "select",
          mode: "time",
          disabled: true,
          placeholder: "请选择参加工作时间",
          required: true,
        },
        {
          title: "身高",
          prop: "shenggao",
          propVal: "shenggao",
          placeholder: "请输入(cm)",
          type: "number",
          mode: "input",
          maxlength: 5,
          required: true,
        },
        {
          title: "体重",
          prop: "tizhong",
          propVal: "tizhong",
          placeholder: "请输入(kg)",
          type: "number",
          mode: "input",
          maxlength: 5,
          required: true,
        },
        {
          title: "民族",
          prop: "nationalityLabel",
          propVal: "nationalityLabel",
          placeholder: "请选择民族",
          type: "select",
          mode: "select",
          disabled: true,
          required: true,
          optionList: [],
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "nationalityLabel", itemName);
            this.$set(this.form, "nationality", itemNameValue);
          },
        },
        {
          title: "是否有驾照",
          prop: "drivingLicence",
          type: "radio",
          placeholder: "",
          radioCheckWrap: false,
          required: true,
          radioList: [
            {
              label: "是",
              value: "是",
            },
            {
              label: "否",
              value: "否",
            },
          ],
          callback: (e) => {
            let isRequired = e === "是";
            this.$set(
              this.rules.drivingLicenceLevelLabel[0],
              "required",
              isRequired
            );
            this.formList.filter(
              (item) => item.prop === "drivingLicenceLevelLabel"
            )[0].required = isRequired;

            if (e === "否") {
              this.$set(this.form, 'drivingLicenceLevelLabel', '');
              this.$set(this.form, 'drivingLicenceLevel', '');
            }
            this.$forceUpdate();
          },
        },
        {
          title: "驾驶证级别",
          prop: "drivingLicenceLevelLabel",
          propVal: "drivingLicenceLevelLabel",
          placeholder: "请选择驾驶证级别",
          type: "select",
          required: true,
          mode: "select",
          disabled: true,
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "drivingLicenceLevelLabel", itemName);
            this.$set(this.form, "drivingLicenceLevel", itemNameValue);
          },
          optionList: [],
        },
        {
          title: "目前职业状态",
          prop: "zhiyezhuangtai",
          type: "radio",
          placeholder: "",
          itemClass: "radio-width",
          radioCheckWrap: false,
          required: true,
          radioList: [
            {
              label: "在职",
              value: "在职",
            },
            {
              label: "离职",
              value: "离职",
            },
            {
              label: "待业",
              value: "待业",
            },
            {
              label: "退休",
              value: "退休",
            },
            {
              label: "应届生",
              value: "应届生",
            },
          ],
        },
        {
          title: "期望月薪",
          prop: "qiwangyuexin",
          propVal: "qiwangyuexin",
          placeholder: "请输入(元)",
          type: "number",
          mode: "input",
          required: true,
          maxlength: 8,
          callback: (e) => e?.replace(/\D/g, ""),
        },
        {
          title: "最快入职日期",
          prop: "zuikuairuzhi",
          type: "select",
          mode: "time",
          disabled: true,
          required: true,
          placeholder: "请选择最快入职日期",
        },
        {
          title: "籍贯",
          prop: "birthplace",
          propVal: "birthplace",
          rightSlot: true,
          required: true,
        },
        {
          title: "户籍类型",
          prop: "hujileixingLabel",
          propVal: "hujileixingLabel",
          placeholder: "请选择户籍类型",
          type: "select",
          mode: "select",
          required: true,
          disabled: true,
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "hujileixingLabel", itemName);
            this.$set(this.form, "hujileixing", itemNameValue);
          },
          optionList: [],
        },
        {
          title: "婚育状况",
          prop: "marriageStatus",
          labelWidth: "100px",
          type: "radio",
          placeholder: "",
          itemClass: "radio-width",
          radioCheckWrap: true,
          required: true,
          radioList: [],
        },
        {
          title: "子女",
          prop: "offspring",
          propVal: "offspring",
          placeholder: "请输入(个)",
          type: "number",
          mode: "input",
          required: true,
          maxlength: 1,
        },
        {
          title: "户口所在地",
          prop: "area1",
          propVal: "area1",
          required: true,
          rightSlot: true,
        },
        {
          title: "所在地详细",
          prop: "hukousuozaiditext",
          propVal: "hukousuozaiditext",
          placeholder: "户口所在地详细地址",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "通信地址",
          prop: "area2",
          propVal: "area2",
          required: true,
          rightSlot: true,
        },
        {
          title: "通信地址详细",
          prop: "addresstext",
          propVal: "addresstext",
          placeholder: "请输入通信地址详细",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "政治面貌",
          prop: "politicalStatusLabel",
          propVal: "politicalStatusLabel",
          placeholder: "请选择政治面貌",
          type: "select",
          mode: "select",
          required: true,
          disabled: true,
          elementProps: {
            keyName: "itemName",
          },
          handleConfirm: ({ value: [{ itemName, itemNameValue }] }) => {
            this.$set(this.form, "politicalStatusLabel", itemName);
            this.$set(this.form, "politicalStatus", itemNameValue);
          },
          optionList: [],
        },
        {
          title: "紧急联络人",
          prop: "jinjilianxiren",
          propVal: "jinjilianxiren",
          placeholder: "请输入紧急联络人",
          type: "text",
          mode: "input",
          maxlength: 10,
          required: true,
        },
        {
          title: "紧急联络人电话",
          prop: "renxirendianhua",
          propVal: "renxirendianhua",
          placeholder: "请输入紧急联络人电话",
          type: "number",
          mode: "input",
          maxlength: 20,
          required: true,
        },
        {
          title: "Email",
          prop: "email",
          propVal: "email",
          placeholder: "请输入Email",
          type: "text",
          mode: "input",
          maxlength: 50,
          required: true,
        },
        {
          title: "个人专长/业余爱好",
          prop: "gerenzhuanchang",
          propVal: "gerenzhuanchang",
          placeholder: "请输入个人专长/业余爱好",
          type: "textarea",
          elementProps: { maxlength: 150 },
          required: true,
        },
        {
          title: "在校期间获奖情况",
          prop: "zaixiaohuojiang",
          propVal: "zaixiaohuojiang",
          placeholder: "请输入在校期间获奖情况",
          type: "textarea",
          elementProps: { maxlength: 150 },
          required: true,
        },
        {
          title: "自我评价及业务擅长",
          prop: "ziwopingjia",
          propVal: "ziwopingjia",
          placeholder: "请输入自我评价及业务擅长",
          type: "textarea",
          elementProps: { maxlength: 150 },
          required: true,
        },
      ],
    };
  },
};
