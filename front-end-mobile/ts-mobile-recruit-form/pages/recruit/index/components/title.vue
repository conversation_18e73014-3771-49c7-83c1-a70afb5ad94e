<template>
  <div class="children-box">
    <view class="title">
      <span>6、职称晋升（由高到低）</span>
      <ts-button
        text="新增"
        size="mini"
        type="primary"
        @click="handleAdd"
      ></ts-button>
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :rules="item.rules"
        :showSubmitButton="false"
      />

      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import { getTitle } from "api/data.js";
import BaseUpload from "components/base-upload/base-upload.vue";
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "职称",
        prop: "zhichengmingchengLabel",
        propVal: "zhichengmingchengLabel",
        type: "select",
        mode: "select",
        disabled: true,
        placeholder: "请选择",
        elementProps: {
          keyName: "itemName",
        },
        handleConfirm: ({ value = [] }) => {
          this.handleFullTimeConfirm(value, id);
        },
        optionList: [this.titleList],
        required: true,
      },
      {
        title: "职称的专业",
        prop: "zhuenye",
        propVal: "zhuenye",
        placeholder: "请输入职称的专业",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "取得时间",
        prop: "qudeshijian",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择取得时间",
        required: true,
      },
      {
        title: "职称取得地点",
        prop: "qudedidian",
        propVal: "qudedidian",
        placeholder: "请输入职称取得地点",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "附件",
        prop: "fujian",
        imgVal: "fujian",
        type: "file",
        name: "file",
        placeholder: "上传附件",
      },
    ],
    form: {
      zhichengmingchengLabel: "",
      zhichengmingcheng: "",
      zhuenye: "",
      qudeshijian: "",
      qudedidian: "",
      fujian: id,
    },
    rules: {
      zhichengmingchengLabel: [
        {
          required: true,
          message: "请选择职称",
          trigger: "",
        },
      ],
      qudeshijian: [
        {
          required: true,
          message: "请选择取得时间",
          trigger: "",
        },
      ],
    },
  };
}
export default {
  components: {
    BaseForm,
    BaseUpload,
  },
  async created() {
    await this.handleGetDictionaries();
    this.handleAdd();
  },
  data() {
    return {
      list: [],
      titleList: [],
      returnItem,
    };
  },
  methods: {
    async handleGetDictionaries() {
      const res = await getTitle();
      if (res.success) {
        this.titleList = (res.object || []).map((item) => {
          return {
            itemName: item.jobtitleName,
            itemNameValue: item.jobtitleId,
          };
        });
      }
    },
    handleFullTimeConfirm(value = [], id) {
      let valueList = value.filter((item) => item),
        { itemName, itemNameValue } = valueList[valueList.length - 1] || {};

      const index = this.list.findIndex((item) => item.id === id);
      if (index !== -1) {
        this.$set(this.list[index]["form"], "zhichengmingcheng", itemNameValue);
        this.$set(this.list[index]["form"], "zhichengmingchengLabel", itemName);
      }
    },
    async submit() {
      let result = [];
      for (var i = 0; i < this.list.length; i++) {
        let v = await this.$refs[`baseForm${i}`][0].validate();
        result.push(v);
      }

      if (result.every((item) => item)) {
        return this.list.map((item) => item.form);
      } else {
        return false;
      }
    },
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();
        if (item.zhichengmingcheng) {
          item.zhichengmingchengLabel = this.titleList.filter(
            ({ itemNameValue }) => itemNameValue == item.zhichengmingcheng
          )[0].itemName;
        }

        renderListItem.form = item;
        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss">
.children-box {
  padding: 8px 0;
  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
