<template>
  <div class="children-box">
    <view class="title">
      <span>4、学术团体任职情况</span>
      <ts-button text="新增" size="mini" type="primary" @click="handleAdd"
        >新增</ts-button
      >
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :showSubmitButton="false"
      >
      </base-form>

      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "开始时间",
        prop: "start",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择开始时间",
      },
      {
        title: "结束时间",
        prop: "end",
        type: "select",
        mode: "time",
        disabled: true,
        placeholder: "请选择结束时间",
      },
      {
        title: "学术团体或专业杂志名称",
        prop: "xueshituanti",
        propVal: "xueshituanti",
        placeholder: "请输入学术团体或专业杂志名称",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "职务",
        prop: "zhiwu",
        propVal: "zhiwu",
        placeholder: "请输入职务",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "证明人及电话",
        prop: "zhengmingren",
        propVal: "zhengmingren",
        placeholder: "请输入证明人及电话",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
    ],
    form: {
      start: "",
      end: "",
      xueshituanti: "",
      zhiwu: "",
      zhengmingren: "",
    },
  };
}

export default {
  components: {
    BaseForm,
  },
  data() {
    return {
      list: [],
      returnItem,
    };
  },
  created() {
    this.handleAdd();
  },
  methods: {
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    async submit() {
      return this.list.map((item) => item.form);
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();
        let [start = "", end = ""] =
          item.qizhishijian && item.qizhishijian.split("~");
        item.start = start;
        item.end = end;
        delete item.qizhishijian;

        renderListItem.form = item;
        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.children-box {
  padding: 8px 0;
  width: 100%;
  /deep/ .u-form-item__body__right__content {
    width: 100%;

    .item__body__right__content__icon {
      width: 100%;
    }
  }

  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
