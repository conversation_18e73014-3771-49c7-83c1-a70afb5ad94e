<template>
  <div class="children-box">
    <view class="title">1、基础信息</view>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    >
      <template slot="avatar">
        <upload-avatar ref="UploadAvatar" v-model="form.avatar" />
      </template>
      <template slot="applicantPost">
        <ts-input
          class="select-input-contaier"
          v-model="form.interviewPath"
          placeholder="请选择"
          type="text"
          readonly
          :border="$formInputBorder"
          input-align="right"
          @click.native="handleClickTree('post')"
        />
      </template>
      <template slot="birthplace">
        <ts-input
          class="select-input-contaier"
          v-model="form.birthplace"
          placeholder="请选择"
          type="text"
          readonly
          :border="$formInputBorder"
          input-align="right"
          @click.native="handleClickTree('birthplace')"
        ></ts-input>
      </template>

      <template slot="identityNumber">
        <view class="id-card-container">
          <ts-input
            class="select-input-contaier"
            v-model="form.identityNumber"
            placeholder="请输入身份证"
            type="text"
            readonly
            :border="$formInputBorder"
            input-align="right"
            @click.native="handleClickIdentityNumber()"
          ></ts-input>

          <ts-button
            class="validate-btn"
            text="查询"
            size="success"
            type="mini"
            @click="handleVaildateIdCard"
          />
        </view>
      </template>

      <template slot="area1">
        <ts-input
          class="select-input-contaier"
          v-model="form.hukousuozaidi"
          placeholder="请选择"
          type="text"
          readonly
          :border="$formInputBorder"
          input-align="right"
          @click.native="handleOpenSelectArea('hukousuozaidicode')"
        />
      </template>

      <template slot="area2">
        <ts-input
          class="select-input-contaier"
          v-model="form.address"
          placeholder="请选择"
          type="text"
          readonly
          :border="$formInputBorder"
          input-align="right"
          @click.native="handleOpenSelectArea('addresscode')"
        />
      </template>
    </base-form>

    <dialog-select-tree
      v-model="dialogSelectTree"
      :eachValue="eachValue"
      :treeList="range"
      @submit="handleTreeSubmit"
    />

    <keyboard-package
      :type="2"
      @input="inputVal"
      :safeAreaInsetBottom="true"
      ref="KeyboardPackage"
    />

    <!-- 地区选择 -->
    <ts-cascader
      v-model="regionSelectShow"
      :list="cityDatas"
      :default-value="regionDefault"
      mode="mutil-column-auto"
      @confirm="regionSelectConfirm"
    />
  </div>
</template>

<script>
import Rules from "./basis-form-rules";
import FormItem from "./basis-form-item";
import BaseForm from "components/base-form/base-form.vue";
import UploadAvatar from "components/upload-avatar/upload-avatar.vue";
import KeyboardPackage from "components/keyboard-package/keyboard-package.vue";
import DialogSelectTree from "components/dialog-select-tree/index.vue";
import common from "util/common.js";

import { cityDatas } from "config/cityDatas.js";
import { city } from "config/area.js";
import {
  getHrRecruitmentTalentPoolTree,
  getDictionaries,
  findIdentityNumber,
} from "api/data.js";

export default {
  mixins: [Rules, FormItem],
  components: {
    BaseForm,
    UploadAvatar,
    KeyboardPackage,
    DialogSelectTree,
  },
  created() {
    this.handleGetHrRecruitmentTalentPoolTree();
    this.handleSecondArea();
    this.handleGetDictionaries();
  },
  data() {
    return {
      form: {
        avatar: "",
        applicantPost: "",
        applicantPosttext: "",
        interviewPath: "",
        employeeName: "",

        personnelCategory: "",
        personnelCategoryLabel: "",

        gender: "",
        genderLabel: "",

        iphone: "",
        identityNumber: "",
        age: "",

        waiyudengji: "",
        waiyudengjiLabel: "",
        entryDate: "",

        shenggao: "",
        tizhong: "",

        nationality: "",
        nationalityLabel: "",

        drivingLicence: "",
        drivingLicenceLevel: "",
        drivingLicenceLevelLabel: "",
        zhiyezhuangtai: "",
        qiwangyuexin: "",
        zuikuairuzhi: "",
        birthplace: "",

        hujileixing: "",
        hujileixingLabel: "",
        marriageStatus: "",
        offspring: "",

        hukousuozaidi: "",
        hukousuozaidicode: "",
        hukousuozaiditext: "",
        address: "",
        addresscode: "",
        addresstext: "",

        politicalStatus: "",
        politicalStatusLabel: "",
        jinjilianxiren: "",
        renxirendianhua: "",
        email: "",
        gerenzhuanchang: "",
        zaixiaohuojiang: "",
        ziwopingjia: "",

        birthplaceId: "",
      },

      regionSelectShow: false,
      cityDatas,
      regionDefault: [],
      setKey: "",

      range: [],
      eachValue: {},
      postTreeData: [],

      city,
      dialogSelectTree: false,
    };
  },
  watch: {
    "form.identityNumber"(val) {
      if (uni.$u.test.idCard(val)) {
        this.$nextTick(() => {
          this.$set(this.form, "age", String(this.getAgeFromIdCard(val)));
        });
      } else {
        this.$set(this.form, "age", "");
      }
    },
  },
  methods: {
    async handleVaildateIdCard() {
      let { identityNumber } = this.form;
      let isVaildateIdCard = uni.$u.test.idCard(identityNumber);

      if (!isVaildateIdCard) {
        uni.showToast({
          title: "请输入正确的身份证进行验证",
          icon: "none",
          duration: 2000,
        });
        return false;
      }

      const res = await findIdentityNumber({ identityNumber });
      if (res.statusCode === 200 && res.success) {
        if (res.object === null) {
          uni.showToast({
            title: "未查询到登记信息,请填写",
            icon: "none",
            duration: 2000,
          });
          return false;
        }
        this.$refs.UploadAvatar.imageUrl = "";

        this.$set(this.form, "id", "");
        this.$set(this.form, "avatar", "");
        this.$set(this.form, "applicantPost", "");
        this.$set(this.form, "applicantPosttext", "");
        this.$set(this.form, "interviewPath", "");
        this.$set(this.form, "employeeName", "");
        this.$set(this.form, "gender", "");
        this.$set(this.form, "genderLabel", "");
        this.$set(this.form, "iphone", "");
        this.$set(this.form, "personnelCategory", "");
        this.$set(this.form, "personnelCategoryLabel", "");
        this.$set(this.form, "waiyudengji", "");
        this.$set(this.form, "waiyudengjiLabel", "");
        this.$set(this.form, "entryDate", "");
        this.$set(this.form, "shenggao", "");
        this.$set(this.form, "tizhong", "");
        this.$set(this.form, "nationality", "");
        this.$set(this.form, "nationalityLabel", "");
        this.$set(this.form, "drivingLicence", "");
        this.$set(this.form, "drivingLicenceLevel", "");
        this.$set(this.form, "drivingLicenceLevelLabel", "");
        this.$set(this.form, "zhiyezhuangtai", "");
        this.$set(this.form, "qiwangyuexin", "");
        this.$set(this.form, "zuikuairuzhi", "");
        this.$set(this.form, "birthplace", "");
        this.$set(this.form, "hujileixing", "");
        this.$set(this.form, "hujileixingLabel", "");
        this.$set(this.form, "marriageStatus", "");
        this.$set(this.form, "offspring", "");
        this.$set(this.form, "hukousuozaidi", "");
        this.$set(this.form, "hukousuozaidicode", "");
        this.$set(this.form, "hukousuozaiditext", "");
        this.$set(this.form, "address", "");
        this.$set(this.form, "addresscode", "");
        this.$set(this.form, "addresstext", "");
        this.$set(this.form, "politicalStatus", "");
        this.$set(this.form, "politicalStatusLabel", "");
        this.$set(this.form, "jinjilianxiren", "");
        this.$set(this.form, "renxirendianhua", "");
        this.$set(this.form, "email", "");
        this.$set(this.form, "gerenzhuanchang", "");
        this.$set(this.form, "zaixiaohuojiang", "");
        this.$set(this.form, "ziwopingjia", "");
        this.$emit("handleQueryRegistrationInfo", {
          hrmsEducationInfo: [],
          hrmsZpglWorkrecord: [],
          hrmsZpglAcademy: [],
          hrmsZpglContinueLearning: [],
          hrmsZpglProfessional: [],
          hrmsZpglFamily: [],
          otherData: {},
        });
        let {
          id,
          avatar,
          applicantPost,
          applicantPosttext,
          interviewPath,
          employeeName,
          personnelCategory,
          personnelCategoryLabel,
          gender,
          genderLabel,
          iphone,
          identityNumber,
          age,
          waiyudengji,
          waiyudengjiLabel,
          entryDate,
          shenggao,
          tizhong,
          nationality,
          nationalityLabel,
          drivingLicence,
          drivingLicenceLevel,
          drivingLicenceLevelLabel,
          zhiyezhuangtai,
          qiwangyuexin,
          zuikuairuzhi,
          birthplace,
          hujileixing,
          hujileixingLabel,
          marriageStatus,
          offspring,
          hukousuozaidi,
          hukousuozaidicode,
          hukousuozaiditext,
          address,
          addresscode,
          addresstext,
          politicalStatus,
          politicalStatusLabel,
          jinjilianxiren,
          renxirendianhua,
          email,
          gerenzhuanchang,
          zaixiaohuojiang,
          ziwopingjia,
          hrmsEducationInfo,
          hrmsZpglWorkrecord,
          hrmsZpglAcademy,
          hrmsZpglContinueLearning,
          hrmsZpglProfessional,
          hrmsZpglFamily,

          isShoushu,
          isShoushutext,
          isChufeng,
          isChufengtext,
          jianlilaiyuan,
          referrerName,
          isXiangguanceshi,
          isGongzuodaodong,
          fujian,
        } = res.object;

        let otherData = {
          isShoushu,
          isShoushutext,
          isChufeng,
          isChufengtext,
          jianlilaiyuan,
          referrerName,
          isXiangguanceshi,
          isGongzuodaodong,
          fujian,
        };

        let genderOptionList = this.formList.find(
          ({ prop }) => prop === "genderLabel"
        ).optionList[0];
        genderLabel = genderOptionList.find(
          ({ itemNameValue }) => itemNameValue == gender
        )?.itemName;

        let personnelCategoryOptionList = this.formList.find(
          ({ prop }) => prop === "personnelCategoryLabel"
        ).optionList[0];
        personnelCategoryLabel = personnelCategoryOptionList.find(
          ({ itemNameValue }) => itemNameValue == personnelCategory
        )?.itemName;

        let waiyudengjiOptionList = this.formList.find(
          ({ prop }) => prop === "waiyudengjiLabel"
        ).optionList[0];
        waiyudengjiLabel = waiyudengjiOptionList.find(
          ({ itemNameValue }) => itemNameValue == waiyudengji
        )?.itemName;

        let nationalityOptionList = this.formList.find(
          ({ prop }) => prop === "nationalityLabel"
        ).optionList[0];
        nationalityLabel = nationalityOptionList.find(
          ({ itemNameValue }) => itemNameValue == nationality
        )?.itemName;

        let drivingLicenceLevelOptionList = this.formList.find(
          ({ prop }) => prop === "drivingLicenceLevelLabel"
        ).optionList[0];
        drivingLicenceLevelLabel = drivingLicenceLevelOptionList.find(
          ({ itemNameValue }) => itemNameValue == drivingLicenceLevel
        )?.itemName;

        let hujileixingOptionList = this.formList.find(
          ({ prop }) => prop === "hujileixingLabel"
        ).optionList[0];
        hujileixingLabel = hujileixingOptionList.find(
          ({ itemNameValue }) => itemNameValue == hujileixing
        )?.itemName;

        let politicalStatusOptionList = this.formList.find(
          ({ prop }) => prop === "politicalStatusLabel"
        ).optionList[0];
        politicalStatusLabel = politicalStatusOptionList.find(
          ({ itemNameValue }) => itemNameValue == politicalStatus
        )?.itemName;

        this.$set(this.form, "id", id);
        setTimeout(() => {
          this.$set(this.form, "avatar", avatar);
        }, 0);
        this.$set(this.form, "applicantPost", applicantPost);
        this.$set(this.form, "applicantPosttext", applicantPosttext);
        this.$set(this.form, "interviewPath", interviewPath);
        this.$set(this.form, "employeeName", employeeName);
        this.$set(this.form, "gender", gender);
        this.$set(this.form, "genderLabel", genderLabel);
        this.$set(this.form, "iphone", iphone);
        this.$set(this.form, "identityNumber", identityNumber);
        this.$set(this.form, "age", age);

        this.$set(this.form, "personnelCategory", personnelCategory);
        this.$set(this.form, "personnelCategoryLabel", personnelCategoryLabel);

        this.$set(this.form, "waiyudengji", waiyudengji);
        this.$set(this.form, "waiyudengjiLabel", waiyudengjiLabel);
        this.$set(this.form, "entryDate", entryDate);
        this.$set(this.form, "shenggao", shenggao);
        this.$set(this.form, "tizhong", tizhong);
        this.$set(this.form, "nationality", nationality);
        this.$set(this.form, "nationalityLabel", nationalityLabel);
        this.$set(this.form, "drivingLicence", drivingLicence);
        this.$set(this.form, "drivingLicenceLevel", drivingLicenceLevel);
        this.$set(
          this.form,
          "drivingLicenceLevelLabel",
          drivingLicenceLevelLabel
        );
        this.$set(this.form, "zhiyezhuangtai", zhiyezhuangtai);
        this.$set(this.form, "qiwangyuexin", qiwangyuexin);
        this.$set(this.form, "zuikuairuzhi", zuikuairuzhi);
        this.$set(this.form, "birthplace", birthplace);
        this.$set(this.form, "hujileixing", hujileixing);
        this.$set(this.form, "hujileixingLabel", hujileixingLabel);
        this.$set(this.form, "marriageStatus", marriageStatus);
        this.$set(this.form, "offspring", offspring);
        this.$set(this.form, "hukousuozaidi", hukousuozaidi);
        this.$set(this.form, "hukousuozaidicode", hukousuozaidicode);
        this.$set(this.form, "hukousuozaiditext", hukousuozaiditext);
        this.$set(this.form, "address", address);
        this.$set(this.form, "addresscode", addresscode);
        this.$set(this.form, "addresstext", addresstext);
        this.$set(this.form, "politicalStatus", politicalStatus);
        this.$set(this.form, "politicalStatusLabel", politicalStatusLabel);
        this.$set(this.form, "jinjilianxiren", jinjilianxiren);
        this.$set(this.form, "renxirendianhua", renxirendianhua);
        this.$set(this.form, "email", email);
        this.$set(this.form, "gerenzhuanchang", gerenzhuanchang);
        this.$set(this.form, "zaixiaohuojiang", zaixiaohuojiang);
        this.$set(this.form, "ziwopingjia", ziwopingjia);
        this.$emit("handleQueryRegistrationInfo", {
          hrmsEducationInfo,
          hrmsZpglWorkrecord,
          hrmsZpglAcademy,
          hrmsZpglContinueLearning,
          hrmsZpglProfessional,
          hrmsZpglFamily,
          otherData,
        });
      } else {
        uni.showToast({
          title: res.message || "获取登记信息失败,请联系管理员",
          icon: "none",
          duration: 2000,
        });
      }
    },
    
    handleClickTree(type) {
      this.inputClickType = type;
      if (type === "post") {
        this.eachValue.id = this.form.applicantPost;
        this.range = this.postTreeData;
      }
      if (type === "birthplace") {
        this.eachValue.id = this.form.birthplaceId;
        this.range = this.city;
      }

      this.dialogSelectTree = true;
    },

    handleTreeSubmit(clickItem) {
      const { allPath = "", id = "", name = "" } = clickItem;
      if (this.inputClickType === "post") {
        this.form.applicantPosttext = name;
        this.form.applicantPost = id;
        this.form.interviewPath = allPath;
      }
      if (this.inputClickType === "birthplace") {
        this.form.birthplace = allPath;
        this.form.birthplaceId = id;
      }

      this.$forceUpdate();
    },

    handleClickIdentityNumber() {
      this.$refs.KeyboardPackage.open();
    },

    inputVal(val) {
      const writeValue = (key, maxlength) => {
        let len = _this.form[key].length;
        if (!val) {
          _this.form[key] = _this.form[key].substring(0, len - 1);
          return;
        }
        if (len === maxlength) return;
        _this.form[key] += val;
      };

      val = val.toString();
      let key = "identityNumber";
      let _this = this;
      writeValue(key, 18);
    },

    // open 回显上次选中地区
    handleOpenSelectArea(setKey) {
      let that = this;
      this.setKey = setKey;
      switch (this.setKey) {
        case "hukousuozaidicode":
          this.form.hukousuozaidicode &&
            handleSetDefalut(this.form.hukousuozaidicode);
          break;
        case "addresscode":
          this.form.addresscode && handleSetDefalut(this.form.addresscode);
          break;
      }

      function handleSetDefalut(codeValue) {
        let data = common.deepClone(that.cityDatas);
        that.regionDefault = codeValue
          .split("-")
          .filter((t) => t)
          .map((key) => {
            let index = data.findIndex((item) => item.value == key);
            if (index >= 0) {
              data = data[index].children || [];
            }
            return index;
          });
      }
      this.regionSelectShow = true;
    },

    // 地区确认
    regionSelectConfirm(e) {
      let labelStr = e.map((item) => item.label).join("-");
      let valueStr = e.map((item) => item.value).join("-");
      switch (this.setKey) {
        case "hukousuozaidicode":
          this.$set(this.form, "hukousuozaidi", labelStr);
          this.$set(this.form, "hukousuozaidicode", valueStr);
          break;
        case "addresscode":
          this.$set(this.form, "address", labelStr);
          this.$set(this.form, "addresscode", valueStr);
          break;
      }
    },

    // 获取应聘岗位
    async handleGetHrRecruitmentTalentPoolTree() {
      const res = await getHrRecruitmentTalentPoolTree();

      if (res.success == false) {
        return;
      }

      this.postTreeData = res.object;
    },

    async submit() {
      let validate = await this.$refs.baseForm.validate();
      if (!validate) {
        return false;
      }
      return this.form;
    },
    // 获取字典
    async handleGetDictionaries() {
      //人员类别
      const res0 = await getDictionaries("ORG_ATTRIBUTES");
      this.formList.filter(
        (item) => item.prop === "personnelCategoryLabel"
      )[0].optionList = [res0];

      //学历
      const res = await getDictionaries("SEX_TYPE");
      this.formList.filter(
        (item) => item.prop === "genderLabel"
      )[0].optionList = [res];

      //民族
      const res1 = await getDictionaries("nationality_name");
      this.formList.filter(
        (item) => item.prop === "nationalityLabel"
      )[0].optionList = [res1];

      //驾驶证级别
      const res2 = await getDictionaries("DRIVING_LICENCE_LEVEL");
      this.formList.filter(
        (item) => item.prop === "drivingLicenceLevelLabel"
      )[0].optionList = [res2];

      //婚育状况
      const res3 = await getDictionaries("marriage_status");
      const list3 = res3.map((item) => {
        return {
          label: item.itemName,
          value: item.itemNameValue,
        };
      });
      this.formList.filter(
        (item) => item.prop === "marriageStatus"
      )[0].radioList = list3;

      //外语等级
      const res4 = await getDictionaries("WAIYU_TYPE");
      this.formList.filter(
        (item) => item.prop === "waiyudengjiLabel"
      )[0].optionList = [res4];

      //户籍类型
      const res5 = await getDictionaries("HUJI_TYPE");
      this.formList.filter(
        (item) => item.prop === "hujileixingLabel"
      )[0].optionList = [res5];

      //政治面貌
      const res6 = await getDictionaries("political_status");
      this.formList.filter(
        (item) => item.prop === "politicalStatusLabel"
      )[0].optionList = [res6];
    },
    // 将省市区设置为两级
    handleSecondArea() {
      digui(this.city, {}, undefined);
      function digui(data, parent, num) {
        for (let index = 0; index < data.length; index++) {
          const item = data[index];
          if (item.children) {
            let count;
            if (!num) {
              count = 1;
            } else {
              count = num;
            }
            count++;
            digui(item.children, item, count);
          } else {
            if (num === 3) {
              parent.children = null;
            }
          }
        }
      }
    },
  },
};
</script>

<style lang="scss">
.children-box {
  padding: 8px 0;
  .title {
    font-size: 16px;
    color: #000;
    font-weight: 700;
  }
  /deep/ .u-form {
    .id-card-container {
      display: flex;
      align-items: center;
      .ts-input {
        flex: 1;
      }
      .validate-btn {
        width: 50px;
        display: inline-block;
        color: #000;
      }
    }
    .all-width {
      .item__body__right__content__icon {
        width: 100%;
      }
    }
    .radio-width {
      .item__body__right__content__icon {
        width: 100%;
        .u-radio-group {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
