<template>
  <div class="children-box">
    <view class="title">
      <span>3、工作经历（由高到低）</span>
      <ts-button text="新增" size="mini" type="primary" @click="handleAdd"
        >新增</ts-button
      >
    </view>
    <view v-for="(item, index) in list" :key="item.id">
      <base-form
        :ref="`baseForm${index}`"
        :formList="item.formList"
        :formData.sync="item.form"
        :rules="item.rules"
        :showSubmitButton="false"
      >
        <template slot="lizhiyuanyingList">
          <ld-select
            class="select-input-contaier"
            :multiple="true"
            :list="lizhiyyList"
            label-key="itemName"
            value-key="itemNameValue"
            placeholder="请选择"
            clearable
            v-model="item.form.lizhiyuanyingList"
          ></ld-select>
        </template>
      </base-form>

      <view class="del-item">
        <span></span>
        <ts-button
          text="删除"
          size="mini"
          type="error"
          @click="handleDelItem(item)"
        ></ts-button>
      </view>
    </view>
  </div>
</template>

<script>
import ldSelect from "components/ld-select/ld-select.vue";
import { getDictionaries } from "api/data.js";
import BaseForm from "components/base-form/base-form.vue";
import common from "util/common.js";

function returnItem(id = common.guid()) {
  return {
    id: id,
    formList: [
      {
        title: "开始时间",
        prop: "start",
        type: "select",
        mode: "time",
        disabled: true,
		    required: true,
        placeholder: "请选择开始时间",
      },
      {
        title: "结束时间",
        prop: "end",
        type: "select",
        mode: "time",
        disabled: true,
		    required: true,
        placeholder: "请选择结束时间",
      },
      {
        title: "单位名称",
        prop: "danweimingcheng",
        propVal: "danweimingcheng",
        placeholder: "请输入单位名称",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
      {
        title: "科室",
        prop: "keshi",
        propVal: "keshi",
        placeholder: "请输入科室",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
      {
        title: "职务",
        prop: "zhiwu",
        propVal: "zhiwu",
        placeholder: "请输入职务",
        type: "text",
        mode: "input",
        maxlength: 25,
        required: true,
      },
      {
        title: "薪资",
        prop: "xinzi",
        propVal: "xinzi",
        placeholder: "请输入薪资（元）",
        type: "number",
        mode: "input",
        callback: (e) => e?.replace(/\D/g, ""),
        maxlength: 25,
      },
      {
        title: "证明人及电话",
        prop: "zhengmingren",
        propVal: "zhengmingren",
        placeholder: "请输入证明人及电话",
        type: "text",
        mode: "input",
        maxlength: 25,
      },
      {
        title: "离职原因",
        labelWidth: 150,
        prop: "lizhiyuanyingList",
        propVal: "lizhiyuanyingList",
        rightSlot: true,
      },
    ],
    form: {
      start: "",
      end: "",
      danweimingcheng: "",
      keshi: "",
      zhiwu: "",
      xinzi: "",
      zhengmingren: "",
      lizhiyuanyingList: [],
    },
    rules: {
      start: [
        {
          required: true,
          message: "请填写开始时间",
          trigger: "",
        },
      ],
      end: [
        {
          required: true,
          message: "请填写结束时间",
          trigger: "",
        },
      ],
      danweimingcheng: [
        {
          required: true,
          message: "请填写单位名称",
          trigger: "",
        },
      ],
      keshi: [
        {
          required: true,
          message: "请填写科室",
          trigger: "",
        },
      ],
      zhiwu: [
        {
          required: true,
          message: "请填写职务",
          trigger: "",
        },
      ],
    },
  };
}

export default {
  components: {
    BaseForm,
    ldSelect,
  },
  async created() {
    await this.handleGetDictionaries();
    this.handleAdd();
  },
  data() {
    return {
      list: [],

      lizhiyyList: [],
      returnItem,
    };
  },
  methods: {
    async handleGetDictionaries() {
      const res = await getDictionaries("LIZHI_TYPE");
      this.lizhiyyList = res;
    },
    handleAdd() {
      this.list.push(this.returnItem());
    },
    handleDelItem({ id }) {
      const index = this.list.findIndex((item) => item.id === id);
      if (index != -1) {
        this.list.splice(index, 1);
      }
    },
    async submit() {
      let result = [];
      for (var i = 0; i < this.list.length; i++) {
        let v = await this.$refs[`baseForm${i}`][0].validate();
        result.push(v);
      }

      if (result.every((item) => item)) {
        return this.list.map((item) => item.form);
      } else {
        return false;
      }
    },
    async handleEcho(list) {
      this.list = list.map((item) => {
        let renderListItem = this.returnItem();
        let [start = "", end = ""] =
          item.qizhishijian && item.qizhishijian.split("~");
        item.start = start;
        item.end = end;
        delete item.qizhishijian;

        renderListItem.form = item;
        return renderListItem;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.children-box {
  padding: 8px 0;
  width: 100%;
  /deep/ .u-form-item__body__right__content {
    width: 100%;

    .item__body__right__content__icon {
      width: 100%;
    }
  }

  .del-item {
    padding: 0 8px 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }
  .title {
    width: 100%;
    padding: 0 8px;
    font-size: 16px;
    color: #000;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    .add-btn {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
