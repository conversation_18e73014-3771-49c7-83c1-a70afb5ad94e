<template>
  <view class="content">
    <u-sticky class="sticky" v-show="currentIndex != 8">
      <ts-tabs disabled :list="list" :current="currentIndex" />
    </u-sticky>
    <view class="dialog"></view>

    <view class="form-content">
      <view v-show="currentIndex === 0">
        <BasisForm
          ref="form0"
          @handleQueryRegistrationInfo="handleQueryRegistrationInfo"
        />
      </view>
      <view v-show="currentIndex === 1">
        <Study ref="form1" />
      </view>
      <view v-show="currentIndex === 2">
        <Wrok ref="form2" />
      </view>
      <view v-show="currentIndex === 3">
        <Group ref="form3" />
      </view>
      <view v-show="currentIndex === 4">
        <Cultivate ref="form4" />
      </view>
      <view v-show="currentIndex === 5">
        <Title ref="form5" />
      </view>
      <view v-show="currentIndex === 6">
        <Famliy ref="form6" />
      </view>
      <view v-show="currentIndex === 7">
        <Other ref="form7" />
      </view>
      <view v-show="currentIndex === 8">
        <submit-success />
      </view>
    </view>

    <view class="bottom-submit" v-show="currentIndex != 8">
      <ts-button
        v-show="currentIndex !== 0"
        text="上一步"
        size="normal"
        type="warning"
        @click="handleGoBack"
      ></ts-button>
      <ts-button
        v-show="currentIndex !== 7"
        text="下一步"
        size="primary"
        @click="handleGoToNext"
      ></ts-button>
      <ts-button
        v-show="currentIndex === 7"
        text="提交"
        size="normal"
        type="success"
        :disabled="disabled"
        @click="handleSubmit"
      ></ts-button>
    </view>
  </view>
</template>

<script>
import BasisForm from "./components/basis-form.vue";
import Study from "./components/study";
import Wrok from "./components/work";
import Group from "./components/group.vue";
import Cultivate from "./components/cultivate";
import Title from "./components/title";
import Famliy from "./components/famliy.vue";
import Other from "./components/other";
import SubmitSuccess from "./components/submit-success.vue";
import { setZpglEmployee, updateZpglEmployee } from "api/data.js";
import common from "util/common.js";

export default {
  components: {
    BasisForm,
    Study,
    Wrok,
    Group,
    Cultivate,
    Title,
    Famliy,
    Other,
    SubmitSuccess,
  },
  data() {
    return {
      currentIndex: 0,
      list: [
        {
          name: "基本信息",
        },
        {
          name: "学习经历",
        },
        {
          name: "工作经历",
        },
        {
          name: "学术团体任职情况",
        },
        {
          name: "进修培训",
        },
        {
          name: "职称晋升",
        },
        {
          name: "家庭关系",
        },
        {
          name: "简历附件",
        },
      ],
      disabled: false,
      submitForm: {},
    };
  },
  onLoad() {},
  methods: {
    handleGoBack() {
      this.currentIndex--;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });
    },
    handleQueryRegistrationInfo(data) {
      this.$refs.form1.handleEcho(data.hrmsEducationInfo);
      this.$refs.form2.handleEcho(data.hrmsZpglWorkrecord);
      this.$refs.form3.handleEcho(data.hrmsZpglAcademy);
      this.$refs.form4.handleEcho(data.hrmsZpglContinueLearning);
      this.$refs.form5.handleEcho(data.hrmsZpglProfessional);
      this.$refs.form6.handleEcho(data.hrmsZpglFamily);
      this.$refs.form7.handleEcho(data.otherData);
    },
    async handleGoToNext() {
      const res = await this.$refs[`form${this.currentIndex}`].submit();

      if (res) {
        this.submitForm[this.currentIndex] = res;
        this.currentIndex++;

        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0,
        });
      }
    },
    async handleSubmit() {
      const form7Res = await this.$refs.form7.submit();
      if (!form7Res) {
        return;
      }

      this.disabled = true;
      this.submitForm[7] = this.$refs.form7.form;

      let localForm = common.deepClone(this.submitForm);
      const data = {
        ...localForm[0],
        'hrmsEducationInfo': localForm[1],
        'hrmsZpglWorkrecord': localForm[2],
        'hrmsZpglAcademy': localForm[3],
        'hrmsZpglContinueLearning': localForm[4],
        'hrmsZpglProfessional': localForm[5],
        'hrmsZpglFamily': localForm[6],
        ...localForm[7],
      };

      let API = setZpglEmployee;
      if (data.id) API = updateZpglEmployee;

      const {
        hrmsEducationInfo,
        hrmsZpglWorkrecord,
        hrmsZpglAcademy,
        hrmsZpglContinueLearning,
        hrmsZpglProfessional,
      } = data;

      [hrmsEducationInfo, hrmsZpglWorkrecord, hrmsZpglAcademy, hrmsZpglContinueLearning].forEach(f => {
        f.forEach(item => {
          if (item.start && item.end) item.qizhishijian = `${item.start}~${item.end}`;
          delete item.start;
          delete item.end;
        })
      });
      //新增接口为白名单接口，需要获取当前登录账号机构编码传参
      data.ssoOrgCode = this.$store.state.common.userInfo.corpcode;
      const res = await API(data);
      if (res.success && res.statusCode === 200) {
        uni.showToast({
          title: "登记成功!",
          icon: "none",
          duration: 2000,
        });
        setTimeout(() => {
          this.currentIndex = 8;
          this.disabled = false;
        }, 2000);
      } else {
        uni.showToast({
          title: res.message,
          icon: "none",
          duration: 2000,
        });
        this.disabled = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /deep/ * {
    box-sizing: border-box;
  }
  /deep/ .sticky {
    top: 0px !important;
    background: #fff !important;
  }
  /deep/ .select-input-contaier {
    .uni-input-input {
      pointer-events: none !important;
    }
  }

  .dialog {
    position: fixed;
    background-color: transparent;
    width: 100vw;
    top: 0;
    left: 0;
    height: 42px;
    z-index: 99999;
  }

  .form-content {
    width: 100%;
    padding-bottom: 55px;
  }

  .bottom-submit {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    position: fixed;
    bottom: 0;
    height: 55px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    padding-top: 8px;

    .ts-button {
      width: 30%;
      margin: 0 8px;
      /deep/ .u-button__text {
        color: #fff !important;
      }
    }
  }
}
</style>
