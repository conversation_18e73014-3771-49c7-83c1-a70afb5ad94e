<template>
  <view>
    <view class="container flex text-flex-center" @click="uploadAvatar">
      <view></view>
      <image
        class="avatar"
        v-if="!imageUrl"
        src="/static/default_avatar.png"
      ></image>
      <image class="avatar" v-else :src="imageUrl"></image>
    </view>
  </view>
</template>

<script>
import common from "util/common.js";

import { getFileAttachmentByBusinessId } from "api/data.js";
export default {
  model: {
    prop: "businessId",
    event: "input",
  },
  props: {
    businessId: {
      type: String,
      default: () => "",
    },
    action: {
      //上传地址
      type: String,
      default: "",
    },
  },
  data() {
    return {
      imageUrl: "",
    };
  },
  watch: {
    businessId: {
      handler(val) {
        // 本地没有预览头像 则进行回显
        if (val && !this.imageUrl) {
          getFileAttachmentByBusinessId({ businessId: val }).then((res) => {
            if (res.success == false) {
              return;
            }
            this.imageUrl = location.origin + res.object[0].realPath;
          });
        }
      },
    },
  },
  methods: {
    uploadAvatar() {
      uni.chooseImage({
        count: 1,
        success: ({ tempFiles }) => {
          uni.showLoading({
            title: "上传中",
          });

					// 上传每次都是一个新的businessId 头像只有一个
          let businessId = common.guid();
          tempFiles.forEach((item) => {
            uni.uploadFile({
              count: 1,
              url: "/ts-basics-bottom/fileAttachment/openUpload?moduleName=hrm",
              fileType: "image",
              filePath: item.path,
              formData: {
                moduleName: "zp",
                businessId: businessId,
              },

              success: (uploadFileRes) => {
                let res = JSON.parse(uploadFileRes.data);
                if (res.statusCode === 200 && res.success) {
                  this.imageUrl = location.origin + res.object.realPath;
                  this.$emit("input", businessId);
                }
              },
              fail: (err) => {
                uni.hideLoading();
              },
              complete: () => {
                uni.hideLoading();
              },
            });
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  justify-content: space-between;
}

.avatar {
  width: 48px;
  height: 48px;
}

.icon-photo {
  position: absolute;
  left: 54%;
  bottom: 4rpx;
  z-index: 100;

  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
}
</style>
