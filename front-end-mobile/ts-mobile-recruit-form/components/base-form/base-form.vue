<template>
  <view class="form">
    <view class="form-container">
      <u-form
        class="form-box"
        ref="uForm"
        :model="form"
        :error-type="errorType"
        :rules="rules"
      >
        <template v-for="(item, index) in formList">
          <u-form-item
            :key="index"
            :required="item.required"
            :label-width="item.labelWidth || $formLabelWidth"
            :label-align="item.labelAlign"
            :label-position="
              item.labelPosition
                ? item.labelPosition
                : labelPositionFilter(
                    item.type,
                    $formInputLabelPosition,
                    $formTextareaLabelPosition
                  )
            "
            :label="item.title"
            :prop="item.prop"
            :class="item.itemClass"
          >
            <template #left>
              <text
                v-if="item.labelSlot"
                :class="item.labelSlotClass"
                :style="item.labelSlotStyle"
                @click="labelSlotClick(item)"
              >
                {{ item.labelSlot }}
              </text>
            </template>

            <template
              #right
              v-if="
                item.type == 'radio' || item.type == 'rate' || item.rightSlot
              "
            >
              <slot :name="item.prop">
                <u-radio-group
                  v-if="item.type == 'radio'"
                  v-model="form[item.prop]"
                  @change="radioGroupChange($event, item)"
                  :wrap="item.radioCheckWrap"
                >
                  <u-radio
                    shape="circle"
                    v-for="(radioItem, radioIndex) in item.radioList"
                    :key="radioIndex"
                    :name="radioItem.value"
                  >
                    {{ radioItem.label }}
                  </u-radio>
                </u-radio-group>
                <u-rate
                  v-else-if="item.type == 'rate'"
                  :count="item.count"
                  v-model="form[item.prop]"
                  :inactive-color="item.inactiveColor"
                  :active-color="item.activeColor"
                  :size="item.size"
                  :active-icon="item.activeIcon"
                  :inactive-icon="item.inactiveIcon"
                  :custom-prefix="item.customPrefix"
                >
                </u-rate>
                <text
                  v-if="item.rightSlot"
                  :class="item.rightSlotClass"
                  :style="item.rightSlotStyle"
                  @click="rightSlotClick(item)"
                >
                  {{ item.rightSlot }}
                </text>
              </slot>
            </template>
            <template
              #default
              v-if="
                item.type == 'file' ||
                  item.type == 'select' ||
                  item.type == 'text' ||
                  item.type == 'number' ||
                  item.type == 'textarea'
              "
            >
              <slot :name="item.prop">
                <base-upload
                  v-if="item.type == 'file'"
                  v-model="form[item.prop]"
                  :disabled="item.disabled"
                ></base-upload>
                <u-textarea
                  v-else-if="item.type == 'textarea'"
                  v-model="form[item.prop]"
                  :key="item.props"
                  v-bind="{
                    border: $formInputBorder,
                    placeholder: item.placeholder || '请输入',
                    ...item.elementProps
                  }"
                ></u-textarea>
                <u-input
                  v-else
                  v-model="form[item.prop]"
                  :key="item.props"
                  :class="{ 'select-input-contaier': item.disabled }"
                  :border="$formInputBorder"
                  :height="item.height"
                  :type="item.type"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :input-align="
                    item.inputAlign
                      ? item.inputAlign
                      : inputAlignFilter(
                          item.type,
                          item.labelPosition,
                          $formInputAlign,
                          $formTextareaAlign
                        )
                  "
                  :maxlength="item.maxlength"
                  trim
                  @input="item.callback ? changeInputVal($event, item) : ''"
                  @click.native="
                    item.type == 'select' && item.mode == 'select'
                      ? changeSelectShow(item, index)
                      : item.type == 'select' && item.mode == 'time'
                      ? changePickerShow(item)
                      : item.type == 'select' && item.mode == 'person'
                      ? choosePerson(item)
                      : item.type == 'select' && item.mode == 'dept'
                      ? chooseDept(item)
                      : ''
                  "
                ></u-input>
              </slot>
            </template>
          </u-form-item>
        </template>
      </u-form>
      <view class="button-box" v-if="showSubmitButton">
        <u-button type="primary" @click="submit">{{ submitTitle }}</u-button>
      </view>
    </view>
    <u-picker
      ref="picker"
      :show="pickerShow"
      :columns="columns"
      v-bind="pickerProps"
      @change="handlePickerChange"
      @cancel="pickerShow = false"
      @confirm="pickerConfirm"
    ></u-picker>
    <u-datetime-picker
      ref="timePicker"
      :show="timePickerShow"
      v-model="timePickerVal"
      v-bind="timePickerProps"
      @confirm="handleTimePickerConfirm"
      @cancel="timePickerShow = false"
    ></u-datetime-picker>
  </view>
</template>

<script>
import uForm from 'uview-ui/components/u--form/u--form.vue';
import uFormItem from 'uview-ui/components/u-form-item/u-form-item.vue';
import uInput from 'uview-ui/components/u-input/u-input.vue';
import uPicker from 'uview-ui/components/u-picker/u-picker.vue';

export default {
  name: 'base-form',
  components: { uForm, uFormItem, uInput, uPicker },
  props: {
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    submitTitle: {
      type: String,
      default: '提交'
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    showSubmitButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      errorType: 'toast',
      selctAllObj: {},
      clickProp: null,
      clickPropVal: null,
      relationProp: [],
      clickMode: null,
      clickParams: null,
      clickField: null,
      selectShow: false,

      pickerShow: false,
      actionPickerItem: {},
      columns: [], //选择器 选项
      pickerChangeMethod: null, //picker 选中值改变事件
      pickerProps: {}, //picker 属性值
      personLabel: {},
      deptLabel: {},

      timePickerShow: false, //控制时间选择器显示
      actionTimePickerItem: {}, // 当前正在选择的form项
      timePickerProps: {}, // 时间选择器 属性
      timePickerVal: null, // 时间选择器值
      form: this.formData
    };
  },
  watch: {
    formList: {
      handler(newVal = [], oldVal = []) {
        this.init(newVal, oldVal);
      },
      immediate: true,
      deep: true
    },
    form(newVal) {
      this.$emit('update:formData', newVal);
    }
  },
  mounted() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    labelPositionFilter(type, inputLable, textareaLable) {
      if (type == 'textarea' || type == 'file') {
        return textareaLable;
      } else {
        return inputLable;
      }
    },
    inputAlignFilter(type, labelPosition, inputAlign, textareaAlign) {
      if (type == 'textarea') {
        return textareaAlign;
      } else {
        if (labelPosition == 'top') {
          return 'left';
        }
        return inputAlign;
      }
    },
    //初始化选择项、校验规则
    init(newFormList, oldFormList) {
      let selctAllObj = {},
        personLabel = {},
        deptLabel = {};

      newFormList.forEach(item => {
        let prop = item.prop;
        if (item.optionList) {
          selctAllObj[prop] = item.optionList;
        }

        if (item.type == 'select' && item.mode == 'person') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'person' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            personLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            personLabel[prop] = [];
          }
        } else if (item.type == 'select' && item.mode == 'dept') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'dept' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            deptLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            deptLabel[prop] = [];
          }
        }
      });
      this.selctAllObj = JSON.parse(JSON.stringify(selctAllObj));
      this.personLabel = JSON.parse(JSON.stringify(personLabel));
      this.deptLabel = JSON.parse(JSON.stringify(deptLabel));

      //抛出初始化完成事件，用以解决初始化数据清空问题, 或者初始化赋值问题
      this.$emit('init-finished');
    },
    //单选
    radioGroupChange(e, item) {
      this.$set(this.form, item.prop, e);
      if (item.callback) {
        item.callback(e);
      }
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.form, item.prop, item.callback(e, item));
      });
    },
    //打开列选择器
    changeSelectShow(e) {
      document.activeElement.blur();
      this.pickerShow = true;
      this.actionPickerItem = e;
      this.columns = e.optionList || [];
      this.pickerProps = e.elementProps || {};
      this.pickerChangeMethod = e.handleChange;
    },
    /**@desc picker 选中项改变事件 */
    handlePickerChange(e) {
      const { columnIndex, index, picker = this.$refs.picker } = e;
      if (
        this.pickerChangeMethod &&
        Object.prototype.toString.call(this.pickerChangeMethod) ==
          '[object Function]'
      ) {
        this.pickerChangeMethod(e);
      } else {
        let column = this.columns[columnIndex],
          selectItem = {};
        if (Object.prototype.toString.call(column) == '[object Array]') {
          selectItem = column[index] || {};
          let nextColumnKey = this.pickerProps.children || 'children',
            seconedItems = selectItem[nextColumnKey] || [];

          column.find(
            item => item[nextColumnKey] && item[nextColumnKey].length
          ) && picker.setColumnValues(columnIndex + 1, seconedItems);
        }
      }
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (this.form[this.clickPropVal] !== e[0].value) {
        this.$set(this.form, this.clickProp, e[0].label);
        this.$set(this.form, this.clickPropVal, e[0].value);
        if (this.relationProp.length > 0) {
          for (var i = 0; i < this.relationProp.length; i++) {
            this.$set(this.form, this.relationProp[i].prop, '');
            if (this.relationProp[i].propVal) {
              this.$set(this.form, this.relationProp[i].propVal, '');
            }
          }
        }
      }
    },
    //打开选择器
    changePickerShow(e) {
      if (e.mode == 'time') {
        let minDate = this.$dayjs()
            .subtract(100, 'year')
            .format('YYYY-MM-DD'),
          maxDate = this.$dayjs()
            .add(100, 'year')
            .format('YYYY-MM-DD');

        this.timePickerShow = true;
        this.actionTimePickerItem = e;
        this.timePickerVal =
          this.form[e.prop] || this.$dayjs().format('YYYY-MM-DD');
        this.timePickerProps = {
          mode: 'date',
          minDate: new Date(minDate).getTime(),
          maxDate: new Date(maxDate).getTime(),
          valueFormat: 'YYYY-MM-DD',
          ...e.elementProps
        };
        this.timePickerProps.formatter &&
          this.$refs.timePicker.setFormatter(this.timePickerProps.formatter);
        return;
      }
      this.pickerShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.pickerProps = e.elementProps;
      this.clickField = e.field;
    },
    handleTimePickerConfirm(val = {}) {
      let { value = '' } = val,
        { prop } = this.actionTimePickerItem;
      this.$set(
        this.form,
        prop,
        this.$dayjs(value).format(this.timePickerProps.valueFormat)
      );
      this.timePickerShow = false;
    },
    async getDatas(api) {
      let datas = await this.ajax.getDatas(api);
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    //时间确认事件
    pickerConfirm(e) {
      if (this.clickMode == 'time') {
        this.$set(
          this.form,
          this.clickProp,
          this.timePickerConfirm(e, this.clickField)
        );
      } else {
        const pickerConfirm = this.actionPickerItem.handleConfirm;

        if (
          Object.prototype.toString.call(pickerConfirm) == '[object Function]'
        ) {
          pickerConfirm(e);
          this.pickerShow = false;
          return;
        }
        // this.$set(this.form, this.actionPickerItem.prop);
        // this.$set(this.form, this.actionPickerItem.propVal, column);
      }
    },
    //格式化时间
    timePickerConfirm(e, field = 'yy-MM-dd') {
      if (field == 'YY') {
        return `${e.year}年`;
      } else if (field == 'yy-MM') {
        return `${e.year}-${e.month}`;
      } else if (field == 'yy-MM-dd') {
        return `${e.year}-${e.month}-${e.day}`;
      } else if (field == 'HH:mm') {
        return `${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm:ss') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
      }
    },
    //选择人员
    choosePerson(item) {
      // let personList = this.personLabel[item.prop] || [];
      let personList = [];
      let personLabels =
          (this.form[item.prop] && this.form[item.prop].split(',')) || [],
        personValues =
          (this.form[item.propVal] && this.form[item.propVal].split(',')) || [],
        personInfoProp = {
          name: 'empName',
          describe: 'empDeptName',
          concatSymbol: '',
          key: 'empCode',
          ...(item.personInfoProp || {})
        };
      personLabels.map((label, index) => {
        personList.push({
          [personInfoProp.name]: label,
          [personInfoProp.key]: personValues[index]
        });
      });

      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.name,
        personInfoProp: personInfoProp
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < (item.searchParams || []).length; i++) {
          if (
            !this.form[item.searchParams[i].value] &&
            item.searchParams[i].message
          ) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        personPageParams = { ...personPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        // this.$set(this.personLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        } else {
          let personNameList = [],
            personIdList = [],
            personName =
              (item.personInfoProp && item.personInfoProp.name) || 'empName',
            psersonId =
              (item.personInfoProp && item.personInfoProp.key) || 'empCode';

          data.map(i => {
            personNameList.push(i[personName]);
            personIdList.push(i[psersonId]);
          });
          this.$set(this.form, item.prop, personNameList.join(','));
          this.$set(this.form, item.propVal, personIdList.join(','));
        }
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
        this.$forceUpdate();
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${item.chooseType}&getListMode=${item.getListMode}`
      });
    },
    chooseDept(item) {
      let deptList = this.deptLabel[item.prop] || [];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.deptLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        }
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.id);
        });
        this.$set(this.form, item.prop, deptName.join(','));
        this.$set(this.form, item.propVal, deptId.join(','));
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
        this.$forceUpdate();
      });

      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&mode=${item.selectMode || 'scroll'}`
      });
    },
    //label右侧控件点击事件
    labelSlotClick(e) {
      if (e.labelSlotCallback) {
        e.labelSlotCallback(e);
      }
    },
    rightSlotClick(e) {
      if (e.labelSlotCallback) {
        e.rightSlotClick(e);
      }
    },
    async validate() {
      return await this.$refs.uForm.validate();;
    },
    //提交表单
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          this.$emit('submit', valid);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: $uni-spacing-col-base;
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
.file-list {
  display: flex;
  .u-icon {
    margin-right: 8px;
  }
  .file-list-item-title {
    max-width: 360rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
/deep/.file-upload-item .u-form-item__body {
  flex-direction: unset !important;
  .u-form-item__body__right__content__slot {
    justify-content: flex-end;
  }
}
/deep/ .u-form-item__body .u-textarea {
  margin-top: 10px;
}
</style>
