<template>
  <view class="base-upload-container">
    <view class="upload-icon" @click="upload">
      <u-icon name="plus-circle" size="18"></u-icon>
    </view>
    <!-- 禁止删除 外网无删除接口 未开放白名单 不安全 后台系统可以删除。 -->
    <u-upload
      v-bind="elementProps"
      :fileList="picList"
      :deletable="false"
      @delete="handleDelete"
    >
      <slot>
        <view></view>
      </slot>
    </u-upload>
  </view>
</template>

<script>
import common from "util/common.js";
import Base64 from "util/base64.min.js";
import {
  fileAttachmentOpenUpload,
  getFileAttachmentByBusinessId,
} from "api/data.js";

export default {
  name: "base-upload",
  model: {
    prop: "businessId",
    event: "input",
  },
  props: {
    businessId: {
      type: String,
    },
    elementProps: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      picList: [],
    };
  },
  watch: {
    businessId: {
      handler(val) {
        this.picList = [];
        if (val) this.getFileList();
      },
      immediate: true,
    },
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      getFileAttachmentByBusinessId({ businessId: this.businessId }).then(
        (res) => {
          if (res.success == false) {
            return;
          }
          // this.picList = (res.object || []).map((item) => {
          this.picList = (res.object || []).map((item) => {
            return {
              ...item,
              url: location.origin + item.realPath,
              fileId: item.fileId || item.id,
            };
          });
        }
      );
    },

    /**@desc 上传文件 */
    async upload() {
      // 选择文件
      let chooseFiles = await new Promise((resolve, reject) => {
        uni.chooseFile({
          count: 1,
          sourceType: ["album", "camera"], // 可以选择的图片来源类型
          success: ({ tempFiles }) => {
            let file = tempFiles[0],
              useFullFiles = [],
              errorFiles = [];

            var fileSize = file.size / (1024 * 1024);
            if (fileSize > 10) {
              uni.showToast({
                title: "上传附件请不要超过10MB",
                icon: "none",
                duration: 2000,
              });
              return;
            }

            if (
              common.isImg(file.name) ||
              file.name.toLowerCase().indexOf(".mhtml") >= 0
            ) {
              useFullFiles.push(file);
            } else {
              errorFiles.push(file);
            }

            if (errorFiles.length) {
              uni.showToast({
                title:
                  "暂不支持的文件类型！" +
                  errorFiles.map((file) => file.name).join("，"),
                icon: "none",
                duration: 2000,
              });
            }

            resolve(useFullFiles);
          },
          fail() {
            resolve([]);
          },
        });
      });

      if (!chooseFiles.length) {
        return;
      }

      let data = new FormData(),
        successFiles = [],
        errorFiles = [];
      data.append("file", chooseFiles[0]);
      if (this.businessId) {
        data.append("businessId", this.businessId);
      } else {
        let businessId = common.guid();
        data.append("businessId", businessId);
        this.$emit("input", businessId);
      }
      data.append("moduleName", "zp");

      const res = await fileAttachmentOpenUpload(data);
      if (!res.success) {
        errorFiles.push({
          res,
          file: chooseFiles[0],
        });
      } else {
        successFiles.push(res.object);
      }

      //提示上传失败文件
      if (errorFiles.length) {
        let title = "";
        let { res, file } = errorFiles[0];
        title = file.name + "，" + (res.message || "上传失败");
        uni.showToast({
          title,
          duration: 2000,
          icon: "none",
        });
      }

      if (!successFiles.length) {
        return;
      }

      this.picList.push(
        ...successFiles.map((item) => ({
          ...item,
          fileName: item.originalName,
          url: location.origin + item.realPath,
          fileId: item.fileId || item.id,
        }))
      );
    },
    /**@desc 下载 */
    handleDownLoad(file) {
      let fileUrl = file.url;
      if (file.realPath.indexOf("ts-basics-bottom") == -1) {
        fileUrl =
          location.origin +
          "/ts-document/attachment/downloadFile/" +
          file.fileId;
      }
      // #ifdef H5
      let ua = navigator.userAgent.toLowerCase();
      if (ua.indexOf("micromessenger") >= 0 && ua.match(/ipad|iphone|ipod/)) {
        uni.showModal({
          title: "提示",
          content: "请在浏览器中打开该页面进行下载",
          showCancel: false,
        });
      } else {
        let el = document.createElement("a");
        el.href = fileUrl;
        el.target = "_blank";
        el.download = file.fileName;
        document.body.appendChild(el);
        el.click();
        el.remove();
      }
      // #endif
      // #ifndef H5
      uni.downloadFile({
        url: file.filePath,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePaths,
              success: function (data) {
                this.$u.toast("下载完成");
              },
            });
          }
        },
      });
      // #endif
    },
    /**@desc 预览 */
    previewFile(e) {
      let filePath = `${this.$documentPreviewHost}${e.realPath}?fullfilename=${
        e.fileId
      }.${e.fileSuffix || e.fileName.split(".")[1]}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.base-upload-container {
  width: 100%;
  position: relative;
  padding-top: 4px;
  .upload-icon {
    position: absolute;
    right: 0;
    top: -19px;
  }
  .file-list-content + .u-upload {
    margin-top: 4px;
  }
}
.file-item {
  display: flex;
  align-items: baseline;
  width: 100%;
  overflow: hidden;
  .file-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
}
.file-action-btn {
  flex-shrink: 0;
  color: #005bac;
  & + & {
    margin-left: 8px;
  }
  &.delete-btn {
    color: #ff605a;
  }
}
/deep/ .u-upload__deletable {
  background-color: #ff605a;
}
</style>
