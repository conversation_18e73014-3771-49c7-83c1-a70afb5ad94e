export default {
  methods: {
    // 父级别content点击
    parentContentClick(e) {
      e.stopPropagation();
      const element = e.currentTarget.closest(".tree-parent");
      const isSwitch = [...element.classList].indexOf("close") === -1;
      const itemSwitch = element.getElementsByClassName("item-switch-open")[0];

      if (isSwitch) {
        element.classList.add("close");
        itemSwitch.classList.add("item-switch-close");
      } else {
        element.classList.remove("close");
        itemSwitch.classList.remove("item-switch-close");
      }
    },
    renderItemSelect(data, level) {
      const {
        $createElement,
        selectList,
        parentContentClick,
        itemContentClick,
      } = this;

      return data.map((item) => {
        let localLevel = 0;
        if (level) localLevel = level;

        if (item.children) {
          return [
            $createElement(
              "view",
              {
                attrs: {
                  class: `tree-parent tree-parent-${localLevel} close ${item.id}`,
                },
                on: {
                  click: parentContent<PERSON>lick,
                },
                style: { paddingLeft: 30 * localLevel + "rpx" },
              },
              [
                $createElement(
                  "view",
                  {
                    attrs: {
                      class: "parent-title",
                    },
                  },
                  [
                    <uni-view class="item-switch-open item-switch-close"></uni-view>,
                    <span>{item.name}</span>,
                  ]
                ),
                this.renderItemSelect(item.children, ++localLevel),
              ]
            ),
          ];
        } else {
          return $createElement(
            "view",
            {
              class: "tree-item-content",
              on: {
                click: (e) => itemContentClick(e, item),
              },
              style: { paddingLeft: 30 * localLevel + "rpx" },
            },
            [
              <text class="text-font">{item.name}</text>,
              $createElement("IconWarpCheck", {
                props: {
                  selectList,
                  data: item,
                  changeHandle: () => this.clickTreeItem(item, event),
                },
                class: "icon-warp-check",
              }),
            ]
          );
        }
      });
    },
  },
  render(h) {
    return h(
      `view`,
      {
        attrs: {
          id: "TreeView",
        },
        class: "tree-view-content",
      },
      [
        h(
          `view`,
          {
            class: "tree-view",
          },
          [this.renderItemSelect(this.treeList, 0)]
        ),
      ]
    );
  },
};
