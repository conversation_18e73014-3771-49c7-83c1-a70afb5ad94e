<script>
import IconWarpCheck from "./IconWrapCheck.vue";
import renderTree from "./Mixins/renderOrganizationalTree";

export default {
  name: "OrganizationalTreeItem",
  components: {
    IconWarpCheck,
  },
  mixins: [renderTree],
  props: {
    treeList: {
      type: Array,
      default: () => [],
    },
    eachValue: {
      type: Object,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    each(allIds) {
      let ids = allIds.split(">");
      while (ids.length > 0) {
        const selectItems = document.getElementsByClassName(ids.shift());
        if (selectItems[0]) selectItems[0].classList.remove("close");
      }
    },
    itemContentClick(e, data) {
      e.stopPropagation();

      const index = this.selectList.indexOf(data.id);
      if (index !== -1) {
        this.selectList.splice(index, 1);
      } else {
        this.selectList.splice(0);
        this.selectList.push(data.id);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tree-view {
  .icon-warp-check {
    margin-right: 32rpx;
  }

  .tree-item-content {
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.5s;
    opacity: 1;

    &.content-close {
      height: 0;
      opacity: 0;
    }

    &.active {
      background: rgba(82, 96, 255, 0.2);
    }

    .tree-item-info {
      display: flex;
      align-items: center;
      height: 100%;
      flex: 1;

      .tree-item-text {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }

  .tree-parent {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // height: 80rpx;
    padding-left: 32rpx;
    transition: all 0.5s;

    &.close {
      height: 80rpx;
      overflow: hidden;
	  
      .tree-item-content {
        display: none;
      }
    }

    .parent-title {
      .item-switch-open {
        width: 16rpx;
        height: 16rpx;
        border-top: 4rpx solid #000;
        border-left: 4rpx solid #000;
        transform: rotate(225deg);
        transition: all 0.7s;
        margin-right: 16rpx;
        display: inline-block;

        &.item-switch-close {
          transform: rotate(45deg);
        }

        &.no-switch {
          visibility: hidden;
        }
      }

      line-height: 80rpx;
      flex: 1;
      font-size: 32rpx;
      color: #333333;
      font-weight: 700;
    }
  }
}
</style>
