<template>
  <ts-popup
    mode="bottom"
    :show="typeSelectDialog"
    :safe-area-inset-bottom="true"
    @close="typeSelectDialogClose"
  >
    <view class="content">
      <view class="navbar-right">
        <span class="left" @click="typeSelectDialogClose">取消</span>
        <span></span>
        <span class="right" @click="confirm">确定</span>
      </view>

      <!-- <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入模糊查询"
        @input="searchInput"
        @clear="clearHandle"
      ></u-search> -->

      <select-from-tree
        ref="treeContent"
        :treeList="localTreeList"
        :selectList="selectList"
        :eachValue="eachValue"
      />
    </view>
  </ts-popup>
</template>

<script>
import common from "util/common.js";
import selectFromTree from "./select-from-tree.vue";

export default {
  model: {
    event: "change",
    prop: "modelValue",
  },
  props: {
    modelValue: {
      type: Boolean,
    },
    treeList: {
      type: Array,
    },
    eachValue: {
      type: Object,
    },
  },
  components: {
    selectFromTree,
  },
  data() {
    return {
      typeSelectDialog: false,
      searchInput: null,
      treeMap: {},

      localTreeList: [],
      selectList: [],
      keywords: "",
    };
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.localTreeList = common.deepClone(this.treeList);

        this.searchInput = this.debounce(this.input, 300);
        this.treeToMap(this.treeList);

        const { id } = this.eachValue;
        if (id) {
          this.selectList.push(id);
          const { allIds } = this.getSelectItemInfo(id);

          this.$nextTick(() => {
            this.$refs.treeContent.each(allIds);
          });
        }
      } else {
        this.selectList = [];
        this.localTreeList = [];
        this.searchInput = null;
      }

      this.typeSelectDialog = val;
    },
  },
  methods: {
    input() {
      let result = {};
      for (let key in this.treeMap) {
        let node = common.deepClone(this.treeMap[key]);

        if (new RegExp(this.keywords).test(node.name)) {
          result[key] = node;
          this.findParent(result, node);
          this.findChildren(result, node);
        }
      }

      let res = [];
      for (let key in result) {
        let treeItem = result[key];
        let pid = treeItem.pid;
        if (pid === "" || !pid) {
          res.push(treeItem);
        } else {
          if (!result[pid]) {
            result[pid] = {
              children: [],
            };
          }
          result[pid].children.push(treeItem);
        }
      }
      this.localTreeList = res;
    },
    clearHandle() {},
    confirm() {
      if (this.selectList && this.selectList.length) {
        const id = this.selectList[0];
        const activeItem = this.getSelectItemInfo(id);
        this.$emit("submit", activeItem);
      }
      this.typeSelectDialogClose();
    },
    getSelectItemInfo(id) {
      let result = null;
      this.treeList.forEach((item) => {
        item.allPath = item.name;
        item.allIds = item.id;
      });
      digui(this.treeList, false);
      function digui(arr, parentItem) {
        for (let i = 0; i < arr.length; i++) {
          const element = arr[i];
          if (parentItem) {
            element.allPath = parentItem.allPath + ">" + element.name;
            element.allIds = parentItem.allIds + ">" + element.id;
          }

          if (element.children && element.children.length) {
            digui(element.children, element);
          } else {
            if (element.id === id) {
              result = element;
            }
          }
        }
      }
      return result;
    },
    // 递归 讲所有id data 绑定数据上 进行搜索
    treeToMap(node) {
      for (let item of node) {
        this.treeMap[item.id] = {
          ...item,
          children: item.children ? [] : null,
        };
        if (item.children && item.children.length > 0) {
          this.treeToMap(item.children);
        }
      }
    },
    // 查找node的parent
    findParent(result, node) {
      if (node.pid && node.pid != "") {
        result[node.pid] = common.deepClone(this.treeMap[node.pid]);
        this.findParent(result, this.treeMap[node.pid]);
      }
    },
    // 查找node的children
    findChildren(result, node) {
      for (let key in this.treeMap) {
        let item = this.treeMap[key];
        if (item.pid === node.id) {
          result[item.id] = common.deepClone(item);
          this.findChildren(result, item);
        }
      }
    },
    debounce(fn, wait) {
      let timer;
      return function () {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    },
    typeSelectDialogClose() {
      this.$emit("change", false);
      this.typeSelectDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 40px 8px 8px 8px;
  position: relative;
  .navbar-right {
    width: 100%;
    height: 36px;
    color: rgb(82, 96, 255);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    padding: 0 18px;
    top: 0;
    left: 0;
    background: #fff;
  }

  /deep/ .tree-view-content {
    display: block;
    height: 300px;
    overflow: auto;
  }
}
</style>
