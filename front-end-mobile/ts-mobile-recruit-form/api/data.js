import request from "config/requset.js";

export const getHrRecruitmentTalentPoolTree = () => {
  return request({
    url: "/ts-hrms/api/HrRecruitmentTalentPool/tree",
    method: "get",
  });
};

export const getDictionaries = async (type) => {
  try {
    const res = await request({
      url: `/ts-hrms/getDictionariesByType/${type}`,
      method: "post",
    });
    if (!res.success) {
      throw Error(`${type}码表获取失败`);
    }

    return res.object;
  } catch (e) {
    console.log(e);
  }
  return "";
};

// 上传附件
export const fileAttachmentOpenUpload = function (data) {
  return request({
    method: "POST",
    url: `/ts-basics-bottom/fileAttachment/openUpload?moduleName=hrm`,
    headers: {
      "Content-Type": "multipart/form-data;",
    },
    data,
  });
};

// 查询附件 BusinessId
export const getFileAttachmentByBusinessId = function (params) {
  return request({
    method: "get",
    url: `/ts-basics-bottom/fileAttachment/getFileAttachmentByBusinessId`,
    params,
  });
};

export const getTitle = async () => {
  return request({
    url: `/ts-hrms/getJobtitleByType/list`,
    method: "post",
  });
};

export const setZpglEmployee = (data) => {
  return request({
    url: "/ts-hrms/api/zpglEmployee/save",
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
};

export const updateZpglEmployee = data => {
  return request({
    url: '/ts-hrms/api/zpglEmployee/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 招聘根据身份证查询登记信息
export const findIdentityNumber = (params) => {
  return request({
    url: "/ts-hrms/api/zpglEmployee/findIdentityNumber",
    method: "get",
    params
  });
};

export const zpglInterviewMessageSignIn = (data) => {
  return request({
    url: "/ts-hrms/api/zpglInterviewMessage/signIn",
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
};
