import request from "config/requset.js";

export const voluntariesExternalSave = function (data) {
  return request({
    method: "POST",
    url: `/ts-hrms/outerNet/voluntariesExternal/save`,
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
};

export const voluntariesActivityRecordSave = function (data) {
  return request({
    method: "POST",
    url: `/ts-hrms/outerNet/voluntariesActivityRecord/save`,
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
};

export const voluntariesActivityInfo = function (id) {
  return request({
    method: "GET",
    url: `/ts-hrms/outerNet/voluntariesActivity/${id}`,
  });
};

export const voluntariesActivityGetPersonByIdCard = function (idCard) {
  return request({
    method: "get",
    url: `/ts-hrms/outerNet/voluntariesActivity/getPersonByIdCard/${idCard}`,
  });
};
