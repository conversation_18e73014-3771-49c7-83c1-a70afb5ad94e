import axios from "axios";

let options = {
  axios,
  timeout: 120 * 1000,
  headers: {
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "x-requested-with": "XMLHttpRequest",
  },
};
let timeout = options.timeout;
let headers = options.headers;

const request = options.axios.create({
  timeout,
  headers,
  custom: {
    showLoading: false,
    loadingText: "加载中...",
    loadingMask: true,
    loadingTime: 800,
  },
});

/**@desc 在请求前做的操作**/
request.interceptors.request.use(
  (config) => {
    if (config.custom.showLoading) {
      uni.showLoading({
        title: config.custom.loadingText,
      });
    }
    return config;
  },
  (error) => {}
);
/**@desc 在请求后做的操作**/
request.interceptors.response.use(
  /**@desc 对响应数据做点什么**/
  (response) => {
    if (response.config.custom.showLoading) {
      uni.hideLoading();
    }
    return response.data;
  },
  /**@desc 对请求错误做些什么**/
  (error) => {
    return Promise.reject(error);
  }
);

export default request;
