<script>
export default {
  async onLaunch() {
    this.$onGlobalStateChange((state = {}, prev = {}) => {
      let { event, data: newVal } = state,
        oldVal = prev.data || {};
      if (event === 'equipmentStocktaking') {
        this.$eventBus.$emit('equipment-stocktaking', { result: newVal })
      }
    });
  },
  onShow: function() {},
  onHide: function() {}
};
</script>

<style lang="scss">
/*每个页面公共css */
@import '@trasen-oa/trasen-uview-ui/index.scss';
page {
  height: 100%;
}
</style>
