import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  // 字典
  getDataByDataLibrary(data) {
    return request.get(
      `${apiConfig.basics()}/dictItem/getDictItemByTypeCode?typeCode=${data}`
    );
  },

  // 盘点
  inventoryTaskUpdate(params) {
    return request.post(`${apiConfig.ams()}/api/device/inventoryTask/update`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 扫码盘点
  inventoryTaskScanUpdate(params) {
    return request.post(`${apiConfig.ams()}/api/device/inventoryTask/scanUpdate`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 盘点计划列表
  inventoryPlanList(params) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryPlan/list`, { params }
    );
  },

  //盘点任务详情
  inventoryPlanDetails(id) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryPlan/${id}`
    );
  },

  // 盘点任务 数量统计
  inventoryTaskStatusNums(id, params) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryTask/status/nums/${id}`, { params }
    );
  },

  // 盘点任务 查询相关科室
  inventoryPlanOrgList(id, params) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryPlan/orgList/${id}`,
      { params }
    );
  },

  // 盘点任务 查询相关分类
  inventoryPlanCateList(id, params) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryPlan/cateList/${id}`,
      { params }
    );
  },

  // 盘点任务 设备列表
  inventoryTaskList(id, params) {
    return request.get(
      `${apiConfig.ams()}/api/device/inventoryTask/list/${id}`, { params }
    );
  },

  // 盘点任务 批量盘点
  inventoryTaskBatchUpdate(params) {
    return request.post(`${apiConfig.ams()}/api/device/inventoryTask/batchUpdate`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 提交盘点数据
  inventoryPlanSure(id) {
    return request.post(`${apiConfig.ams()}/api/device/inventoryPlan/sure/${id}`, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  }
};
