<template>
  <u-popup
    mode="bottom"
    :mask-close-able="false"
    :mask-custom-style="{background: 'rgba(0, 0, 0, 0.3)'}"
    v-model="equipmentStocktakingDialog"
    height="100vh"
  >
    <view class="equipment-stocktaking-container">
      <!-- 扫描动画 -->
      <view class="radar-container" v-show="isScanning">
        <view class="scanning-text"> 
          <view>{{ wps === fridPds ? '已盘到所有资产' : '正在扫描' }}</view>
          <view>（ {{ wps }} / {{ fridPds }}）</view>
        </view>
        <view class="radar-scanner">
          <view class="radar-circle"></view>
          <view class="radar-circle circle-2"></view>
          <view class="radar-circle circle-3"></view>
          <view class="radar-line"></view>
        </view>
      </view>

      <view class="operate-header">
        <view class="operate-header-title">
          {{ !isScanning ? '提示: 按下手持终端按键，开始盘点。' : ''}}
        </view>

        <u-button
          class="close-btn"
          type="info"
          @click="handleEquipmentStocktakingClose"
        >
          关闭
        </u-button>
      </view>

      <view class="operate-title" v-if="Object.keys(viewStocktakingIds).length">
        盘到资产 （ {{ wps }} / {{ fridPds }}）
      </view>

      <view class="equipment-list-container">
        <scroll-view scroll-y class="equipment-list" v-if="Object.keys(viewStocktakingIds).length">
          <stocktaking-item
            :id="`list-container-item-${index + 1}`"
            v-for="(i, key, index) in viewStocktakingIds"
            :key="i.id"
            :item="i"
            :disk-to-list="DiskToList"
            renderType="success"
            showIndex
            :itemIndex="index"
            @change-ret="handleChangeStockRet"
          />
        </scroll-view>
        <view v-else class="no-data-container">
          <img src="@/assets/img/no-data.png" alt="">
          <text>暂无数据</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import StocktakingItem from '@/pages/stocktaking-details/components/stocktaking-item.vue';
import Api from '@/api/api/equipmentStocktaking.js';

// 扫描开始
let EQUIPMENT_STOCKTAKING_START = 'equipment_stocktaking_start';
// 扫描结束
let EQUIPMENT_STOCKTAKING_END = 'equipment_stocktaking_end';
// 扫描开始失败
let EQUIPMENT_START_ERROR = 'equipment_start_error';
// 扫描结束失败
let EQUIPMENT_END_ERROR = 'equipment_end_error';

export default {
  components: {
    StocktakingItem
  },
  props: {
    DiskToList: {
      type: Array
    }
  },
  data() {
    return {
      wps: 0,
      isScanning: false,
      equipmentStocktakingDialog: false,
      detailsId: '',
      viewStocktakingIds: {},
      localStocktakingIds: {},
      scanTimeout: null,
      isScanningActive: false
    };
  },

  computed: {
    fridPds() {
      return Object.keys(this.viewStocktakingIds).length || 0;
    }
  },

  methods: {
    initData() {
      this.$set(this, 'wps', 0);
      this.$set(this, 'isScanning', false);
      this.$set(this, 'equipmentStocktakingDialog', false);
      this.$set(this, 'detailsId', '');
      this.$set(this, 'viewStocktakingIds', {});
      this.$set(this, 'localStocktakingIds', {});
      this.$set(this, 'scanTimeout', null);
      this.$set(this, 'isScanningActive', false);
    },

    async open({ detailsId, wps, totalHeight }) {
      this.initData();
      // this.$nextTick(() => {
      //   this.setComponentHeights(totalHeight);
      // });

      this.wps = wps;
      this.detailsId = detailsId;
      this.equipmentStocktakingDialog = true;

      uni.webView.postMessage({
        data: {
          action: 'WATCH_KEYDOWN',
          params: {}
        }
      });
      this.$eventBus.$on('equipment-stocktaking', this.handleEquipmentStocktaking);
    },

    handleEquipmentStocktaking(data) {
      console.log(data.result, '?data.result');
      switch (data.result) {
        case EQUIPMENT_START_ERROR:
          this.stopScanning();
          uni.showToast({
            title: 'FRID扫描启动失败，请联系管理员!',
            icon: 'none'
          });
          break;
        case EQUIPMENT_END_ERROR:
          this.stopScanning();
          uni.showToast({
            title: 'FRID扫描结束失败，请联系管理员!',
            icon: 'none'
          });
          break;

        case EQUIPMENT_STOCKTAKING_START:
          this.startScanning();
          break;

        case EQUIPMENT_STOCKTAKING_END:
          this.stopScanning();
          break;

        default:
          this.handleStocktakingData(data);
          break;
      }
    },

    startScanning() {
      this.$set(this, 'localStocktakingIds', {});

      this.isScanning = true;
      this.isScanningActive = true;
      this.startScanningLoop();
    },

    stopScanning() {
      this.isScanning = false;
      this.isScanningActive = false;
      if (this.scanTimeout) {
        clearTimeout(this.scanTimeout);
        this.scanTimeout = null;
      }
    },

    async startScanningLoop() {
      if (!this.isScanningActive) return;

      try {
        await this.autoFridUpdateInventoryTask();
      } catch (error) {
        this.showError('扫描出错，请重试');
        this.stopScanning();
        return;
      }

      // 设置下一次请求
      if (this.isScanningActive) {
        this.scanTimeout = setTimeout(() => {
          this.startScanningLoop();
        }, 400);
      }
    },

    handleStocktakingData(data) {
      if (data.result) {
        const ids = data.result.split(',');
        ids.forEach((id) => {
          if (this.localStocktakingIds[id] === undefined) {
            this.$set(this.localStocktakingIds, id, {});
          }
        });
      }
    },

    async autoFridUpdateInventoryTask() {
      let localIds = Object.keys(this.localStocktakingIds);
      console.log(localIds, '?localIds');
      let viewIds = Object.keys(this.viewStocktakingIds);
      console.log(viewIds, '?viewIds');

      let assetCodeList = localIds.filter(id => !viewIds.includes(id));
      if (!assetCodeList.length) return;

      console.log(assetCodeList, '?assetCodeList');
      try {
        let res = await Api.inventoryTaskBatchUpdate({
          inventoryPlanId: this.detailsId,
          ret: '1',
          status: '1',
          assetCodeList
        });
        console.log(JSON.stringify(res));
        if (!res.success) {
          throw new Error(res.message || '盘点失败');
        }

        let data = res?.object || [];
        data.forEach(item => {
          if (this.viewStocktakingIds[item.assetCode] === undefined) {
            this.$set(this.viewStocktakingIds, item.assetCode, item);
          }
        });

        this.$nextTick(() => {
          let index = Object.keys(this.viewStocktakingIds).length;
          const element = document.getElementById(`list-container-item-${index}`);
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      } catch (error) {
        throw error;
      }
    },

    // 更改盘点结果
    async handleChangeStockRet(ret, row) {
      if (row.ret === ret) return;

      try {
        const data = {
          id: row.id,
          inventoryPlanId: this.detailsId,
          status: row.status,
          ret
        };

        await this.updateInventoryTask(data, '更新');
        row.ret = ret;
      } catch (error) {
        this.showError('操作失败');
      }
    },

    async updateInventoryTask(data, operationType) {
      const res = await Api.inventoryTaskUpdate(data);
      
      if (res.success && res.statusCode === 200) {
        this.showSuccess(`${operationType}成功`);
      } else {
        this.showError(res.message || `${operationType}失败`);
      }
    },

    // setComponentHeights(totalHeight) {
    //   const equipmentContainer = document.querySelector('.equipment-stocktaking-container');
    //   if (equipmentContainer) {
    //     equipmentContainer.style.height = `calc(100vh - ${totalHeight}px - 16px)`;
    //   }
    // },

    handleEquipmentStocktakingClose() {
      this.$emit('refresh');
      this.stopScanning();
      this.initData();

      uni.webView.postMessage({
        data: {
          action: 'END_WATCH_KEYDOWN',
          params: {}
        }
      });
      this.equipmentStocktakingDialog = false;

      this.$eventBus.$off('equipment-stocktaking', this.handleEquipmentStocktaking);
    },

    showSuccess(message) {
      uni.showToast({
        title: message,
        icon: 'success'
      });
    },

    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.equipment-stocktaking-container {
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;

  .operate-header {
    background: linear-gradient(135deg, #4299e1, #667eea);
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    overflow: hidden;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .operate-header-title {
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      color: #ffffff;
      letter-spacing: 1rpx;
    }

    .close-btn {
      height: 54rpx;
      border-radius: 8rpx;
      margin: 0;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(8px);
      color: #fff;
      
      &::after {
        border: none;
      }
    }
  }

  .operate-title {
    padding: 12rpx 20rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }

  .equipment-list-container {
    flex: 1;
    overflow: auto;
    background-color: #e1e1e1;
    padding: 0 16rpx 16rpx 16rpx;

    .equipment-list {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      background-color: transparent;
      overflow: auto;
    }

    .no-data-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 540rpx;
        height: 240rpx;
        margin-bottom: 40rpx;
      }
      text {
        font-size: 36rpx;
        color: #8b8b8b;
        font-weight: 800;
      }
    }
  }
}

.radar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 200rpx;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  .scanning-text {
    color: #fff;
    font-size: 36rpx;
    font-weight: bold;
    text-shadow: 0 0 10rpx rgba(54, 209, 220, 0.8);
    animation: blink 1s infinite;
    text-align: center;
  }
  .radar-scanner {
    position: relative;
    margin-top: 50rpx;
    width: 500rpx;
    height: 500rpx;
    .radar-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      border: 8rpx solid rgba(91, 134, 229, 0.6);
      border-radius: 50%;
      animation: pulse 2s infinite;
      &.circle-2 {
        animation-delay: 0.5s;
      }

      &.circle-3 {
        animation-delay: 1s;
      }
    }
    .radar-line {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 50%;
      height: 8rpx;
      background: linear-gradient(90deg, rgb(54, 209, 220), transparent);
      transform-origin: left;
      animation: scan 2s linear infinite;
      box-shadow: 0 0 15rpx rgb(54, 209, 220);
    }
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.3);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

@keyframes scan {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>