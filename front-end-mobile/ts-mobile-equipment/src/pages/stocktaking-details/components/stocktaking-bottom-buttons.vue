<template>
  <view class="bottom-btn">
    <u-button
      class="operate-btn"
      type="primary"
      @click.stop="$emit('scan')"
    >
      扫描盘点
    </u-button>

    <u-button
      class="operate-btn"
      type="primary"
      @click.stop="$emit('auto')"
    >
      自动盘点
    </u-button>
  </view>
</template>

<script>
export default {
  name: 'StocktakingBottomButtons',

  emits: ['scan', 'auto']
};
</script>

<style lang="scss" scoped>
.bottom-btn {
  display: flex;
  justify-content: space-between;
  padding: 16rpx;
  background: #fff;
  border-top: 1px solid #f5f6fa;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);

  .operate-btn {
    width: 160rpx;
    height: 68rpx;
    font-size: 34rpx;
  }
}
</style> 