<template>
  <view class="list-item">
    <view class="item-card">
      <view class="item-header">
        <text class="item-title">{{ showIndex ? `${itemIndex + 1}` : '' }} {{ item.name }}</text>
        <text class="item-code">{{ item.assetCode }}</text>
      </view>
      
      <view class="item-info">
        <view class="info-row">
          <text class="label">品牌：</text>
          <text class="value">{{ item.brandName }}</text>
        </view>
        <view class="info-row">
          <text class="label">型号：</text>
          <text class="value">{{ item.model }}</text>
        </view>
        <view class="info-row">
          <text class="label">所属科室：</text>
          <text class="value">{{ item.belongToOrgName }}</text>
          <text class="label">启用日期：</text>
          <text class="value">{{ formattedDate }}</text>
        </view>

        <view class="info-row nom" v-show="renderType === 'default'">
          <text class="label">手动盘点：</text>
          <view class="value manual-inventory">
            <view
              @tap="() => handleStatusChange('2')"
              :class="{'fail-item': true, 'active': item.status === '2', 'tab-nomal': index === 1}"
            >
              盘亏
            </view>

            <view
              @tap="() => handleStatusChange('1')"
              :class="{'success-item': true, 'active': item.status === '1', 'tab-nomal': index === 1}"
            >
              盘到
            </view>
          </view>
        </view>

        <view class="info-row nom" v-show="item.status">
          <text class="label"></text>
          <view class="value manual-inventory">
            <template v-if="item.status === '1'">
              <view
                v-for="option in diskToList"
                :key="option.value"
                @tap="() => handleRetChange(option.value)"
                :class="{'ret-item': true, 'suc-active': item.ret === option.value}"
              >
                {{ option.label }}
              </view>
            </template>

            <template v-if="item.status === '2'">
              <view
                v-for="option in inventoryLossList"
                :key="option.value"
                @tap="() => handleRetChange(option.value)"
                :class="{'ret-item': true, 'err-active': item.ret === option.value}"
              >
                {{ option.label }}
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'StocktakingItem',

  props: {
    showIndex: {
      type: Boolean,
    },
    renderType: {
      type: String,
      default: 'default'
    },
    index: {
      type: Number,
    },
    itemIndex: {
      type: Number,
    },
    item: {
      type: Object,
      required: true
    },
    diskToList: {
      type: Array,
      default: () => []
    },
    inventoryLossList: {
      type: Array,
      default: () => []
    }
  },

  computed: {
    formattedDate() {
      return this.item.useDate ? dayjs(this.item.useDate).format('YYYY-MM-DD') : '';
    }
  },

  methods: {
    handleStatusChange(status) {
      this.$emit('change-status', status, this.item);
    },

    handleRetChange(ret) {
      this.$emit('change-ret', ret, this.item);
    }
  }
};
</script>

<style lang="scss" scoped>
.list-item {
  margin-top: 16rpx;
  
  .item-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      
      .item-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
      
      .item-code {
        font-size: 32rpx;
        color: #007aff;
        font-weight: 500;
      }
    }
    
    .item-info {
      .info-row {
        display: flex;
        margin-bottom: 12rpx;

        &.nom {
          margin-bottom: 0;
        }
        
        .label {
          width: 150rpx;
          color: #666;
          font-size: 30rpx;
        }
        
        .value {
          flex: 1;
          color: #333;
          font-size: 30rpx;

          &.manual-inventory {
            display: flex;
            flex-wrap: wrap;

            .fail-item {
              border-radius: 8rpx;
              padding: 4rpx 22rpx;
              font-size: 28rpx;
              border: 2rpx solid #D43023;
              color: #D43023;
              margin-right: 16rpx;

              &.active {
                background-color: #D43023;
                color: #fff !important;
              }

              &.tab-nomal {
                border-color: #cccccc;
                color: #333;
              }
            }

            .success-item {
              border-radius: 8rpx;
              padding: 4rpx 22rpx;
              font-size: 28rpx;
              border: 2rpx solid #007aff;
              color: #007aff;
              &.active {
                background-color: #007aff;
                color: #fff !important;
              }
              &.tab-nomal {
                border-color: #cccccc;
                color: #333;
              }
            }

            .ret-item {
              border-radius: 8rpx;
              padding: 4rpx 22rpx;
              font-size: 28rpx;
              border: 2rpx solid #cccccc;
              color: #333;
              margin-right: 16rpx;
              margin-top: 16rpx;
              position: relative;

              &.suc-active {
                padding-left: 50rpx;
                
                &::after {
                  content: '';
                  position: absolute;
                  left: 16rpx;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 24rpx;
                  height: 24rpx;
                  border: 2rpx solid #007aff;
                  border: 2rpx solid #007aff;
                  background-color: #007aff;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='%23ffffff' stroke='%23ffffff' stroke-width='2' d='M9.55 18l-5.7-5.7 1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4z'/%3E%3C/svg%3E");
                  background-repeat: no-repeat;
                  background-position: center;
                  background-size: 16rpx;
                }
              }

              &.err-active {
                padding-left: 50rpx;
                
                &::after {
                  content: '';
                  position: absolute;
                  left: 16rpx;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 24rpx;
                  height: 24rpx;
                  border: 2rpx solid #D43023;
                  background-color: #D43023;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='%23ffffff' stroke='%23ffffff' stroke-width='2' d='M9.55 18l-5.7-5.7 1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4z'/%3E%3C/svg%3E");
                  background-repeat: no-repeat;
                  background-position: center;
                  background-size: 16rpx;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style> 