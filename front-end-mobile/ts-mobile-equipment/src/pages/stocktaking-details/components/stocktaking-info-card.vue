<template>
  <view class="info-card">
    <view class="title-row">
      <view class="title-name">{{ details.name }}</view>
      <view class="expand-btn" @click="toggleExpand">
        <text>{{ isExpanded ? '收起' : '展开' }}</text>
        <text class="icon" :class="{ 'icon-up': isExpanded }">▼</text>
      </view>
    </view>

    <view class="info-content" :class="{ 'collapsed': !isExpanded }">
      <view class="info-item">
        <text class="label">盘点科室：</text>
        <view class="value">
          <text 
            v-for="(item) in planUseOrg" 
            :key="item.id"
            :class="{ 'active': orgId === item.id }"
            @click="handleOrgClick(item)"
          >
            {{ item.name }}({{ item.nums }})
          </text>
        </view>
      </view>

      <view class="info-item">
        <text class="label">盘点分类：</text>
        <view class="value">
          <text 
            v-for="(item) in planUseType" 
            :key="item.id"
            :class="{ 'active': cateId === item.id }"
            @click="handleTypeClick(item)"
          >
            {{ item.name }}({{ item.nums }})
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StocktakingInfoCard',

  props: {
    details: {
      type: Object,
      required: true,
      default: () => ({})
    },

    planUseOrg: {
      type: Array,
      required: true,
      default: () => ([])
    },

    planUseType: {
      type: Array,
      required: true,
      default: () => ([])
    },

    rendenTypeProp: {
      type: String,
      required: true
    },

    orgId: {
      type: String,
      default: ''
    },

    cateId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      isExpanded: false
    }
  },

  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },

    handleOrgClick(item) {
      this.$emit('update:orgId', this.orgId === item.id ? '' : item.id);
    },

    handleTypeClick(item) {
      this.$emit('update:cateId', this.cateId === item.id ? '' : item.id);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';

.info-card {
  background: #fff;
  margin: 12rpx;
  padding: 12rpx;
  border-radius: 8px;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    padding-bottom: 12rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }

  .title-name {
    color: #333;
    font-weight: 600;
    font-size: 34rpx;
    @include multiLineEllipsis(2);
    flex: 1;
    margin-right: 16rpx;
  }

  .expand-btn {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #666;
    padding: 6rpx 16rpx;
    background: #f8f8f8;
    border-radius: 24rpx;
    
    .icon {
      margin-left: 6rpx;
      font-size: 22rpx;
      transition: transform 0.3s ease;
      
      &.icon-up {
        transform: rotate(180deg);
      }
    }
  }

  .info-content {
    &.collapsed {
      .info-item {
        .value {
          @include multiLineEllipsis(1);
        }
      }
    }
  }

  .info-item {
    margin-bottom: 8rpx;    
    font-size: 30rpx;
    display: flex;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 150rpx;
      flex-shrink: 0;
      color: #666;
    }

    .value {
      flex: 1;
      color: #333;
      word-break: break-all;
      
      text {
        display: inline-block;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
        margin-right: 8rpx;
        margin-bottom: 8rpx;
        background: #f5f7fa;
        border: 1rpx solid #f5f7fa;

        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:nth-child(odd) {
          background: #f0f2f5;
        }
        
        &:last-child {
          margin-right: 0;
        }

        &.active {
          background: #e6f7ff;
          color: #1890ff;
          border: 1rpx solid #91d5ff;
        }
      }
    }
  }
}
</style> 