<template>
  <u-popup
    mode="bottom"
    :mask-close-able="false"
    :mask-custom-style="{background: 'rgba(0, 0, 0, 0.3)'}"
    v-model="equipmentStocktakingDialog"
    safe-area-inset-bottom
    border-radius="24"
  >
    <view class="equipment-stocktaking-container">
      <view class="operate-header">
        <u-button
          class="btn continue-btn"
          type="primary"
          @click="handleContinueInventory"
        >
          继续盘点
        </u-button>

        <u-button
          class="btn close-btn"
          type="info"
          @click="handleEquipmentStocktakingClose"
        >
          关闭
        </u-button>
      </view>

      <view class="equipment-list-container">
        <view class="close-tips">
          <text></text>
          <text>提示：弹窗{{ second }}秒后自动关闭</text>
        </view>

        <scroll-view scroll-y class="equipment-list">
          <stocktaking-item
            :id="`list-container-item-${index + 1}`"
            v-for="(i, key, index) in viewStocktakingIds"
            :key="i.id"
            :item="i"
            :disk-to-list="DiskToList"
            renderType="success"
            @change-ret="handleChangeStockRet"
          />
        </scroll-view>

      </view>
  </view>
  </u-popup>
</template>

<script>
import StocktakingItem from '@/pages/stocktaking-details/components/stocktaking-item.vue';
import Api from '@/api/api/equipmentStocktaking.js';
export default {
  components: {
    StocktakingItem
  },
  props: {
    DiskToList: {
      type: Array
    }
  },
  data() {
    return {
      equipmentStocktakingDialog: false,
      second: 8, // 倒计时秒数
      timer: null, // 定时器

      detailsId: '',
      viewStocktakingIds: {},
    };
  },
  methods: {
    initData() {
      this.$set(this, 'equipmentStocktakingDialog', false);
      this.$set(this, 'second', 8);
      this.$set(this, 'timer', null);

      this.$set(this, 'detailsId', '');
      this.$set(this, 'viewStocktakingIds', {});
    },

    async open({ echoData, detailsId }) {
      this.initData();

      this.detailsId = detailsId;
      if (Array.isArray(echoData)) {
        echoData.forEach(i => {
          this.$set(this.viewStocktakingIds, [i.id], i)
        })
      }
      this.equipmentStocktakingDialog = true;

      this.timer = setInterval(() => {
        this.second--;
        if (this.second <= 0) {
          this.handleEquipmentStocktakingClose();
        }
      }, 1000);
    },

    // 更改盘点结果
    async handleChangeStockRet(ret, row) {
      if (row.ret === ret) return;

      try {
        const data = {
          id: row.id,
          inventoryPlanId: this.detailsId,
          status: row.status,
          ret
        };

        await this.updateInventoryTask(data, '更新');
        row.ret = ret;
      } catch (error) {
        this.showError('操作失败');
      }
    },

    async updateInventoryTask(data, operationType) {
      const res = await Api.inventoryTaskUpdate(data);
      
      if (res.success && res.statusCode === 200) {
        this.showSuccess(`${operationType}成功`);
      } else {
        this.showError(res.message || `${operationType}失败`);
      }
    },

    handleContinueInventory() {
      this.handleEquipmentStocktakingClose();

      setTimeout(() => {
        this.$emit('continue');
      }, 1500);
    },

    handleEquipmentStocktakingClose() {
      clearInterval(this.timer);
      this.$emit('refresh');
      this.initData();
    },

    showSuccess(message) {
      uni.showToast({
        title: message,
        icon: 'success'
      });
    },

    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.equipment-stocktaking-container {
  background: #ffffff;
  display: flex;
  flex-direction: column;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  
  .operate-header {
    width: 100%;
    height: 90rpx;
    padding: 0 20rpx;
    overflow: hidden;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn {
      height: 64rpx;
      border-radius: 8rpx;
      margin: 0;
      background: rgba(234, 231, 231, 0.35);
      border: 2px solid #eee;
      font-weight: 500;
      &.continue-btn {    
        background: linear-gradient(135deg, #4299e1, #667eea);
        &::after {
          border: none;
        }
      }
      &.close-btn {
        &::after {
          border: none;
        }
      }
    }
  }

  .operate-title {
    padding: 12rpx 20rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }

  .equipment-list-container {
    flex: 1;
    overflow: auto;
    background-color: #e1e1e1;
    padding: 0 16rpx 16rpx 16rpx;

    .close-tips {
      margin-top: 16rpx;
      color: red;
      display: flex;
      justify-content: space-between;
    }

    .equipment-list {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      background-color: transparent;
      overflow: auto;
    }
  }
}
</style>