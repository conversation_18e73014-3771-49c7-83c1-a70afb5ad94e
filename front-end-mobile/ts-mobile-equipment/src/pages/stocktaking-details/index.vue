<template>
  <view class="ts-container">
    <u-navbar
      ref="navbar"
      title="盘点任务详情"
      title-bold
      title-width="500"
      :custom-back="goBack"
    >
      <template #right>
        <u-button
          class="submit-btn"
          type="primary"
          @click.stop="handleSubmit"
          v-show="!isTaskEnd"
        >
          提交盘点数据
        </u-button>
      </template>
    </u-navbar>
    
    <!-- 基本信息卡片组件 -->
    <stocktaking-info-card
      ref="infoCard"
      :details="details"
      :planUseOrg="planUseOrg"
      :planUseType="planUseType"
      :renden-type-prop="rendenTypeProp"
      :org-id.sync="orgId"
      :cate-id.sync="cateId"
    />

    <!-- Tab切换组件 -->
    <base-tabs-swiper
      ref="tabSwiper"
      class="tab-swiper-box"
      :list="tabList"
      badgeType="text"
      :font-size="28"
      :current="currentTab"
      :is-scroll="false"
      @change="changeTab"
    />

    <!-- 列表内容区域 -->
    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <mescroll
          :ref="`mescroll${index}`"
          :mescrollIndex="index"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <stocktaking-item
            v-for="(i, itemIndex) in item.list"
            showIndex
            :index="index"
            :itemIndex="itemIndex"
            :key="i.id"
            :item="i"
            :disk-to-list="DiskToList"
            :inventory-loss-list="InventoryLossList"
            @change-status="handleChangeStockStatus"
            @change-ret="handleChangeStockRet"
          />
        </mescroll>
      </swiper-item>
    </swiper>

    <!-- 底部操作按钮 -->
    <stocktaking-bottom-buttons
      v-if="currentTab !== 1 && !isTaskEnd && tabList[0].count > 0"
      @scan="handleScanInventory"
      @auto="handleAutoInventory"
    />

    <dialog-equipment-frid
      :DiskToList="DiskToList"
      ref="DialogEquipmentFrid"
      @refresh="handleRefreshStockEndData"
    />

    <dialog-scan-inventory
      :DiskToList="DiskToList"
      ref="DialogScanInventory"
      @refresh="handleRefreshStockEndData"
      @continue="handleScanInventory"
    />
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import DialogEquipmentFrid from './components/dialog-equipment-frid.vue';
import DialogScanInventory from './components/dialog-scan-inventory.vue';
import StocktakingInfoCard from './components/stocktaking-info-card.vue';
import StocktakingItem from './components/stocktaking-item.vue';
import StocktakingBottomButtons from './components/stocktaking-bottom-buttons.vue';
import Api from '@/api/api/equipmentStocktaking.js';
import wxInit from '@/common/wxInit.js';

export default {
  name: 'StocktakingDetails',
  
  components: {
    mescroll,
    DialogEquipmentFrid,
    DialogScanInventory,
    StocktakingInfoCard,
    StocktakingItem,
    StocktakingBottomButtons
  },

  data() {
    return {
      currentTab: 0,
      tabList: [
        {
          name: '待盘点',
          type: 1,
          count: 0,
          statusExt: '0',
          list: []
        },
        {
          name: '已盘点',
          type: 2,
          count: 0,
          statusExt: '1',
          list: []
        },
      ],

      DiskToList: [],
      InventoryLossList: [],

      detailsId: '',
      details: {},
      planUseOrg: [],
      planUseType: [],
      orgId: '',
      cateId: '',
      parentIndex: '',
    };
  },

  computed: {
    rendenTypeProp() {
      return this.details.skuType === '0' ? 'cate22NameSet' : 'cateNameSet';
    },

    isTaskEnd() { // 任务已结束
      return this.details.status === '2';
    }
  },

  async onLoad({ id, parentIndex }) {
    if (!id) {
      this.showError('缺少必要参数');
      return;
    }

    this.detailsId = id;
    this.parentIndex = parentIndex;
    await this.initPageData();
    if (this.isTaskEnd) this.currentTab = 1;
    this.getWxJsapiSignature();
  },

  watch: {
    currentTab: {
      async handler(val) {
        // 刷新列表数据
        this.refreshCurrentList();

        try {
          // 刷新 科室、分类数据
          await Promise.all([
            this.handleInventoryPlanOrgList(),
            this.handleInventoryPlanCateList(),
          ]);
        } catch (error) {
          this.showError('页面初始化失败');
        }
      }
    },

    orgId: {
      async handler(val) {
        this.cateId = '';
        await Promise.all([
          this.refreshCurrentList(),
          this.handleInventoryTaskStatusNums(),
          this.handleInventoryPlanCateList()
        ]);
      }
    },

    cateId: {
      async handler(val) {
        await Promise.all([
          this.refreshCurrentList(),
          this.handleInventoryTaskStatusNums()
        ]);
      }
    },
  },

  methods: {
    async initPageData() {
      try {
        await Promise.all([
          this.getTypeDicList(),

          this.handleInventoryPlanDetails(),

          this.handleInventoryPlanOrgList(),
          this.handleInventoryPlanCateList(),

          this.handleInventoryTaskStatusNums()
        ]);
      } catch (error) {
        this.showError('页面初始化失败');
      }
    },

    // FRID盘点结束后 
    async handleRefreshStockEndData() {
      try {
        await Promise.all([
          this.refreshCurrentList(),
          this.handleInventoryPlanOrgList(),
          this.handleInventoryPlanCateList(),
          this.handleInventoryTaskStatusNums()
        ]);
      } catch (error) {
        this.showError('页面刷新失败');
      }
    },

    // 获取类型字典列表
    async getTypeDicList() {
      try {
        const res = await Api.getDataByDataLibrary('AMS_INVENTORY_TASK_RET');
        if (!res.success) {
          this.showError(res.message || '资产状态数据获取失败');
          return;
        }

        const items = (res.object || []).map(item => ({
          element: 'ts-option',
          label: item.itemName,
          value: item.itemNameValue
        }));

        this.DiskToList = items.filter(m => m.value !== '5');
        this.InventoryLossList = items.filter(m => m.value === '5');
      } catch (error) {
        throw error;
      }
    },
    
    // 获取盘点计划详情
    async handleInventoryPlanDetails() {
      try {
        const res = await Api.inventoryPlanDetails(this.detailsId);
        if (!res.success) {
          this.showError(res.message || '获取盘点任务详情失败');
          return;
        }
        this.details = res?.object || {};
      } catch (error) {
        throw error;
      }
    },
    
    // 获取盘点计划相关科室
    async handleInventoryPlanOrgList() {
      try {
        const res = await Api.inventoryPlanOrgList(this.detailsId, { statusExt: this.tabList[this.currentTab].type });
        if (!res.success) {
          this.showError(res.message || '获取盘点计划相关科室失败');
          return;
        }
        this.planUseOrg = res.object || [];
      } catch (error) {
        throw error;
      }
    },
    
    // 获取盘点计划相关分类
    async handleInventoryPlanCateList() {
      try {
        const res = await Api.inventoryPlanCateList(this.detailsId, { statusExt: this.tabList[this.currentTab].type, orgId: this.orgId });
        if (!res.success) {
          this.showError(res.message || '获取盘点计划相关分类失败');
          return;
        }
        this.planUseType = res.object || [];
      } catch (error) {
        throw error;
      }
    },

    // 获取盘点任务状态数量
    async handleInventoryTaskStatusNums() {
      try {
        const res = await Api.inventoryTaskStatusNums(this.detailsId, { orgId: this.orgId, cateId: this.cateId });
        if (!res.success) {
          this.showError(res.message || '获取盘点任务数量明细失败');
          return;
        }
        const { wps = 0, yps = 0 } = res?.object || {};
        this.updateTabCounts({ wps, yps });
      } catch (error) {
        throw error;
      }
    },

    // 更改盘点状态
    async handleChangeStockStatus(status, row) {
      if (this.isTaskEnd) return;

      if (status === '1') {
        row.status = status;
        return;
      }

      try {
        await this.updateInventoryTask({
          id: row.id,
          inventoryPlanId: this.detailsId,
          status,
          ret: '5'
        }, '盘点', row);
      } catch {
        this.showError('操作失败');
      }
    },

    // 更改盘点结果
    async handleChangeStockRet(ret, row) {
      // 任务已结束 或 结果未变化时直接返回
      if (this.isTaskEnd || row.ret === ret) return;
      try {
        await this.updateInventoryTask({
          id: row.id,
          inventoryPlanId: this.detailsId,
          status: row.status,
          ret
        }, '更新', row);
      } catch (error) {
        this.showError('操作失败');
      }
    },

    async updateInventoryTask(data, operationType, row) {
      const res = await Api.inventoryTaskUpdate(data);
      
      if (res.success && res.statusCode === 200) {
        this.showSuccess(`${operationType}成功`);
        if (this.currentTab === 0) {
          await Promise.all([
            this.handleInventoryTaskStatusNums(),
            this.refreshCurrentList(),
            this.handleInventoryPlanOrgList(),
            this.handleInventoryPlanCateList(),
          ]);
        } else {
          row.status = data.status;
          row.ret = data.ret;
        }
      } else {
        this.showError(res.message || `${operationType}失败`);
      }
    },
    
    // Tab相关方法
    changeTab(index) {
      this.currentTab = index;
    },

    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },

    updateTabCounts({ wps, yps }) {
      this.tabList = this.tabList.map(item => ({
        ...item,
        count: item.type === 1 ? Number(wps) || 0 : Number(yps) || 0
      }));
    },

    // 列表数据相关方法
    async getListData(page, successCallback, errorCallback, index) {
      try {
        const res = await Api.inventoryTaskList(this.detailsId, {
          pageNo: page.num,
          pageSize: page.size,
          statusExt: this.tabList[index].statusExt,
          orgId: this.orgId,
          cateId: this.cateId,
        });
        successCallback(res.rows, res.totalCount, index);
      } catch (err) {
        console.error('Failed to fetch list data:', err);
        errorCallback();
      }
    },

    setListData(rows, totalCount, index) {
      this.tabList[index].list = [...this.tabList[index].list, ...rows];
    },

    datasInit(index) {
      this.tabList[index].list = [];
    },

    async handleSubmit() {
      try {
        const unprocessedAssets = Number(this.tabList[0].count);
        const title = unprocessedAssets > 0 
          ? `当前盘点任务中还有${unprocessedAssets}个资产没有盘到，会默认盘亏处理，您确认提交盘点数据吗？`
          : '您是否确认提交当前盘点任务？';

        const { confirm } = (await uni.showModal({
          title: '提示',
          content: title,
          confirmColor: '#2979ff'
        }))[1];

        if (!confirm) return;

        const res = await Api.inventoryPlanSure(this.detailsId);
        
        if (!res.success || res.statusCode !== 200) {
          throw new Error(res.message);
        }

        this.showSuccess('提交成功');
        uni.reLaunch({
          url: '/pages/equipment-stocktaking/index?fromPage=workBench&index=1'
        });

      } catch (err) {
        this.showError(err.message || '提交失败, 请联系管理员!');
      }
    },

    // 刷新特定tabs列表
    async refreshCurrentList() {
      await this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },

    // 扫码盘点
    async handleScanInventory() {
      wxInit.scanQRCode(async qrRes => {
        if (qrRes.err_Info !== 'success') return;
        const assetCode = qrRes.resultStr;
        let res = await Api.inventoryTaskScanUpdate({
          inventoryPlanId: this.detailsId,
          ret: '1',
          status: '1',
          assetCode
        });
        if (!res.success) {
          const { confirm } = (await uni.showModal({
            title: '盘点异常通知',
            content: '当前资产不在本次盘点任务列表中，无需盘点!',
            showCancel: true,
            cancelText: '继续盘点', 
            confirmText: '关闭',
            confirmColor: '#333',
            cancelColor: '#2979ff'
          }))[1];
          // 点击继续盘点按钮
          if (!confirm) this.handleScanInventory();
        } else {
          this.$refs.DialogScanInventory.open({
            detailsId: this.detailsId,
            echoData: [res.object]
          });
        }
      });
    },

    // 打开设备弹窗
    handleAutoInventory() {
      this.$refs.DialogEquipmentFrid.open({
        wps: this.tabList[0].count,
        detailsId: this.detailsId,
      });
    },

    showSuccess(message) {
      uni.showToast({
        title: message,
        icon: 'success'
      });
    },

    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },

    //获取签名信息并初始化jdk
    async getWxJsapiSignature() {
      await this.ajax
        .getWxJsapiSignature({
          REFERER: this.$store.state.common.token,
        })
        .then((res) => {
          wxInit.initwxJdk(res.object);
        });
    },

    goBack() {
      uni.reLaunch({
        url: `/pages/equipment-stocktaking/index?fromPage=workBench&index=${this.parentIndex}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/ellipsis.scss';

.ts-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;

  .submit-btn {
    height: 64rpx;
    line-height: 60rpx;
    font-size: 34rpx;
    border-radius: 8rpx;
    background-color: #007aff;
    color: #fff;
    padding: 8rpx 16rpx;
    margin-right: 16rpx;
  }
}

.swiper-box {
  flex: 1;
  background-color: #e1e1e1;

  /deep/ .mescroll-wxs-content {
    padding: 0 12rpx;
    padding-bottom: 12rpx !important;
  }
}
</style>
