<template>
  <view class="plan-item" @click.stop="onClick">
    <view class="item-top">
      <text class="item-name">{{ item.name }}</text>
      <view class="status-tag" :class="getStatusClass(item.statusShow)">
        {{ item.statusShow }}
      </view>
    </view>
    <view class="item-info">
      <view class="info-row">
        <text class="info-label">盘点科室：</text>
        <text class="info-value">{{ item.orgNameSet }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">盘点分类：</text>
        <text class="info-value">{{ item[rendenTypeProp] }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">盘点日期：</text>
        <text class="info-value">{{ item.startAt }} 至 {{ item.endAt }}</text>
      </view>
      <view class="flex">
        <view class="info-row wull">
          <text class="info-label">负责人：</text>
          <text class="info-value">{{ item.engineerNameSet }}</text>
        </view>
        <view class="info-row wull">
          <text class="info-label">完成时间:</text>
          <text class="info-value nowarp">{{ item.doneDate | dateFormatter }}</text>
        </view>
      </view>
      <view class="flex">
        <view class="info-row wull">
          <text class="info-label">创建人：</text>
          <text class="info-value">{{ item.createUserName }}</text>
        </view>
        <view class="info-row wull">
          <text class="info-label">创建日期：</text>
          <text class="info-value nowarp">{{ item.createDate | dateFormatter }}</text>
        </view>
      </view>
    </view>
    <view class="quantity-section">
      <view class="quantity-item">
        <text class="quantity-label">资产总数</text>
        <text class="quantity-value all">{{ item.devices }}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">盘到数量</text>
        <text class="quantity-value success">{{ item.pds }}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">遗失数量</text>
        <text class="quantity-value error">{{ item.pks }}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">异常数量</text>
        <text class="quantity-value warning">{{ item.ycs }}</text>
      </view>
    </view>
    <view class="operation-section">
      <slot name="bottomRight"></slot>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'plan-item',
  props: {
    item: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  filters: {
    dateFormatter: (val) => {
      return val ? dayjs(val).format('YYYY-MM-DD') : '';
    }
  },
  computed: {
    rendenTypeProp() {
      return this.item.skuType === '0' ? 'cate22NameSet' : 'cateNameSet';
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    },

    getStatusClass(status) {
      const statusMap = {
        '未开始': 'status-cancelled',
        '进行中': 'status-processing',
        '已结束': 'status-completed'
      };
      return statusMap[status] || '';
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
@import '../../../assets/css/flex.scss';

.plan-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.item-top {
  @include vue-flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;

  .item-name {
    font-size: 36rpx;
    font-weight: bold;
    color: $uni-text-color;
  }

  .status-tag {
    font-size: 28rpx;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-weight: 500;

    &.status-processing {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    &.status-completed {
      background-color: #f6ffed;
      color: #52c41a;
    }

    &.status-cancelled {
      background-color: #f5f5f5;
      color: #8c8c8c;
    }
  }
}

.item-info {
  margin-bottom: 16rpx;

  .flex {
    display: flex;
    justify-content: space-between;
  }

  .info-row {
    display: flex;
    margin-bottom: 8rpx;
    font-size: 30rpx;
    line-height: 1.5;

    &.wull {
      width: 50%;
    }

    .info-label {
      color: $uni-text-color-grey;
      width: 140rpx;
      white-space: nowrap;
    }

    .info-value {
      flex: 1;
      color: $uni-text-color;
      @include multiLineEllipsis(3);

      &.nowarp {
        white-space: nowrap;
      }
    }
  }
}

.quantity-section {
  display: flex;
  flex: 1;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;

  .quantity-item {
    flex: 1;
    text-align: center;

    .quantity-value {
      display: block;
      font-size: 40rpx;
      font-weight: bold;
      color: $uni-text-color;
      margin-bottom: 4rpx;

      &.all {
        color: #398BDE;
      }

      &.success {
        color: #52c41a;
      }

      &.warning {
        color: #faad14;
      }

      &.error {
        color: #F92C2E;
      }
    }

    .quantity-label {
      display: block;
      font-size: 30rpx;
      color: $uni-text-color-grey;
    }
  }
}

.operation-section {
  display: flex;
  justify-content: space-between;
}
</style>
