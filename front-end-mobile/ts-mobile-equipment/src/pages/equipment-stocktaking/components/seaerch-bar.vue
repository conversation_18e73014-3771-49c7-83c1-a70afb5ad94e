<template>
  <view class="search-content">
    <u-search
      shape="square"
      v-model="keywords"
      :show-action="false"
      placeholder="输入任务名称搜索"
      @search="handleSearchDataChange"
      @clear="handleSearchDataChange"
    />

    <!-- <view
      class="work-icon work-icon-xinzeng has-more-condition"
      @click="handleAdd"
    ></view> -->

    <view
      class="work-icon work-icon-shaixuan"
      :class="hasCondition ? 'has-more-condition' : ''"
      @click="handleOpenMoreSearch"
    ></view>

    <screen
      ref="screen"
      :formList="searchFormList"
      :formData="searchData"
      @reset="handleSearchDataReset"
      @change="handleSearchDataChange"
    ></screen>
  </view>
</template>

<script>
import screen from './screen.vue';

export default {
  components: {
    screen
  },
  data() {
    return {
      keywords: '',

      searchData: {
        skuType: ''
      },

      searchFormList: [
        {
          title: '资产类别',
          type: 'select',
          mode: 'radio',
          chooseType: 'radio',
          prop: 'skuType',
          optionList: [
            {
              value: '',
              name: '全部'
            },
            {
              value: '0',
              name: '医疗设备'
            },
            {
              value: '1',
              name: '一般资产'
            }
          ]
        }
      ]
    };
  },

  computed: {
    hasCondition() {
      return this.searchData.skuType;
    }
  },

  methods: {
    getData() {
      let skuType = this.searchData.skuType || '';
      return {
        name: this.keywords,
        skuType
      };
    },

    handleAdd() {
      // uni.setStorageSync(
      //   'orderReportConfig',
      //   JSON.stringify({
      //     isReception: true,
      //     formData: {}
      //   })
      // );
      //
      // uni.navigateTo({
      //   url: `/pages/work-order-reporting/index?fromPage=work-order-reception`
      // });
    },

    handleOpenMoreSearch() {
      this.$refs.screen.handleopen();
    },

    handleSearchDataReset() {
      this.searchData = {
        skuType: ''
      };
    },

    handleSearchDataChange() {
      this.$emit('refresh', this.getData());
    }
  }
};
</script>

<style lang="scss" scoped>
.search-content {
  background: #fff;
  display: flex;
  padding: 12rpx 30rpx;
  align-items: center;
}

.work-icon-shaixuan,
.work-icon-xinzeng {
  margin-left: 16rpx;
}

.has-more-condition {
  color: $u-type-primary;
}
</style>
