<template>
  <view class="screen">
    <u-popup
      v-model="isShow"
      mode="right"
      z-index="989"
      width="600"
      @close="handleSearch"
    >
      <view class="screen-box">
        <view
          class="screen-item"
          v-for="(item, index) in formList"
          :key="index"
        >
          <view class="screen-item-title">
            {{ item.title }}
          </view>
          <view
            class="screen-item-option"
            v-if="item.type == 'select' && item.mode == 'checkbox'"
          >
            <u-checkbox-group
              class="screen-option-group"
              v-model="formData[item.prop]"
              @change="checkboxGroupChange($event, item)"
            >
              <u-checkbox
                class="screen-option-item"
                v-for="(radioItem, radioIndex) in item.optionList"
                :key="radioIndex"
                :name="radioItem.value"
                v-model="radioItem.checked"
                :show-icon="false"
                :class="radioItem.checked ? 'active-screen-item' : ''"
              >
                {{ radioItem.name }}
              </u-checkbox>
            </u-checkbox-group>
          </view>
          <view
            class="screen-item-option"
            v-if="item.type == 'select' && item.mode == 'radio'"
          >
            <u-radio-group
              class="screen-option-group"
              v-model="formData[item.prop]"
              @change="radioGroupChange($event, item)"
            >
              <u-radio
                class="screen-option-item"
                v-for="(radioItem, radioIndex) in item.optionList"
                :key="radioIndex"
                :name="radioItem.value"
                :show-icon="false"
              >
                {{ radioItem.name }}
              </u-radio>
            </u-radio-group>
          </view>
          <view
            class="screen-item-option"
            v-else-if="item.type == 'select' && item.mode == 'dept'"
          >
            <view
              v-if="item.chooseType == 'checkbox'"
              class="screen-option-group dept-content"
            >
              <view
                class="screen-option-item dept-selected-item"
                v-for="deptItem in formData[item.propVal]"
                :key="deptItem.id"
                @click="changeDept(deptItem.id, item)"
              >
                <view class="dept-item-text">{{ deptItem.name }}</view>
              </view>
              <text class="screen-option-item" @tap="chooseDept(item)">+</text>
            </view>

            <view v-else class="screen-option-group dept-content">
              <view
                class="screen-option-item dept-selected-item-radio"
                v-for="deptItem in formData[item.propVal]"
                :key="deptItem.id"
                @tap="chooseDept(item)"
              >
                <view class="dept-item-text">{{ deptItem.name }}</view>
              </view>

              <text
                v-if="!formData[item.propVal].length"
                class="screen-option-item"
                @tap="chooseDept(item)"
              >
                +
              </text>
            </view>
          </view>
          <view
            class="screen-item-option"
            v-else-if="item.type == 'select' && item.mode == 'person'"
          >
            <view class="person-content">
              <view
                class="screen-option-item dept-selected-item-radio"
                v-for="personItem in formData[item.propVal]"
                :key="
                  personItem[
                    (item.personInfoProp && item.personInfoProp.key) || 'userId'
                  ]
                "
                @tap="choosePerson(item)"
              >
                <view class="dept-item-text">{{
                  personItem[
                    (item.personInfoProp && item.personInfoProp.name) || 'name'
                  ]
                }}</view>
              </view>

              <text
                v-if="!formData[item.propVal].length"
                class="screen-option-item"
                @tap="choosePerson(item)"
              >
                +
              </text>
            </view>
          </view>
          <view class="screen-item-option" v-else-if="item.mode == 'time'">
            <view v-if="item.type == 'range'">
              <view class="screen-item-time-picker-range">
                <view
                  class="time-picker-start-time"
                  :class="{ 'active-time-picker': formData[item.propVal][0] }"
                  @click="startTimeShow = true"
                >
                  {{ formData[item.propVal][0] || '' }}
                </view>
                <view style="margin: 0 8px;">-</view>
                <view
                  class="time-picker-end-time"
                  :class="{ 'active-time-picker': formData[item.propVal][1] }"
                  @click="endTimeShow = true"
                >
                  {{ formData[item.propVal][1] || '' }}
                </view>
              </view>

              <u-picker
                mode="time"
                :defaultTime="formData[item.propVal][0]"
                v-model="startTimeShow"
                :params="item.params"
                :safe-area-inset-bottom="true"
                @confirm="handleDateConfirm($event, item, 0)"
              ></u-picker>
              <u-picker
                mode="time"
                :defaultTime="formData[item.propVal][1]"
                v-model="endTimeShow"
                :params="item.params"
                :safe-area-inset-bottom="true"
                @confirm="handleDateConfirm($event, item, 1)"
              ></u-picker>
            </view>
          </view>
        </view>
        <view class="btn-box">
          <u-button @click="handleReset" :throttle-time="200">重置</u-button>
          <u-button @click="isShow = false" :throttle-time="200">确定</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'screen',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      isShow: false,
      startTimeShow: false, //时间选择 开始时间是否展示
      endTimeShow: false //时间选择 结束时间是否展示
    };
  },
  methods: {
    handleopen() {
      this.isShow = true;
    },
    radioGroupChange(e, item) {
      this.$set(this.formData, item.prop, e);
    },
    checkboxGroupChange(e, item) {
      this.$set(this.formData, item.prop, e.join(','));
    },
    chooseDept(item) {
      //获取已选科室
      let deptList = this.formData[item.propVal];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      //判断是否为条件查询
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        //查询api
        let params = {
          api: item.searchApi
        };
        //获取查询条件参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.formData[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, ...params };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.formData, item.propVal, data);
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.userId);
        });

        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
      });
      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${item.chooseType}&getListType=${item.getListType}&mode=once`
      });
    },
    changeDept(id, item) {
      let newData = this.formData[item.propVal].filter(item => item.id != id);
      this.$set(this.formData, item.propVal, newData);
    },
    choosePerson(item) {
      let personList = this.formData[item.propVal] || [];
      let personPageParams = {
        title: item.name,
        personInfoProp: item.personInfoProp
      };
      //判断是否为条件查询
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        //查询api
        let params = {};
        //获取查询条件参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (
            !this.formData[item.searchParams[i].value] &&
            item.searchParams[i].message
          ) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.formData[
            item.searchParams[i].value
          ];
        }
        personPageParams = {
          ...personPageParams,
          params,
          api: item.searchApi,
          apiType: item.searchApiType
        };
      }

      uni.setStorageSync('person_list', JSON.stringify(personList));
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));

      uni.$on('trasenPerson', data => {
        this.$set(this.formData, item.propVal, data);

        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${item.chooseType}&getListType=${item.getListType}&searchType=${item.searchType}`
      });
    },
    handleDateConfirm(e, item, index) {
      let time = `${e.year}-${e.month}-${e.day}`,
        newData = this.formData[item.propVal];

      if (newData instanceof Array) {
        newData[index] = time;

        newData.sort((now, next) => {
          let nowDateTimeStamp = new Date(now).getTime(),
            nextTimeStamp = new Date(next).getTime();
          if (nowDateTimeStamp < nextTimeStamp) {
            return -1;
          } else if (nowDateTimeStamp > nextTimeStamp) {
            return 1;
          } else {
            return 0;
          }
        });
      } else {
        newData = time;
      }
      this.$set(this.formData, item.propVal, newData);
    },
    handleReset() {
      this.$emit('reset');
    },
    handleSearch() {
      this.isShow = false;
      this.$emit('change');
    }
  }
};
</script>

<style lang="scss" scoped>
.screen-box {
  padding: 0 20rpx;
  padding-bottom: 44px;
  padding-top: 32rpx;
}
.screen-item-title {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
  color: #666666;
}
.screen-option-group {
  width: 100%;
}
.screen-option-item {
  border-radius: 8rpx;
  line-height: 70rpx;
  height: 70rpx;
  width: 30% !important;
  margin: 0 3% 20rpx 0;
  text-align: center;
  background-color: #ffffff;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  border: 1px solid #dddddd;
  box-sizing: border-box;
  color: #333333;
  position: relative;
}
.btn-box {
  display: flex;
  position: fixed;
  bottom: 0;
  width: calc(100% - 40rpx);
  padding: 8px 0;
  z-index: 99;
  .u-btn {
    width: 132px;
    height: 44px;
    line-height: 25px;
    font-size: 18px;
    color: #333333;
    &:last-child {
      background: $u-type-primary;
      border-radius: 4px;
      border-color: $u-type-primary;
      color: #fff;
    }
    &::after {
      border-color: #e5e5e5;
    }
  }
}

.dept-content {
  display: flex;
  flex-wrap: wrap;
}
.screen-item-time-picker-range {
  display: flex;
  justify-content: center;
}
.time-picker-start-time,
.time-picker-end-time {
  background: #fff;
  border: 1px solid #dddddd;
  flex: 1;
  height: 68rpx;
  line-height: 68rpx;
  border-radius: 8rpx;
  text-align: center;
}
.active-time-picker {
  border-color: $u-type-primary;
  color: $u-type-primary;
}

/deep/ {
  .u-checkbox__icon-wrap {
    display: none !important;
  }
  .screen-item-option .screen-option-item {
    background-color: #fff;
    width: 85px;
    height: 34px;
    line-height: 34px;
    .u-radio__label,
    .u-checkbox__label {
      margin: 0 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      color: #979797;
      text-align: center;
    }
  }
  .screen-item-option .screen-option-item.u-radio--checked,
  .screen-item-option .screen-option-item.active-screen-item {
    border: 1px solid $u-type-primary;
    background-color: #fff;
    .u-radio__label,
    .u-checkbox__label {
      color: $u-type-primary;
    }
  }
  .checked-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 19px;
    border-bottom: 19px solid #005bac;
    border-left: 19px solid transparent;

    .u-icon {
      color: #fff;
      transform: scale(0.8);
      position: absolute;
      left: -12px;
      bottom: -19px;
      .u-icon__icon {
        font-size: 12px !important;
      }
    }
  }
  .screen-item-title {
    font-size: 16px;
    line-height: 22px;
    height: 22px;
  }
  .screen-option-item.dept-selected-item-radio,
  .screen-option-item.dept-selected-item {
    position: relative;
    color: $u-type-primary;
    padding: 0 8px;
    border-color: $u-type-primary;

    .dept-item-text {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .screen-option-item.dept-selected-item-radio {
    min-width: 30%;
    max-width: 100%;
    width: auto !important;
  }
  @media screen and (max-width: 750px) {
    .screen-item-option .screen-option-item {
      width: 170rpx;
      height: 68rpx;
      line-height: 68rpx;
      .u-radio__label,
      .u-checkbox__label {
        margin: 0 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .checked-icon {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 38rpx;
      border-bottom: 38rpx solid #005bac;
      border-left: 38rpx solid transparent;

      .u-icon {
        color: #fff;
        transform: scale(0.8);
        position: absolute;
        left: -24rpx;
        bottom: -38rpx;
        .u-icon__icon {
          font-size: 24rpx;
        }
      }
    }
  }
  @media screen and (max-width: 349px) {
    .checked-icon .u-icon {
      transform: scale(0.7);
      left: -11px;
      bottom: -17px;
    }
  }
}
</style>
