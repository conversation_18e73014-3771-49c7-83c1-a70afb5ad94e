<template>
  <view class="ts-container">
    <u-navbar title="设备盘点" title-bold :custom-back="goBack" />
    <search-bar
      ref="searchBar"
      @refresh="refresh"
    />

    <base-tabs-swiper
      ref="tabSwiper"
      class="tab-swiper-box"
      :list="tabList"
      badgeType="text"
      :font-size="28"
      :current="currentTab"
      :is-scroll="false"
      @change="changeTab"
    />

    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <mescroll
          :ref="`mescroll${index}`"
          :mescrollIndex="index"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <plan-item
            v-for="i in item.list"
            :key="i.id"
            :item="i"
            :isReception="true"
            @click="jumpToDetail(i)"
          />
             <!-- <template #bottomRight>
               <view></view>
               <view class="bottom-right-operate">
                 <u-button
                   class="operate-btn"
                   type="primary"
                   @click.stop="editRow(i)"
                 >编辑
                 </u-button>
                 <u-button
                   class="operate-btn"
                   type="error"
                   @click.stop="deleteRow(i)"
                 >删除
                 </u-button>
               </view>
             </template> -->
        </mescroll>
      </swiper-item>
    </swiper>

  </view>
</template>

<script>
import searchBar from './components/seaerch-bar.vue';
import planItem from './components/plan-item.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  name: 'index',
  components: {
    searchBar,
    planItem,
    mescroll
  },
  data() {
    return {
      fromPage: '',
      tabList: [
        {
          name: '待盘点',
          type: 1,
          count: 0,
          statusExt: '0',
          list: []
        },
        {
          name: '已结束',
          type: 3,
          count: 0,
          statusExt: '1',
          list: []
        }
      ],
      currentTab: 0
    };
  },

  async onLoad(opt) {
    if (opt && opt.index) {
      this.currentTab = opt.index;
    }
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
  },

  methods: {
    refresh(data) {
      this.tabList.forEach((item, index) => {
        this.$refs[`mescroll${index}`] &&
        this.$refs[`mescroll${index}`][0] &&
        this.$refs[`mescroll${index}`][0].downCallback();
      });
    },

    changeTab(index) {
      this.currentTab = index;
    },

    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },

    getListData(page, successCallback, errorCallback, index) {
      let searchData = this.$refs.searchBar.getData();
      this.ajax
        .inventoryPlanList({
          pageNo: page.num,
          pageSize: page.size,
          statusExt: this.tabList[index].statusExt,
          ...searchData
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
          this.tabList[index].count = res.totalCount || 0;
        })
        .catch(err => {
          errorCallback();
        });
    },

    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      this.tabList[index]['count'] = totalCount;
    },

    datasInit(index) {
      this.tabList[index]['list'] = [];
    },

    jumpToDetail(row) {
      uni.navigateTo({
        url: `/pages/stocktaking-details/index?id=${row.id}&parentIndex=${this.currentTab}&fromPage=equipment-stocktaking`
      });
    },

    editRow(row) {
      console.log('=>(index.vue:165) row', row);
    },
    deleteRow(row) {
      console.log('=>(index.vue:165) row', row);
    },

    goBack() {
      if (this.fromPage === 'workBench' || this.fromPage === '') {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/workbench'
        });
      } else {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/workBench'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.swiper-box {
  flex: 1;

  /deep/ .mescroll-upwarp {
    padding: 0 0 16rpx 0;
  }

  /deep/ .mescroll-wxs-content {
    padding: 0 16rpx;
    padding-top: 16rpx;
  }
}

.bottom-right-operate {
  display: flex;
  flex-direction: column;

  .operate-btn {
    min-width: 140rpx;
    max-width: 140rpx;
    height: 48rpx;
    border-radius: 6rpx;
    margin-bottom: 8rpx;
  }
}
</style>
