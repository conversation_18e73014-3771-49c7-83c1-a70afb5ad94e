<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
  <title></title>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
    })
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
  </script>
  <base href="<%= BASE_URL%>" />
  <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
</head>
<body>
  <div class='qiankun-children' id="uni-app-qiankun-children" style='height: 100%;width: 100%;'>
    <div id="<%= VUE_APP_CONTAINER %>"></div>
    <div id="app"></div>
  </div>
</body>
<script type="text/javascript" src="<%= BASE_URL %>static/uni.webview.js"></script>
</html>
