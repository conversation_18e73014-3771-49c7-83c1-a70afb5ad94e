// #ifdef H5
var jweixin = require('@/common/jweixinModule.js');
var localId = null;
export default {
  initwxJdk: function(data) {
    jweixin.config({
      beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: data.appId, // 必填，企业微信的corpID
      timestamp: data.timestamp, // 必填，生成签名的时间戳
      nonceStr: data.nonceStr, // 必填，生成签名的随机串
      signature: data.signature, // 必填，签名，见附录1
      jsApiList: [
        'startRecord',
        'stopRecord',
        'onVoiceRecordEnd',
        'uploadVoice',
        'downloadVoice',
        'translateVoice'
      ] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    });
    jweixin.ready(function() {
      jweixin.checkJsApi({
        jsApiList: [
          'startRecord',
          'stopRecord',
          'onVoiceRecordEnd',
          'uploadVoice',
          'downloadVoice',
          'translateVoice'
        ], // 需要检测的JS接口列表，所有JS接口列表见附录2,
        success: function(res) {
          // 以键值对的形式返回，可用的api值true，不可用为false
          // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
        }
      });
    });
  },
  startRecord: function() {
    jweixin.startRecord();
  },
  stopRecord: function(_self, recordTime) {
    jweixin.stopRecord({
      success: function(res) {
        localId = res.localId;
        jweixin.uploadVoice({
          localId, // 需要上传的音频的本地ID，由stopRecord接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function(res) {
            let duration = 60 - recordTime;
            _self.uploadFile(res, _self.name, duration);
          }
        });
      }
    });
  },
  stopRecordText: function(_self, name) {
    jweixin.stopRecord({
      success: function(res) {
        localId = res.localId;
        jweixin.translateVoice({
          localId, // 需要上传的音频的本地ID，由stopRecord接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function(res) {
            _self.form[name] = _self.form[name] + res.translateResult;
            localId = null;
          }
        });
      }
    });
  }
};
//#endif
