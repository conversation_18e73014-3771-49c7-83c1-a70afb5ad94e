import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  工单助手列表**/
  getMessagePageList(params) {
    return request.get(
      `${apiConfig.worksheet()}/message/selectMessagePageList`,
      {
        params: params
      }
    );
  },
  readMessageDetail(params) {
    return request.get(
      `${apiConfig.worksheet()}/message/selectOneWsWsMessageListOutVoById/${params}/0`
    );
  }
};
