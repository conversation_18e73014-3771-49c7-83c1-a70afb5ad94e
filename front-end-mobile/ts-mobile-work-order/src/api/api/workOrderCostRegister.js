import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  deleteCostData(data) {
    return request.post(
      `${apiConfig.worksheet()}/wsCostDel`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  saveEditCost(data) {
    return request.post(
      `${apiConfig.worksheet()}/wsCost`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
};
