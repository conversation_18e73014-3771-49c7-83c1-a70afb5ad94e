import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取应用基本设置**/
  getDeptList(api, params) {
    return request.post(`${apiConfig.basics()}${api}`, params);
  },
  getPersonByDept(params) {
    return request.post(
      `${apiConfig.basics()}/employee/getEmployeeAllPageList`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
