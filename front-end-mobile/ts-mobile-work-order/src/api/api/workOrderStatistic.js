import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取应用基本设置**/
  getWorkOrderSituation(params) {
    return request.get(
      `${apiConfig.worksheet()}/statisticsData/getWorkOrderSituation`,
      {
        params: params,
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '加载中' // 请求loading中的文字提示
        }
      }
    );
  },
  getWorkOrderType(params) {
    return request.get(
      `${apiConfig.worksheet()}/statisticsData/getWorkOrderType`,
      {
        params: params
      }
    );
  },
  getDeptUserReceiveWorkSheetDatas(params) {
    return request.get(
      `${apiConfig.worksheet()}/statisticsData/getDeptUserReceiveWorkSheetDatas`,
      {
        params: params
      }
    );
  }
};
