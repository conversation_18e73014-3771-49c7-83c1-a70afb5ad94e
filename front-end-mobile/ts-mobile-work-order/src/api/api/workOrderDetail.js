import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取应用基本设置**/
  getWorkOrderDetail(id) {
    return request.get(
      `${apiConfig.worksheet()}/workSheet/workSheetInfo/${id}`,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '' // 请求loading中的文字提示
        }
      }
    );
  },
  /**@desc 工单认领操作 */
  claimWorkOrder(data) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/toClaim`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 工单费用明细 */
  getWorkOrderCostDetailList(data) {
    return request.get(`${apiConfig.worksheet()}/wsCostList`, {
      params: data
    });
  },
  /**@desc 工单费用总值 */
  getWorkOrderCostSum(data) {
    return request.get(`${apiConfig.worksheet()}/wsCostSum`, {
      params: data
    });
  },
  /**@desc 追回工单 */
  handleCallbackOrder(data) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetToRecover`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  },
  /**@desc 撤回工单 */
  handleRebcackOrder(data) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetToRecoverEnd`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
};
