import request from '../ajax.js';
import { apiConfig } from '../config.js';
import store from '../../store';

export default {
  /**@desc 基础服务 附件上传 */
  upload(businessId, data) {
    return new Promise((resolve, reject) => {
      let xhr = new XMLHttpRequest();
      let url = `${apiConfig.basics()}/fileAttachment/upload?moduleName=workDesk&businessId=${businessId}`;
      xhr.open('POST', url, true);
      xhr.ontimeout = function() {
        // xhr请求超时事件处理
        resolve({
          success: false,
          message: '请求超时'
        });
      };
      xhr.onreadystatechange = async () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          //上传成功
          let res = JSON.parse(xhr.responseText);
          if (res.statusCode === 302 || res.statusCode === 21000) {
            // uni.reLaunch({
            //   url: '/pages/login/login'
            // });
            store.dispatch('common/goToLogin');
          } else if (res.statusCode != 200) {
            uni.showModal({
              title: '提示',
              showCancel: false,
              confirmColor: '#005BAC',
              content: res.message
            });
            resolve(res);
          } else {
            resolve(res);
          }
        }
      };
      xhr.send(data);
    });
  },
  /**@desc 通过businessId获取文件列表 */
  getFileAttachmentByBusinessId(businessId) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/getFileAttachmentByBusinessId?businessId=${businessId}`
    );
  },
  /**@desc 通过id删除文件 */
  deleteFileId(id) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/deleteFileId?fileid=${id}`
    );
  }
};
