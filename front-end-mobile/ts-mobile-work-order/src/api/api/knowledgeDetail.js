import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 查询知识点详情**/
  getKnowledgeBaseInfo(id) {
    return request.get(
      `${apiConfig.worksheet()}/knowledgeBase/getKnowledgeBaseInfo/${id}`
    );
  },
  /**@desc 移出知识点**/
  removeKnowledge(params) {
    return request.post(
      `${apiConfig.worksheet()}/knowledgeBase/remove`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 移入知识点**/
  moveInKnowledge(params) {
    return request.post(`${apiConfig.worksheet()}/knowledgeBase/move`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 移入知识点**/
  submitKnowledge(params) {
    return request.post(
      `${apiConfig.worksheet()}/knowledgeBase/submit`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  deleteKnowledge(params) {
    return request.post(
      `${apiConfig.worksheet()}/knowledgeBase/delete`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  likeKnowledge(id) {
    return request.get(
      `${apiConfig.worksheet()}/knowledgeBase/giveALike/${id}`
    );
  },
  cancelLikeKnowledge(id) {
    return request.get(
      `${apiConfig.worksheet()}/knowledgeBase/cancelThePraise/${id}`
    );
  }
};
