import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取知识点类型**/
  getKnowledgeTypeAllList() {
    return request.get(
      `${apiConfig.worksheet()}/knowledgeType/getKnowledgeTypeAllList`
    );
  },
  /**@desc 知识点提交**/
  getSubmitKnowledgeBaseInfo(id) {
    return request.get(
      `${apiConfig.worksheet()}/workSheet/selectSubmitKnowledgeBaseInfo/${id}`
    );
  },
  /**@desc 知识点提交**/
  addKnowledge(params) {
    return request.post(`${apiConfig.worksheet()}/knowledgeBase/save`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  }
};
