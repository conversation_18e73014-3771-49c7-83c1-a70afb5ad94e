import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  工单终止提交**/
  workSheetTerminated(params) {
    return request.post(
      `${apiConfig.worksheet()}/workTask/workSheetTerminated`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
