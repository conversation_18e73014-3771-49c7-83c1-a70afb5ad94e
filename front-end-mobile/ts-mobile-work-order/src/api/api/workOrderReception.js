import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取服务台tab标签上的数量**/
  getWorkSheetListBusCounts() {
    return request.get(
      `${apiConfig.worksheet()}/workSheet/workSheetListBusCounts/5`
    );
  },
  /**@desc 获取未建单数据 */
  getCallRecordsPageList(params) {
    return request.post(
      `${apiConfig.worksheet()}/CustometServiceLog/selectCallRecordsPageList`,
      params
    );
  },
  /**@desc 标记无效来电 */
  markUseless(data = {}) {
    return request.post(
      `${apiConfig.worksheet()}/CustometServiceLog/modifyingCallRecords`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
