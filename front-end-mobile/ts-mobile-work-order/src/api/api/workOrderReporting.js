import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取故障类型所属科室**/
  getFaultTypeDeptList() {
    return request.get(`${apiConfig.worksheet()}/workSheet/meauList`);
  },
  /**@desc 获取故障类型**/
  getDatas(api) {
    return request.get(`${apiConfig.worksheet()}${api}`);
  },
  /**@desc 保存故障描述**/
  saveFaultCommon(params) {
    return request.post(`${apiConfig.worksheet()}/faultCommon/save`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  saveWorkOrder(params) {
    return request.post(`${apiConfig.worksheet()}/workSheet/save`, params, {
      custom: {
        showLoading: true, // 是否显示请求中的loading
        loadingText: '提交中...' // 请求loading中的文字提示
      },
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  updateByWorkNumber(params) {
    return request.post(`${apiConfig.worksheet()}/workSheet/updateByWorkNumber`, params, {
      custom: {
        showLoading: true, // 是否显示请求中的loading
        loadingText: '提交中...' // 请求loading中的文字提示
      },
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  //获取工单设置
  getWorkSheetSetConfig() {
    return request.get(`${apiConfig.worksheet()}/sysConfig`);
  },

  //获取扫码设备信息
  getSelectOne(data) {
    return request.post(
      `${apiConfig.worksheet()}/faultEquipment/selectOne`,
      data
    );
  }
};
