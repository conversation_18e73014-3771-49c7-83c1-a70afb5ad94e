import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  响应接单**/
  workSheetRespondAccept(params) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetAccept`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc  响应退回**/
  workSheetRespondBack(params) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetBack`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc  响应转发**/
  workSheetRespondResend(params) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetResend`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
