import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取催办信息**/
  openNodeInfo(pkWsTaskId) {
    return request.get(
      `${apiConfig.worksheet()}/workTask/openNodeInfo?pkWsTaskId=${pkWsTaskId}`
    );
  },
  /**@desc 催办提交**/
  workSheetHadRecovered(params) {
    return request.post(
      `${apiConfig.worksheet()}/workSheet/workSheetHadRecovered`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8',
          'error-message': 1
        }
      }
    );
  }
};
