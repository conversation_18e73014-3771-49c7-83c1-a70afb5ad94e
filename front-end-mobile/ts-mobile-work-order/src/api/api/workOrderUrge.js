import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取催办信息**/
  getHastenInfo(id) {
    return request.get(
      `${apiConfig.worksheet()}/workSheetHasten/getHastenInfo/${id}`
    );
  },
  /**@desc 催办提交**/
  workSheetHasten(params) {
    return request.post(
      `${apiConfig.worksheet()}/workSheetHasten/save`,
      params,
      {
        custom: {
          showLoading: true, // 是否显示请求中的loading
          loadingText: '提交中...' // 请求loading中的文字提示
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8',
          'error-message': 1
        }
      }
    );
  }
};
