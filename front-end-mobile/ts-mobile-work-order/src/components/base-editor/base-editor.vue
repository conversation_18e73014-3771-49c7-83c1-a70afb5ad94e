<template>
  <view class="editor-contanier">
    <view class="toolbar" @tap="format" :style="toolStyle">
      <view
        :style="{ color: formats.bold ? activeColor : '' }"
        class="iconfont icon-zitijiacu"
        data-name="bold"
      ></view>
      <view
        :style="{ color: formats.italic ? activeColor : '' }"
        class="iconfont icon-zitixieti"
        data-name="italic"
      ></view>
      <view
        :style="{ color: formats.underline ? activeColor : '' }"
        class="iconfont icon-zitixiahuaxian"
        data-name="underline"
      ></view>
      <view
        :style="{ color: formats.strike ? activeColor : '' }"
        class="iconfont icon-zitishanchuxian"
        data-name="strike"
      ></view>
      <view
        :style="{ color: formats.align === 'left' ? activeColor : '' }"
        class="iconfont icon-zuoduiqi"
        data-name="align"
        data-value="left"
      ></view>
      <view
        :style="{ color: formats.align === 'center' ? activeColor : '' }"
        class="iconfont icon-juzhongduiqi"
        data-name="align"
        data-value="center"
      ></view>
      <view
        :style="{ color: formats.align === 'right' ? activeColor : '' }"
        class="iconfont icon-youduiqi"
        data-name="align"
        data-value="right"
      ></view>
      <view
        :style="{ color: formats.align === 'justify' ? activeColor : '' }"
        class="iconfont icon-zuoyouduiqi"
        data-name="align"
        data-value="justify"
      ></view>
      <view
        :style="{ color: formats.lineHeight ? activeColor : '' }"
        class="iconfont icon-line-height"
        data-name="lineHeight"
        data-value="2"
      ></view>
      <view
        :style="{ color: formats.letterSpacing ? activeColor : '' }"
        class="iconfont icon-Character-Spacing"
        data-name="letterSpacing"
        data-value="2em"
      ></view>
      <view
        :style="{ color: formats.marginTop ? activeColor : '' }"
        :class="formats.marginTop ? 'ql-active' : ''"
        class="iconfont icon-722bianjiqi_duanqianju"
        data-name="marginTop"
        data-value="20px"
      ></view>
      <view
        :style="{ color: formats.previewarginBottom ? activeColor : '' }"
        class="iconfont icon-723bianjiqi_duanhouju"
        data-name="marginBottom"
        data-value="20px"
      ></view>
      <view class="iconfont icon-clearedformat" @tap="removeFormat"></view>
      <view
        :style="{ color: formats.fontFamily ? activeColor : '' }"
        class="iconfont icon-font"
        data-name="fontFamily"
        data-value="Pacifico"
      ></view>
      <view
        :style="{ color: formats.fontSize === '24px' ? activeColor : '' }"
        class="iconfont icon-fontsize"
        data-name="fontSize"
        data-value="24px"
      ></view>
      <view
        :style="{ color: formats.color === '#fa3534' ? activeColor : '' }"
        class="iconfont icon-text_color"
        data-name="color"
        data-value="#fa3534"
      ></view>
      <view
        :style="{
          color: formats.backgroundColor === '#fa3534' ? activeColor : ''
        }"
        class="iconfont icon-fontbgcolor"
        data-name="backgroundColor"
        data-value="#fa3534"
      ></view>
      <view class="iconfont icon-date" @tap="insertDate"></view>
      <view
        class="iconfont icon--checklist"
        data-name="list"
        data-value="check"
      ></view>
      <view
        :style="{ color: formats.list === 'ordered' ? activeColor : '' }"
        class="iconfont icon-youxupailie"
        data-name="list"
        data-value="ordered"
      ></view>
      <view
        :style="{ color: formats.list === 'bullet' ? activeColor : '' }"
        class="iconfont icon-wuxupailie"
        data-name="list"
        data-value="bullet"
      ></view>
      <view class="iconfont icon-undo" @tap="undo"></view>
      <view class="iconfont icon-redo" @tap="redo"></view>
      <view
        class="iconfont icon-outdent"
        data-name="indent"
        data-value="-1"
      ></view>
      <view
        class="iconfont icon-indent"
        data-name="indent"
        data-value="+1"
      ></view>
      <view class="iconfont icon-fengexian" @tap="insertDivider"></view>
      <view class="iconfont icon-charutupian" @tap="insertImage"></view>
      <view
        :style="{ color: formats.header === 1 ? activeColor : '' }"
        class="iconfont icon-format-header-1"
        data-name="header"
        :data-value="1"
      ></view>
      <view
        :style="{ color: formats.script === 'sub' ? activeColor : '' }"
        class="iconfont icon-zitixiabiao"
        data-name="script"
        data-value="sub"
      ></view>
      <view
        :style="{ color: formats.script === 'super' ? activeColor : '' }"
        class="iconfont icon-zitishangbiao"
        data-name="script"
        data-value="super"
      ></view>
      <view class="iconfont icon-shanchu" @tap="clear"></view>
      <view
        :style="{ color: formats.direction === 'rtl' ? activeColor : '' }"
        class="iconfont icon-direction-rtl"
        data-name="direction"
        data-value="rtl"
      ></view>
    </view>
    <editor
      id="editor"
      class="ql-container"
      :placeholder="placeholder"
      showImgSize
      showImgToolbar
      showImgResize
      @statuschange="onStatusChange"
      :read-only="readOnly"
      @ready="onEditorReady"
    >
    </editor>
  </view>
</template>

<script>
export default {
  name: 'base-editor',
  props: {
    toolStyle: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    activeColor: {
      type: String,
      default: '#005bac'
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    imgUploadHost: {
      type: String,
      default: ''
    },
    imgUploadUrl: {
      type: String,
      default: ''
    },
    imgUploadParamName: {
      type: String,
      default: ''
    },
    contents: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formats: {}
    };
  },
  watch: {
    contents: {
      handler(newVal) {
        if (this.contents && this.editorCtx) {
          this.setContent();
        }
      }
    }
  },
  methods: {
    onEditorReady() {
      uni
        .createSelectorQuery()
        .select('#editor')
        .context(res => {
          this.editorCtx = res.context;
        })
        .exec();
      if (this.contents) {
        this.setContent();
      }
    },
    setContent() {
      this.editorCtx.setContents({
        html: this.contents
      });
    },
    undo() {
      this.editorCtx.undo();
    },
    redo() {
      this.editorCtx.redo();
    },
    format(e) {
      let { name, value } = e.target.dataset;
      if (!name) return;
      this.editorCtx.format(name, value);
    },
    onStatusChange(e) {
      const formats = e.detail;
      this.formats = formats;
    },
    insertDivider() {
      this.editorCtx.insertDivider({
        success: function() {
          console.log('insert divider success');
        }
      });
    },
    clear() {
      this.editorCtx.clear({
        success: function(res) {
          console.log('clear success');
        }
      });
    },
    removeFormat() {
      this.editorCtx.removeFormat();
    },
    insertDate() {
      const date = new Date();
      const formatDate = `${date.getFullYear()}/${date.getMonth() +
        1}/${date.getDate()}`;
      this.editorCtx.insertText({
        text: formatDate
      });
    },
    insertImage() {
      uni.chooseImage({
        count: 1,
        success: res => {
          this.uploadImg(res.tempFilePaths[0]);
        },
        fail: err => {
          uni.showToast({
            title: '上传失败'
          });
        }
      });
    },
    uploadImg(file) {
      uni.uploadFile({
        url: `${this.imgUploadHost}${this.imgUploadUrl}`, //开发者服务器地址
        name: this.imgUploadParamName, //开发者在服务器端通过这个 key 可以获取到文件二进制内容
        filePath: file, //要上传文件资源的路径。
        success: res => {
          if (res.statusCode == 200) {
            this.editorCtx.insertImage({
              src: `${this.imgUploadHost}${
                JSON.parse(res.data).object[0].filePath
              }`,
              alt: '图片',
              success: () => {}
            });
          }
        },
        fail: err => {
          uni.showToast({
            title: '上传失败'
          });
        }
      });
    },
    getContent() {
      let contentHtml = '';
      this.editorCtx.getContents({
        success: res => {
          contentHtml = res;
        }
      });
      return contentHtml;
    }
  },
  onLoad() {
    uni.loadFontFace({
      family: 'Pacifico',
      source: 'url("Pacifico.ttf")'
    });
  }
};
</script>

<style>
@import './editor-icon.css';
.editor-contanier {
  padding-bottom: 100rpx;
}
.iconfont {
  display: inline-block;
  width: 56rpx;
  height: 56rpx;
  cursor: pointer;
  font-size: 36rpx;
  line-height: 48rpx;
  text-align: center;
}
.toolbar {
  box-sizing: border-box;
  border-bottom: 0;
  padding: 0 20rpx;
}
.ql-container {
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  width: 100%;
  min-height: 30vh;
  height: auto;
  background: #fff;
  font-size: 28rpx;
  line-height: 1.5;
}
</style>
