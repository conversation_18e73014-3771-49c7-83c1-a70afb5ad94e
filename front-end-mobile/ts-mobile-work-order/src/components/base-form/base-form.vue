<template>
  <view class="form">
    <view class="form-container">
      <u-form
        class="form-box"
        ref="uForm"
        :model="form"
        :error-type="errorType"
      >
        <u-form-item
          v-for="(item, index) in formList"
          :key="index"
          :required="item.required"
          :label-width="$formLabelWidth"
          :label-align="item.labelAlign"
          :label-position="
            item.labelPosition
              ? item.labelPosition
              : labelPositionFilter(
                  item.type,
                  $formInputLabelPosition,
                  $formTextareaLabelPosition
                )
          "
          :label="item.title"
          :prop="item.prop"
        >
          <template #left>
            <text
              v-if="item.labelSlot != 'mic'"
              :class="item.labelSlotClass"
              :style="item.labelSlotStyle"
              @click="labelSlotClick(item)"
            >
              {{ item.labelSlot }}
            </text>
            <view class="u-font-xs" @click="recStart(item.prop)" v-else>
              <u-icon name="mic" size="48" color="#000"></u-icon>
            </view>
          </template>
          <template
            #right
            v-if="item.type == 'radio' || item.type == 'rate' || item.rightSlot"
          >
            <u-radio-group
              v-if="item.type == 'radio'"
              v-model="form[item.prop]"
              :disabled="item.disabled"
              @change="radioGroupChange($event, item)"
              :wrap="item.radioCheckWrap"
            >
              <u-radio
                shape="circle"
                v-for="(radioItem, radioIndex) in item.radioList"
                :key="radioIndex"
                :name="radioItem.value"
              >
                {{ radioItem.label }}
              </u-radio>
            </u-radio-group>
            <u-rate
              v-else-if="item.type == 'rate'"
              :count="item.count"
              v-model="form[item.prop]"
              :inactive-color="item.inactiveColor"
              :active-color="item.activeColor"
              :size="item.size"
              :disabled="item.disabled"
              :active-icon="item.activeIcon"
              :inactive-icon="item.inactiveIcon"
              :custom-prefix="item.customPrefix"
            >
            </u-rate>
            <text
              v-if="item.rightSlot"
              :class="item.rightSlotClass"
              :style="item.rightSlotStyle"
              @click="rightSlotClick(item)"
            >
              {{ item.rightSlot }}
            </text>
          </template>
          <template
            #default
            v-if="
              item.type == 'file' ||
                item.type == 'select' ||
                item.type == 'text' ||
                item.type == 'number' ||
                item.type == 'textarea' ||
                item.type == 'audio'
            "
          >
            <view
              v-if="item.type == 'file' && item.fileVal"
              class="flex-column"
            >
              <u-upload
                max-count="9"
                width="160"
                height="160"
                :disabled="item.disabled"
                :show-progress="false"
                :action="item.action"
                :index="item.prop"
                :form-data="item.form"
                :header="item.header"
                :name="item.name"
                :file-list="form[item.imgVal]"
                @on-success="uploadedFile"
                @on-remove="removeFile"
              ></u-upload>
              <view v-if="form[item.fileVal] && form[item.fileVal].length">
                <view
                  class="file-list"
                  v-for="(file, fileIndex) of form[item.fileVal]"
                  :key="fileIndex"
                >
                  <view class="file-list-item-title">
                    <u-icon
                      name="fujian"
                      custom-prefix="work-icon"
                      size="28"
                      color="#666"
                    ></u-icon>
                    {{ file.title || file.fkFileName }}
                  </view>

                  <view class="file-operation-button">
                    <text
                      class="file-operation-button-item"
                      @click="deleteFile(item, file)"
                    >
                      删除
                    </text>
                    <text
                      v-if="
                        file.fkFileName.toLowerCase().indexOf('.mhtml') == -1
                      "
                      class="file-operation-button-item"
                      @click="previewFile(item, file)"
                    >
                      预览
                    </text>
                  </view>
                </view>
              </view>
            </view>
            <u-upload
              v-else-if="item.type == 'file'"
              max-count="9"
              width="160"
              height="160"
              :show-progress="false"
              :disabled="item.disabled"
              :action="item.action"
              :index="item.prop"
              :form-data="item.form"
              :header="item.header"
              :name="item.name"
              :fileList="form[item.propVal]"
              @on-success="uploadedFile"
              @on-remove="removeFile"
            ></u-upload>
            <view class="audioList" v-else-if="item.type == 'audio'">
              <view
                v-for="(e, i) in form[item.prop]"
                :key="i"
                class="audioList_item"
              >
                <zaudio
                  v-if="e.fileUrl"
                  :keyValue="e.fileId"
                  theme="theme4"
                  themeColor="#999"
                ></zaudio>
                <view class="closeAudio" @click="removeAudio(i, item.prop)"
                  ><u-icon name="close" size="28"></u-icon
                ></view>
              </view>
            </view>
            <u-input
              v-else
              :class="inputClass(item)"
              :key="item.props"
              :border="$formInputBorder"
              :height="item.height"
              :type="item.type"
              :placeholder="item.placeholder"
              :disabled="item.disabled"
              :input-align="
                item.inputAlign
                  ? item.inputAlign
                  : inputAlignFilter(
                      item.type,
                      item.labelPosition,
                      $formInputAlign,
                      $formTextareaAlign
                    )
              "
              :maxlength="item.maxlength"
              v-model="form[item.prop]"
              trim
              @input="item.callback ? changeInputVal($event, item) : ''"
              @click="
                item.type == 'select' && item.mode == 'select'
                  ? changeSelectShow(item, index)
                  : item.type == 'select' && item.mode == 'time'
                  ? changePickerShow(item)
                  : item.type == 'select' && item.mode == 'person'
                  ? choosePerson(item)
                  : item.type == 'select' && item.mode == 'dept'
                  ? chooseDept(item)
                  : ''
              "
            >
            </u-input>
            <view
              style="width: 48rpx; min-height: 100rpx;"
              @click="recStart"
              v-if="item.prop == 'faultDeion'"
            >
              <u-icon name="mic" size="48"></u-icon>
            </view>
          </template>
        </u-form-item>
      </u-form>
      <view class="button-box" v-if="showSubmitButton">
        <u-button type="primary" @click="submit">{{ submitTitle }}</u-button>
      </view>
    </view>
    <u-select
      mode="single-column"
      :list="selctAllObj[clickProp]"
      v-model="selectShow"
      @confirm="selectConfirm"
    ></u-select>
    <u-picker
      :mode="clickMode"
      v-model="pickerShow"
      :params="clickParams"
      @confirm="pickerConfirm"
    ></u-picker>
    <u-popup
      border-radius="16"
      v-model="showModel"
      mode="center"
      @close="close"
    >
      <view class="showModel">
        <u-loading
          mode="flower"
          size="160"
          v-show="showModelLoading"
        ></u-loading>
        <view class="showModelTitle">正在录音</view>
        <u-button class="stop" type="primary" @click="stopRecord"
          >停止录音</u-button
        >
      </view>
    </u-popup>
  </view>
</template>

<script>
import wxInit from '@/common/wxInit.js';
import Zaudio from '@/components/uniapp-zaudio/zaudio.vue';
export default {
  name: 'base-form',
  components: { Zaudio },
  props: {
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    submitTitle: {
      type: String,
      default: '提交'
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    showSubmitButton: {
      type: Boolean,
      default: true
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      errorType: ['toast'],
      selctAllObj: {},
      clickProp: null,
      clickPropVal: null,
      relationProp: [],
      clickMode: null,
      clickParams: null,
      clickField: null,
      selectShow: false,
      pickerShow: false,
      personLabel: {},
      deptLabel: {},
      form: this.formData,
      showModel: false,
      showModelLoading: false,
      intervalID: null,
      recordTime: 58,
      name: '',
      audioList: []
    };
  },
  watch: {
    formList: {
      handler(newVal = [], oldVal = []) {
        this.init(newVal, oldVal);
      },
      immediate: true,
      deep: true
    },
    form(newVal) {
      this.$emit('update:formData', newVal);
    }
  },
  destroyed() {
    if (this.intervalID != null) {
      clearInterval(this.intervalID);
    }
  },
  mounted() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.uForm.setRules(this.rules);
    if (this.form.hasOwnProperty('audioList')) {
      // 音频
      let audioList = this.form['audioList']
        .filter(item => item.fileUrl)
        .map(item => {
          return {
            src: encodeURI(
              `${
                this.$store.state.common.baseHost
              }/ts-basics-bottom/fileAttachment/readFile/${item.fileUrl
                .split('/')
                .pop()}`
            ),
            duration: Number(item.duration || 10),
            key: item.fileId
          };
        });
      this.$zaudio.setAudio(audioList);
      this.$zaudio.setRender(0);
    }
    this.getWxJsapiSignature();
  },
  methods: {
    labelPositionFilter(type, inputLable, textareaLable) {
      if (type == 'textarea' || type == 'file' || type == 'audio') {
        return textareaLable;
      } else {
        return inputLable;
      }
    },
    inputAlignFilter(type, labelPosition, inputAlign, textareaAlign) {
      if (type == 'textarea') {
        return textareaAlign;
      } else {
        if (labelPosition == 'top') {
          return 'left';
        }
        return inputAlign;
      }
    },
    //初始化选择项、校验规则
    init(newFormList, oldFormList) {
      let selctAllObj = {},
        personLabel = {},
        deptLabel = {};

      newFormList.forEach(item => {
        let prop = item.prop;
        if (item.optionList) {
          selctAllObj[prop] = item.optionList;
        }

        if (item.type == 'select' && item.mode == 'person') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'person' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            personLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            personLabel[prop] = [];
          }
        } else if (item.type == 'select' && item.mode == 'dept') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'dept' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            deptLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            deptLabel[prop] = [];
          }
        }
      });
      this.selctAllObj = JSON.parse(JSON.stringify(selctAllObj));
      this.personLabel = JSON.parse(JSON.stringify(personLabel));
      this.deptLabel = JSON.parse(JSON.stringify(deptLabel));

      //抛出初始化完成事件，用以解决初始化数据清空问题, 或者初始化赋值问题
      this.$emit('init-finished');
    },
    //单选
    radioGroupChange(e, item) {
      this.$set(this.form, item.prop, e);
      if (item.callback) {
        item.callback(e);
      }
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.form, item.prop, item.callback(e, item));
      });
    },
    //打开列选择器
    async changeSelectShow(e) {
      if (e.disabled) return false;
      this.relationProp = e.relationProp || [];

      if (e.searchParams && e.searchParams.length > 0) {
        let param = [];
        for (var i = 0; i < e.searchParams.length; i++) {
          if (!this.form[e.searchParams[i].value]) {
            this.$u.toast(e.searchParams[i].message);
            return false;
          }
          param.push(
            `${e.searchParams[i].name}=${this.form[e.searchParams[i].value]}`
          );
        }
        let optionList = await this.getDatas(
          `${e.searchApi}?${param.join('&')}`
        );
        this.$set(this.selctAllObj, e.prop, optionList);
      }
      this.$nextTick(() => {
        this.selectShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (this.form[this.clickPropVal] !== e[0].value) {
        this.$set(this.form, this.clickProp, e[0].label);
        this.$set(this.form, this.clickPropVal, e[0].value);
        if (this.relationProp.length > 0) {
          for (var i = 0; i < this.relationProp.length; i++) {
            this.$set(this.form, this.relationProp[i].prop, '');
            if (this.relationProp[i].propVal) {
              this.$set(this.form, this.relationProp[i].propVal, '');
            }
          }
        }
      }
    },
    //打开选择器
    changePickerShow(e) {
      if (e.disabled) return false;

      this.pickerShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.clickParams = e.params;
      this.clickField = e.field;
    },
    async getDatas(api) {
      let datas = await this.ajax.getDatas(api);
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    //时间确认事件
    pickerConfirm(e) {
      if (this.clickMode == 'time') {
        this.$set(
          this.form,
          this.clickProp,
          this.timePickerConfirm(e, this.clickField)
        );
      }
    },
    //格式化时间
    timePickerConfirm(e, field = 'yy-MM-dd') {
      if (field == 'YY') {
        return `${e.year}年`;
      } else if (field == 'yy-MM') {
        return `${e.year}-${e.month}`;
      } else if (field == 'yy-MM-dd') {
        return `${e.year}-${e.month}-${e.day}`;
      } else if (field == 'HH:mm') {
        return `${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm:ss') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
      }
    },
    //选择人员
    choosePerson(item) {
      if (item.disabled) return false;

      let personList = this.personLabel[item.prop] || [];
      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.name,
        personInfoProp: item.personInfoProp
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < (item.searchParams || []).length; i++) {
          if (
            !this.form[item.searchParams[i].value] &&
            item.searchParams[i].message
          ) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        personPageParams = {
          ...personPageParams,
          params,
          api: item.searchApi,
          apiType: item.searchApiType
        };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        this.$set(this.personLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        } else {
          let personNameList = [],
            personIdList = [],
            personName =
              (item.personInfoProp && item.personInfoProp.name) || 'name',
            psersonId =
              (item.personInfoProp && item.personInfoProp.key) || 'userId';

          data.map(i => {
            personNameList.push(i[personName]);
            personIdList.push(i[psersonId]);
          });
          this.$set(this.form, item.prop, personNameList.join(','));
          this.$set(this.form, item.propVal, personIdList.join(','));
        }
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
        this.$forceUpdate();
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&searchType=${item.searchType ||
          'workSheetPost'}`
      });
    },
    chooseDept(item) {
      if (item.disabled) return false;

      let deptList = this.deptLabel[item.prop] || [];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.deptLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        }
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.id);
        });
        this.$set(this.form, item.prop, deptName.join(','));
        this.$set(this.form, item.propVal, deptId.join(','));
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
        this.$forceUpdate();
      });

      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&mode=${item.selectMode || 'scroll'}`
      });
    },
    //上传文件
    uploadedFile(data, index, lists, name) {
      if (data.success && data.statusCode == 200) {
        let list = data.object.map(item => {
          return {
            fileUrl: item.filePath,
            fkFileId: item.fileId,
            fkFileName: item.fileRealName
          };
        });
        this.form[name].push(...list);
      }
    },
    //删除文件
    removeFile(index, lists, name) {
      this.form[name].splice(index, 1);
    },
    removeAudio(index, name) {
      this.form[name].splice(index, 1);
    },
    //label右侧控件点击事件
    labelSlotClick(e) {
      if (e.labelSlotCallback) {
        e.labelSlotCallback(e);
      }
    },
    rightSlotClick(e) {
      if (e.labelSlotCallback) {
        e.rightSlotClick(e);
      }
    },
    validate() {
      let validVal = false;
      this.$refs.uForm.validate(valid => {
        validVal = valid;
      });
      return validVal;
    },
    //提交表单
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          this.$emit('submit', valid);
        }
      });
    },
    //删除附件
    deleteFile(prop, file) {
      this.$emit('deletFile', { file, prop });
    },
    previewFile(prop, file) {
      this.$emit('perviewFile', { file, prop });
    },
    inputClass(item) {
      let classList = [];
      if (item.type == 'select') {
        classList.push('is_select');
      }
      if (this.readOnly) {
        classList.push('is_readonly');
      }
      return classList.concat(item.class);
    },
    recStart(name) {
      if (this.intervalID != null) {
        this.recordTime = 58;
        clearInterval(this.intervalID);
      }
      let _self = this;
      _self.name = name;
      this.intervalID = setInterval(function() {
        _self.recordTime--;
        if (_self.recordTime == 0) {
          _self.$u.toast('最多录制60S语音信息');
          _self.stopRecord();
        }
      }, 1000);
      this.showModel = true;
      this.showModelLoading = true;
      wxInit.startRecord(_self);
    },
    stopRecord() {
      this.showModel = false;
      this.showModelLoading = false;
      // wxInit.stopRecord(this, this.recordTime);
      wxInit.stopRecordText(this, 'faultDeion');
      this.recordTime = 58;
      if (this.intervalID != null) {
        clearInterval(this.intervalID);
      }
    },
    async uploadFile(meRes, name, duration = 0) {
      let res = await this.ajax.wxFileUpload({ mediaId: meRes.serverId });
      if (res.success) {
        let list = res.object.map(item => {
          return {
            fileUrl: item.filePath,
            fkFileId: item.fileId,
            fkFileName: item.fileRealName,
            fileSuffix: item.fileExtension,
            fileId: item.fileId,
            duration
          };
        });
        this.form[name].push(...list);
        let audioList = this.form[name]
          .filter(item => item.fileUrl)
          .map(item => {
            return {
              src: encodeURI(
                `${
                  this.$store.state.common.baseHost
                }/ts-basics-bottom/fileAttachment/readFile/${item.fileUrl
                  .split('/')
                  .pop()}`
              ),
              duration: Number(item.duration || 0),
              key: item.fileId
            };
          });
        this.$zaudio.updateAudio(audioList);
        this.$zaudio.setRender(0);
      }
    },
    close() {
      this.stopRecord();
    },
    // 微信SDK
    //获取签名信息并初始化jdk
    getWxJsapiSignature() {
      let _self = this;
      _self.ajax
        .getWxJsapiSignature({
          REFERER: _self.token
        })
        .then(res => {
          wxInit.initwxJdk(res.object);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: $uni-spacing-col-base;
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.is_readonly,
/deep/.is_select input {
  pointer-events: none;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
.file-list {
  display: flex;
  .u-icon {
    margin-right: 8px;
  }
  .file-list-item-title {
    max-width: 360rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.audioList {
  width: 100%;
  .audioList_item {
    position: relative;
    .imt-audio {
      padding-right: 20px;
    }
    .closeAudio {
      position: absolute;
      right: 0;
      top: 10rpx;
    }
  }
}
.showModel {
  padding: 60rpx;
  /deep/ .u-loading {
    margin-left: 15rpx;
  }
  .showModelTitle {
    text-align: center;
  }
  .stop {
    text-align: center;
    width: auto;
    line-height: 60rpx;
    margin-top: 20rpx;
  }
}
</style>
