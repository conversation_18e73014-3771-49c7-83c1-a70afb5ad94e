<template>
  <view class="ts-container">
    <u-navbar title="我的报修" title-bold :custom-back="goBack"></u-navbar>
    <view class="search-box">
      <u-search
        shape="square"
        v-model="keywords"
        :show-action="false"
        placeholder="输入工单号\故障关键词搜索"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="right-box">
        <u-icon
          name="plus"
          size="36"
          @click="jumpToWorkOrderReporting"
        ></u-icon>
      </view>
    </view>
    <view class="prompt-box">
      <u-icon name="volume-up" size="36"></u-icon>
      <text class="prompt-text"
        >共提起工单{{ creatCount }}个，已完结{{ completeCount }}个，终止{{
          termination
        }}个</text
      >
    </view>
    <view class="work-order-box">
      <base-tabs-swiper
        ref="tabSwiper"
        class="tab-swiper-box"
        :list="tabList"
        badgeType="text"
        :current="currentTab"
        :is-scroll="false"
        @change="changeTab"
      ></base-tabs-swiper>
      <swiper
        class="swiper-box"
        :current="currentTab"
        :duration="300"
        @change="onTabChange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in tabList"
          :key="index"
        >
          <mescroll
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <my-work-order-item
              v-for="i in item.list"
              :key="i.workNumber"
              :item="i"
              @click="jumpToDetail(i.workNumber)"
            >
              <template #bottomRight="i">
                <view>
                  <view
                    class="item-operation-button edit-button"
                    v-if="
                      i.workStatus == 1 &&
                        $store.state.common.userInfo.empId == i.repairManId
                    "
                    @click.stop="editWorkOrder(i.workNumber, i.pkWsTaskId)"
                  >
                    编辑
                  </view>
                  <view
                    class="item-operation-button acceptance-button"
                    v-if="
                      i.workStatus == 4 &&
                        $store.state.common.userInfo.empId == i.repairManId
                    "
                    @click.stop="
                      acceptanceWorkOrder(i.workNumber, i.pkWsTaskId)
                    "
                  >
                    验收
                  </view>
                  <view
                    class="item-operation-button evaluate-button"
                    v-if="
                      i.workStatus == 5 &&
                        $store.state.common.userInfo.empId == item.repairManId
                    "
                    @click.stop="evaluateWorkOrder(i.workNumber, i.pkWsTaskId)"
                  >
                    评价
                  </view>
                </view>
              </template>
            </my-work-order-item>
          </mescroll>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import baseTabsSwiper from '../../components/base-tabs-swiper/base-tabs-swiper.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import myWorkOrderItem from './components/my-work-order-item.vue';
export default {
  name: 'my-work-order-list',
  mixins: [index],
  components: {
    baseTabsSwiper,
    mescroll,
    myWorkOrderItem
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
@import '../../assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.search-box {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.right-box {
  color: $uni-color-primary;
  margin-left: $uni-spacing-row-lg;
  > *:not(:last-child) {
    margin-right: 8px;
  }
}
.prompt-box {
  background-color: #ffffff;
  color: $uni-color-primary;
  height: 88rpx;
  padding: 0 $uni-spacing-row-lg;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.prompt-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
  margin-left: $uni-spacing-col-base;
}
.work-order-box {
  @include vue-flex(column);
  flex: 1;
}
.tab-swiper-box {
  margin-bottom: $uni-spacing-col-base;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
}
.item-operation-button {
  background-color: $uni-color-primary;
  font-size: $uni-font-size-sm;
  border-radius: 12px 12px 12px 0;
  width: 128rpx;
  height: 48rpx;
  line-height: 48rpx;
  color: #ffffff;
  text-align: center;
}
.acceptance-button {
  background-color: #2d76eb;
}
.evaluate-button {
  background-color: #f89e18;
}
.ellipsis-row-1 {
  min-width: 0;
  @include ellipsis;
}
</style>
