<template>
  <view class="my-work-order-item" @click.stop="onClick">
    <view class="item-top">
      <view class="item-title">
        {{ `[${item.workNumber}] ${item.faultDeion}` }}
      </view>
      <text class="item-status" :class="item.workStatus | statusColor">
        {{ item.workStatusName }}
      </text>
    </view>
    <view class="item-bottom">
      <view class="item-bottom-left">
        <view class="item-tag-content">
          <text class="item-tag" :class="item.faultEmergency | emergencyLevel">
            {{ item.faultEmergency | emergencyText }}
          </text>
          <text
            class="item-tag"
            :class="item.faultAffectScope | affectScopeLevel"
          >
            {{ item.faultAffectScope | affectScopeText }}
          </text>
          <text
            class="item-tag"
            :class="item.cqDays | expireLevel"
            v-if="
              item.requiredCompletionTime &&
                item.workStatus == 3 &&
                item.cqDays &&
                item.cqDays >= -2
            "
          >
            {{ item.cqDays | expireText }}
          </text>
          <text
            class="item-tag repulse-tag"
            v-if="item.noPassCounts > 0 && item.workStatus == 3"
          >
            打回
          </text>
        </view>
        <view
          class="item-info"
          v-if="
            isReception &&
              ['1', '2', '3', '5', '6', '7', '8'].indexOf(item.workStatus) >= 0
          "
        >
          报修人：{{ item.repairManName | filterRepairMan(item) }}
        </view>
        <view
          class="item-info"
          v-if="
            item.workStatus == 1 ||
              item.workStatus == 2 ||
              item.workStatus == 6 ||
              item.workStatus == 8
          "
        >
          {{ `报修时间：${item.createTime}` }}
          <text v-if="item.workStatus == 6 && isReception">
            {{ `&emsp;&emsp;确认时间：${item.confirmTime}` }}
          </text>
        </view>
        <view class="item-info" v-if="item.sendTime && item.workStatus == 2">
          {{ `派单时间：${item.sendTime}` }}
        </view>
        <view class="item-info" v-if="item.workStatus == 6 && !isReception">
          {{ `确认时间：${item.confirmTime}` }}
        </view>
        <view class="item-info">
          <text
            class="item-child-info"
            v-if="
              item.requiredCompletionTime &&
                (item.workStatus == 1 ||
                  item.workStatus == 2 ||
                  item.workStatus == 3 ||
                  item.workStatus == 7)
            "
          >
            {{ `要求完成：${item.requiredCompletionTime}` }}
          </text>
          <text
            class="item-child-info"
            v-if="
              item.workStatus == 3 ||
                item.workStatus == 4 ||
                item.workStatus == 5 ||
                item.workStatus == 6 ||
                item.workStatus == 7
            "
            >{{ `处理工时：${item.workHours}H` }}</text
          >
        </view>
        <view
          class="item-info"
          v-if="item.workStatus == 4 || item.workStatus == 5"
        >
          {{ `完成时间：${item.actualCompletionTime}` }}
        </view>
        <view
          class="item-info"
          v-if="
            item.workStatus == 2 ||
              item.workStatus == 3 ||
              item.workStatus == 4 ||
              item.workStatus == 5 ||
              item.workStatus == 6 ||
              item.workStatus == 7 ||
              item.workStatus == 8
          "
        >
          {{ item.fkUserName | filterHanldeUserMessage(item) }}
        </view>
        <view class="item-info" v-if="item.assistName && item.workStatus == 3">
          {{ `协助人：${item.assistName}` }}
        </view>
        <view class="item-info" v-if="item.workStatus == 7">
          {{ `暂停时间：${item.suspendedTime}` }}
        </view>
        <view class="item-info ellipsis-row-1" v-if="item.workStatus == 7">
          {{ `暂停原因：${item.suspendedRemark}` }}
        </view>
        <view class="item-info" v-if="item.workStatus == 8">
          {{ `终止时间：${item.terminationTime}` }}
        </view>
        <view class="item-info ellipsis-row-1" v-if="item.workStatus == 8">
          {{ `终止原因：${item.terminationRemark}` }}
        </view>
        <view
          class="item-info ellipsis-row-1 item-info-error"
          v-if="item.workStatus == 1 && item.backUserName"
        >
          {{
            `${item.backDeptName}-${item.backUserName}退回到服务台，${item.backRemark}`
          }}
        </view>
      </view>
      <view class="item-bottom-right">
        <slot name="bottomRight"></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'my-work-order-item',
  props: {
    item: {
      type: Object,
      default() {
        return {};
      }
    },
    isReception: {
      type: Boolean,
      default: () => false
    }
  },
  filters: {
    emergencyText(val) {
      let text = '';
      switch (val) {
        case 1:
          text = '非常紧急';
          break;
        case 2:
          text = '紧急';
          break;
        case 3:
          text = '常规处理';
          break;
      }
      return text;
    },
    emergencyLevel(val) {
      let level = '';
      switch (val) {
        case 1:
          level = 'most';
          break;
        case 2:
          level = 'more';
          break;
        case 3:
          level = 'normal';
          break;
      }
      return `tag-emergency-${level}`;
    },
    affectScopeText(val) {
      let text = '';
      switch (val) {
        case 1:
          text = '个人事件';
          break;
        case 2:
          text = '科室事件';
          break;
        case 3:
          text = '多科室事件';
          break;
        case 4:
          text = '全院事件';
          break;
      }
      return text;
    },
    affectScopeLevel(val) {
      let level = '';
      switch (val) {
        case 1:
          level = 'individual';
          break;
        case 2:
          level = 'single-dep';
          break;
        case 3:
          level = 'multiple-dep';
          break;
        case 4:
          level = 'whole-hospital';
          break;
      }
      return `tag-affect-${level}`;
    },
    expireLevel(val) {
      let level = '';
      if (val == 0) {
        level = 'today';
      } else if (val < 0) {
        level = 'will';
      } else if (val > 0) {
        level = 'over';
      }
      return `tag-expire-${level}`;
    },
    expireText(val) {
      if (val == 0) {
        return '今日到期';
      } else if (val < 0 && val >= -2) {
        return '即将到期';
      } else if (val > 0) {
        return '已超期';
      }
    },
    statusColor(val) {
      let colorClass = '';
      if (val == 1 || val == 2) {
        colorClass = 'status-error';
      } else if (val == 3 || val == 6) {
        colorClass = 'status-normal';
      } else if (val == 4 || val == 5 || val == 7 || val == 8) {
        colorClass = 'status-warning';
      }
      return colorClass;
    },
    filterRepairMan(name, item) {
      let newname = [item.repairManDeptName, name]
        .filter(item => item)
        .join('-');
      item.repairPhone && item.workStatus != 1
        ? (newname += `(${item.repairPhone})`)
        : null;
      return newname;
    },
    filterHanldeUserMessage(name, item) {
      return (
        `处理人：` +
        [item.fkUserDeptName, name].filter(item => item).join('-') +
        (item.fkUserPhone ? `(${item.fkUserPhone})` : '')
      );
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
@import '../../../assets/css/flex.scss';
.my-work-order-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
}
.item-top {
  @include vue-flex;
}
.item-bottom {
  align-items: flex-end;
  @include vue-flex;
}
.item-bottom-left {
  flex: 1;
  min-width: 0;
}
.item-title {
  @include multiLineEllipsis;
  color: $uni-text-color;
  font-weight: bold;
  flex: 1;
}
.item-status {
  color: #8a94a6;
  font-weight: 600;
}
.status-error {
  color: $uni-color-error;
}
.status-normal {
  color: $uni-color-primary;
}
.status-warning {
  color: $uni-color-warning;
}
.tag-emergency-most {
  background-color: $uni-color-error;
}
.tag-emergency-more {
  background-color: $uni-color-warning;
}
.tag-emergency-normal {
  background-color: $uni-color-normal;
}
.item-tag-content {
  line-height: 36rpx;
  margin-bottom: 12rpx;
}
.item-tag {
  border-radius: $uni-border-radius-sm;
  margin-right: $uni-spacing-row-xsm;
  color: #ffffff;
  font-size: $uni-font-size-xsm;
  padding: 4rpx $uni-spacing-row-base;
  &:last-child {
    margin-right: 0;
  }
}
.repulse-tag {
  background-color: #acacac;
}
.tag-affect-individual {
  background-color: #5aaaea;
}
.tag-affect-single-dep {
  background-color: #646cca;
}
.tag-affect-multiple-dep {
  background-color: #be6cf4;
}
.tag-affect-whole-hospital {
  background-color: #48c6a2;
}
.tag-urge {
  background-color: #fc8070;
}
.tag-expire-today {
  background-color: $uni-color-error;
}
.tag-expire-will {
  background-color: #f5780a;
}
.tag-expire-over {
  background-color: #f81a80;
}
.item-info {
  color: $uni-text-content-color;
  font-size: $uni-font-size-sm;
  line-height: 36rpx;
}
.item-info-error {
  color: $uni-color-error;
}
.item-child-info:nth-child(n) {
  margin-left: $uni-spacing-row-sm;
}
.item-child-info:first-child {
  margin-left: 0;
}
</style>
