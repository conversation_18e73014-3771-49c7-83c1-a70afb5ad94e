export default {
  data() {
    return {
      fromPage: '',
      keywords: '',
      creatCount: 0,
      completeCount: 0,
      termination: 0,
      tabList: [
        {
          name: '未开始',
          status: 1,
          type: 1,
          count: 0,
          list: []
        },
        {
          name: '进行中',
          status: 3,
          type: 2,
          count: 0,
          list: []
        },
        {
          name: '已结束',
          status: 6,
          type: 3,
          count: 0,
          list: []
        }
      ],
      currentTab: 0
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    if (opt && opt.tabIndex) {
      this.currentTab = opt.tabIndex;
    }
    this.getMyWorkOrderCount();
  },
  methods: {
    getMyWorkOrderCount() {
      this.ajax.getMyWorkOrderCount().then(res => {
        this.creatCount = Number(res.object.td);
        this.completeCount = Number(res.object.wc);
        this.termination = Number(res.object.zz);
      });
    },
    search(val) {
      this.keywords = val;
      this.$nextTick(() => {
        this.tabList.map((item, index) => {
          this.datasInit(index);
          this.$refs[`mescroll${index}`][0].downCallback();
        });
      });
    },
    clear() {},
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback, index) {
      this.ajax
        .getMyWorkOrder({
          pageNo: page.num,
          pageSize: page.size,
          fuzzy: this.keywords,
          type: 1,
          workStatus: this.tabList[index].status,
          mobileType: this.tabList[index].type,
          sord: 'desc',
          sidx: 'a.create_time'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        });
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      this.tabList[index]['count'] = totalCount;
    },
    datasInit(index) {
      this.tabList[index]['list'] = [];
    },
    jumpToWorkOrderReporting() {
      uni.navigateTo({
        url: '/pages/work-order-reporting/index'
      });
    },
    editWorkOrder(id, pkWsTaskId) {
      uni.navigateTo({
        url: `/pages/work-order-reporting/index?id=${id}&pkWsTaskId=${pkWsTaskId}&fromPage=my-work-order-list&tabIndex=${this.currentTab}`
      });
    },
    acceptanceWorkOrder(id, pkWsTaskId) {
      uni.navigateTo({
        url: `/pages/work-order-inspect/index?id=${id}&pkWsTaskId=${pkWsTaskId}&fromPage=my-work-order-list&tabIndex=${this.currentTab}`
      });
    },
    evaluateWorkOrder(id, pkWsTaskId) {
      uni.navigateTo({
        url: `/pages/work-order-evaluate/index?id=${id}&pkWsTaskId=${pkWsTaskId}&fromPage=my-work-order-list&tabIndex=${this.currentTab}`
      });
    },
    jumpToDetail(id) {
      uni.navigateTo({
        url: `/pages/work-order-detail/index?id=${id}&fromPage=my-work-order-list`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    }
  }
};
