import date from '../../assets/js/date.js';
export default {
  data() {
    return {
      fromPage: '',
      workOrderTotalCount: 0,
      dateQuickButtons: [],
      selectedDate: {
        beginTime: '',
        endTime: ''
      },
      pickerParams: {
        year: true,
        month: true,
        day: true
      },
      pickerVal: '',
      pickerProp: '',
      showPicker: false,
      statistic: [],
      seriesFormat: function(val, index, series) {
        if (index !== undefined) {
          return series[index].name + '：' + series[index].data;
        }
      },
      echartsEopts: { seriesTemplate: { smooth: true } },
      echartsEoptsssss: {
        legend: {
          left: 'center',
          bottom: 10
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: true,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}\n{d}%'
            },
            labelLine: {
              show: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '40',
                fontWeight: 'bold'
              }
            }
          }
        ]
      },
      uChartsOpts: {
        fontSize: 12,
        enableScroll: true,
        touchMoveLimit: 60,
        dataLabel: true,
        dataPointShape: true,
        dataPointShapeType: 'hollow',
        legend: {
          show: true,
          position: 'bottom'
        },
        extra: {
          ring: {
            ringWidth: 40,
            border: false
          }
        },
        title: {
          name: ''
        },
        subtitle: {
          name: ''
        }
      },
      echartsList: [
        {
          name: '影响范围',
          type: 'fault_affect_scope',
          chartsData: {}
        },
        {
          name: '紧急程度',
          type: 'fault_emergency',
          chartsData: {}
        }
      ],
      list: [],
      buttonName: '全部'
    };
  },
  async onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.initDateQuickButton();
    await this.getStatistic();
  },
  methods: {
    initDateQuickButton() {
      let todayObj = date.getDate(),
        weekObj = date.getWeekDate(),
        monthObj = date.getMonthDate(),
        yearObj = date.getYearDate();
      this.dateQuickButtons = [
        { ...{ name: '今天' }, ...todayObj },
        { ...{ name: '本周' }, ...weekObj },
        { ...{ name: '本月' }, ...monthObj },
        { ...{ name: '本年' }, ...yearObj },
        { name: '全部', start: '', end: '' }
      ];
    },
    async dateQuickClick(val) {
      this.buttonName = val.name;
      this.selectedDate.beginTime = val.start;
      this.selectedDate.endTime = val.end;
      await this.getStatistic();
    },
    clickDatePicker(prop) {
      this.showPicker = true;
      this.pickerProp = prop;
    },
    pickerConfirm(e) {
      this.$set(
        this.selectedDate,
        this.pickerProp,
        date.formatTime(e, this.clickField)
      );
      if (
        this.selectedDate.beginTime &&
        this.selectedDate.endTime &&
        this.selectedDate.beginTime > this.selectedDate.endTime
      ) {
        this.selectedDate.beginTime = '';
        this.selectedDate.endTime = '';
      }
      this.buttonName = '';
    },
    async clickSearch() {
      await this.getStatistic();
    },
    async getStatistic() {
      let statisticDatas = await this.getWorkOrderSituation(
        'getWorkOrderSituation',
        'work_status'
      );
      if (JSON.stringify(statisticDatas) == '{}') return false;

      this.workOrderTotalCount = 0;
      for (let i = 1; i <= 8; i++) {
        let orderNumber = Number(statisticDatas[i]);
        isNaN(orderNumber) ? (orderNumber = 0) : null;
        this.workOrderTotalCount += orderNumber;
      }

      this.statistic = [
        {
          name: '待派单',
          value: statisticDatas[1],
          color: '#ff9900'
        },
        {
          name: '待接单',
          value: statisticDatas[2],
          color: '#ff9900'
        },
        {
          name: '处理中',
          value: statisticDatas[3],
          color: '#ff9900'
        },
        {
          name: '待确认',
          value: statisticDatas[4],
          color: '#ff9900'
        },
        {
          name: '待评价',
          value: statisticDatas[5],
          color: '#fa3534'
        },
        {
          name: '已暂停',
          value: statisticDatas[7],
          color: '#fa3534'
        },
        {
          name: '已终止',
          value: statisticDatas[8],
          color: '#fa3534'
        },
        {
          name: '已办结',
          value: statisticDatas[6],
          color: '#fa3534'
        },
        {
          name: '完结率',
          value: statisticDatas['endProportion'] + '%',
          color: '#005bac'
        },
        {
          name: '验收通过率',
          value: statisticDatas['passRate'] + '%',
          color: '#005bac'
        },
        {
          name: '平均工时',
          value: statisticDatas['avgWorkHours'],
          color: '#005bac'
        },
        {
          name: '综合评分',
          value: statisticDatas['avgScore'],
          color: '#005bac'
        }
      ];
      await this.echartsList.map(async (item, index) => {
        let datas = await this.getWorkOrderSituation(
          'getWorkOrderType',
          item.type
        );
        this.$set(item.chartsData, 'series', [
          {
            data: datas,
            format: this.seriesFormat
          }
        ]);
      });
      this.datasInit();
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    async getWorkOrderSituation(type, statusType) {
      let data = {};
      await this.ajax[type]({
        ...this.selectedDate,
        ...{ statusType: statusType }
      }).then(res => {
        data = res.object;
      });
      return data;
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    getListData(page, successCallback, errorCallback) {
      let param = {
        pageNo: page.num,
        pageSize: page.size,
        sidx: null
      };
      if (this.buttonName === '今天') {
        param.type = 0;
        param.dayOrMonthType = 0;
      }
      if (this.buttonName === '本周') {
        param.type = 1;
        param.dayOrMonthType = 0;
      }
      if (this.buttonName === '本月') {
        param.type = 2;
        param.dayOrMonthType = 0;
      }
      if (this.buttonName === '本年') {
        param.type = 3;
        param.dayOrMonthType = 1;
      }
      if (this.buttonName === '全部') {
        param.type = 4;
        param.dayOrMonthType = 3;
      }
      if (this.buttonName === '') {
        param.type = 5;
        param.dayOrMonthType = 0;
        param = { ...param, ...this.selectedDate };
      }
      this.ajax
        .getDeptUserReceiveWorkSheetDatas({
          ...param
        })
        .then(res => {
          successCallback(res.rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    }
  }
};
