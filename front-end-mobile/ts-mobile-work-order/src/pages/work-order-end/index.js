export default {
  data() {
    return {
      formList: [
        {
          title: '备注',
          prop: 'remark',
          type: 'textarea',
          placeholder: '请备注说明',
          maxlength: 100,
          required: true
        }
      ],
      form: {
        pkWsTaskId: '',
        remark: '',
        yesOrNo: '1',
        fkUserId: ''
      },
      rules: {
        remark: [
          {
            required: true,
            message: '请备注说明',
            trigger: ''
          }
        ]
      },
      workStatus: ''
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkUserId = opt.fkUserId;
    this.workStatus = opt.workStatus;
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.workSheetTerminated(this.form).then(res => {
        if (this.fromPage == 'work-order-reception') {
          uni.reLaunch({
            url: '/pages/work-order-reception/index'
          });
        } else {
          let path = '/pages/my-work-order-list/index';
          if (this.workStatus == 3) {
            path = `${path}?tabIndex=1`;
          }
          uni.reLaunch({
            url: path
          });
        }
      });
    }
  }
};
