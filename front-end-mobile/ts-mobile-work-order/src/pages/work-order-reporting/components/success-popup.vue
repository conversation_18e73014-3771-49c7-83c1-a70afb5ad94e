<template>
  <u-popup
    border-radius="16"
    v-model="value"
    mode="center"
    :mask="true"
    :mask-close-able="false"
    :closeable="false"
  >
    <view class="success-content">
      <view class="success-img-wrap">
        <image
          class="success-img"
          mode="aspectFill"
          src="../../../assets/img/success.png"
        />
      </view>
      <view class="success-text">上报成功</view>
      <view
        class="success-sub-text"
        :class="
          toastContent == '请稍候，工程师将尽快为您处理工单' ? '' : 'red-toast'
        "
      >
        {{ toastContent }}
      </view>
      <u-button type="primary" class="main-button" @click="confirmMainButton"
        >查看我的工单</u-button
      >
      <view class="sub-button">
        <text @click="confirmSubButton">返回工作台</text>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'success-popup',
  props: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    toastContent: {
      type: String,
      default: () => '请稍候，工程师将尽快为您处理工单'
    }
  },
  methods: {
    confirmMainButton() {
      this.$emit('mainCallBack');
    },
    confirmSubButton() {
      this.$emit('subCallBack');
    }
  }
};
</script>

<style lang="scss" scoped>
.success-content {
  background-color: #ffffff;
  padding: 64rpx;
}
.success-img-wrap {
  text-align: center;
  margin-bottom: 20rpx;
}
.success-img {
  width: 200rpx;
  height: 200rpx;
}
.success-text {
  color: $uni-text-color;
  text-align: center;
  font-size: 40rpx;
  font-weight: 600;
}
.success-sub-text {
  color: $uni-text-content-color;
  font-size: 28rpx;
  margin-top: 8px;
  margin-bottom: 60rpx;
}
.main-button {
  margin-bottom: 32rpx;
  font-size: 32rpx;
}
.sub-button {
  text-align: center;
  color: $uni-color-primary;
}
.red-toast {
  color: $u-type-error;
  font-weight: 600;
}
</style>
