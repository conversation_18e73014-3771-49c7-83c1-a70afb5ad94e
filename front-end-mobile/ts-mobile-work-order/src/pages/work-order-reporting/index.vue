<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar title="工单上报" title-bold :custom-back="goBack"></u-navbar>
    <base-form
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      submit-title="报修"
      @submit="submit"
      @perviewFile="handlePreview"
      @deletFile="handleDeletFile"
      @init-finished="handleBaseFormFirstInit"
    ></base-form>
    <seccess-popup
      v-model="successShow"
      :toastContent="toastContent"
      @mainCallBack="jumpToMyWorkOrder"
      @subCallBack="jumpToWorkBench"
    ></seccess-popup>
  </view>
</template>

<script>
import index from './index.js';
import baseForm from '../../components/base-form/base-form.vue';
import seccessPopup from './components/success-popup.vue';
export default {
  name: 'work-order-reporting',
  mixins: [index],
  components: {
    baseForm,
    seccessPopup
  }
};
</script>

<style lang="scss" scoped></style>
