import { Base64 } from '@/assets/js/base64.min';

export default {
  data() {
    return {
      fromPage: '',
      finishedRenderType: false,
      formList: [
        {
          title: '报修地址',
          prop: 'repairDeptAddress',
          type: 'text',
          placeholder: '请填写报修地址',
          maxlength: 70,
          required: true
        },
        {
          title: '报修电话',
          prop: 'repairPhone',
          type: 'text',
          placeholder: '请输入报修电话',
          required: true
        },
        {
          title: '处理科室',
          prop: 'businessDeptName',
          propVal: 'businessDeptId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择处理科室',
          required: true,
          optionList: null,
          relationProp: [
            {
              prop: 'fkFaultType',
              propVal: 'fkFaultTypeId'
            },
            {
              prop: 'fkUser',
              propVal: 'fkUserId'
            }
          ]
        },
        {
          title: '业务类型',
          prop: 'fkFaultType',
          propVal: 'fkFaultTypeId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择业务类型',
          required: true,
          optionList: null,
          searchParams: [
            {
              name: 'fkDeptId',
              value: 'businessDeptId',
              message: '请先选择处理科室'
            }
          ],
          searchApi: '/faultType/selectFaultTypeList/1',
          relationProp: [
            {
              prop: 'fkUser',
              propVal: 'fkUserId'
            }
          ]
        },
        {
          title: '故障设备',
          prop: 'faultEquipmentName',
          propVal: 'fkFaultEquipmentId',
          type: 'text',
          placeholder: '请输入故障设备名称',
          maxlength: 30,
          required: false,
          retrieval: true
        },
        {
          title: '故障描述',
          prop: 'faultDeion',
          type: 'textarea',
          labelSlot: '+添加到常用故障',
          labelSlotClass: 'u-type-primary u-font-xs',
          labelSlotCallback: e => {
            this.saveFaultCommon(e);
          },
          placeholder:
            '在上报问题时，便于我们更加效率的给您解决，请尽量描述清楚故障情况，最好是能够上传附件，谢谢',
          required: true,
          maxlength: 500
        },
        {
          title: '上传附件',
          prop: 'wsFileInputVo',
          imgVal: 'preWsFileInputVo',
          fileVal: 'wsFileList',
          type: 'file',
          name: 'file',
          header: {
            token: this.$store.state.common.token
          },
          action: `${this.$store.state.common.baseHost}/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk`,
          placeholder: '上传附件',
          required: false
        },
        // {
        //   title: '报修录音',
        //   prop: 'audioList',
        //   type: 'audio',
        //   name: 'audio',
        //   labelSlot: 'mic',
        //   placeholder: '录音上传',
        //   required: false
        // },
        {
          title: '影响范围',
          prop: 'faultAffectScopeName',
          propVal: 'faultAffectScope',
          type: 'select',
          mode: 'select',
          optionList: [
            {
              label: '个人事件',
              value: '1'
            },
            {
              label: '科室事件',
              value: '2'
            },
            {
              label: '多科室事件',
              value: '3'
            },
            {
              label: '全院事件',
              value: '4'
            }
          ],
          placeholder: '请选择影响范围',
          required: true
        },
        {
          title: '紧急程度',
          prop: 'faultEmergencyName',
          propVal: 'faultEmergency',
          type: 'select',
          mode: 'select',
          optionList: [
            {
              value: '1',
              label: '非常紧急'
            },
            {
              label: '紧急',
              value: '2'
            },
            {
              label: '常规处理',
              value: '3'
            }
          ],
          placeholder: '请选择紧急程度',
          required: true
        },
        {
          title: '要求完成时间',
          prop: 'requiredCompletionTime',
          type: 'select',
          mode: 'time',
          params: {
            year: true,
            month: true,
            day: true
          },
          filed: 'yy-MM-dd',
          placeholder: '请选择要求完成时间',
          required: false
        },
        // {
        //   title: '处理人',
        //   prop: 'fkUser',
        //   propVal: 'fkUserId',
        //   type: 'select',
        //   mode: 'person',
        //   chooseType: 'radio',
        //   getListType: 'scollSearch',
        //   searchParams: [
        //     {
        //       name: 'faultTypeId',
        //       value: 'fkFaultTypeId',
        //       message: '请先选择故障类型'
        //     }
        //   ],
        //   searchApi: '/workSheetPeopple/getPeopleInfoList',
        //   placeholder: '请选择处理人',
        //   required: false
        // },
        {
          title: '报修备注',
          prop: 'remark',
          type: 'textarea',
          placeholder: '请输入报修备注',
          maxlength: 70,
          required: false
        }
      ],
      form: {
        repairManDeptName: '',
        repairManDeptId: '',
        businessDeptName: '',
        businessDeptId: '',
        fkFaultTypeId: '',
        fkFaultType: '',
        fkFaultEquipmentId: '',
        faultEquipmentName: '',
        repairDeptAddress: '',
        faultDeion: '',
        wsFileInputVo: [],
        preWsFileInputVo: [],
        audioList: [],
        wsFileList: [],
        faultAffectScope: '1',
        faultAffectScopeName: '个人事件',
        faultEmergency: '3',
        faultEmergencyName: '常规处理',
        requiredCompletionTime: '',
        fkUser: '',
        fkUserDeptId: '',
        fkUserId: '',
        remark: '',
        repairManId: '',
        repairPhone: '',
        repairType: 2
      },
      rules: {
        repairManId: [
          {
            required: true,
            message: '请选择报修科室',
            trigger: ''
          }
        ],
        businessDeptName: [
          {
            required: true,
            message: '请选择处理科室',
            trigger: ''
          }
        ],
        fkFaultType: [
          {
            required: true,
            message: '请选择故障类型',
            trigger: ''
          }
        ],
        faultDeion: [
          {
            required: true,
            message: '请描述故障内容',
            trigger: ''
          }
        ],
        faultAffectScopeName: [
          {
            required: true,
            message: '请选择影响范围',
            trigger: ''
          }
        ],
        faultEmergencyName: [
          {
            required: true,
            message: '请选择紧急程度',
            trigger: ''
          }
        ],
        repairPhone: [
          {
            required: true,
            message: '请输入报修电话',
            trigger: ''
          }
        ],
        repairDeptAddress: [
          {
            required: true,
            message: '请输入报修地址',
            trigger: ''
          }
        ]
      },
      successShow: false,
      toastContent: '请稍候，工程师将尽快为您处理工单',
      opt: {},
      isReception: false //是否是服务台操作工单数据
    };
  },
  async onLoad(opt) {
    let config = await this.getWorkSheetSetConfig();
    this.opt = opt;
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    let orderReportConfig = JSON.parse(
      uni.getStorageSync('orderReportConfig') || '{}'
    );
    if (this.$store.state.common.globalSetting.orgCode == 'lxzyyy') {
      let fIndex = this.formList.findIndex(
        f => f.prop === 'requiredCompletionTime'
      );
      fIndex != -1 && this.formList.splice(fIndex, 1);

      let rIndex = this.formList.findIndex(f => f.prop === 'remark');
      rIndex != -1 && this.formList.splice(rIndex, 1);
    }

    this.isReception =
      opt.fromPage == 'work-order-reception' ||
      orderReportConfig.isReception ||
      false;
    if (opt && opt.equipmentName) {
      this.form.faultEquipmentName = opt.equipmentName;
      this.form.fkFaultEquipmentId = opt.fkFaultEquipmentId;
    }
    if (opt && opt.id) {
      await this.ajax.getWorkOrderDetail(opt.id).then(res => {
        let baseInfo = res.object.wsWsSheetInfoOutVo;
        this.finishedRenderType = baseInfo.workStatus === '6';

        this.form.businessDeptName = baseInfo.businessDeptName;
        this.form.businessDeptId = baseInfo.businessDeptId;
        this.form.fkFaultType = baseInfo.fkFaultType;
        this.form.fkFaultTypeId = baseInfo.fkFaultTypeId;
        this.form.fkFaultEquipmentId = baseInfo.fkFaultEquipmentId;
        this.form.faultEquipmentName = baseInfo.faultEquipmentName;
        this.form.repairDeptAddress = baseInfo.repairDeptAddress;
        this.form.faultDeion = baseInfo.faultDeion;

        this.form.repairManId = baseInfo.repairManId;
        this.form.repairManName = baseInfo.repairManName;
        this.form.repairManDeptId = baseInfo.repairManDeptId;
        this.form.repairManDeptName = baseInfo.repairManDeptName;

        this.form.wsFileInputVo = [];
        this.form.wsFileList = [];

        if (config.multiHospitalDistrict == 1) {
          this.$set(
            this.form,
            'fkHospitalDistrictName',
            baseInfo.hospitalDistrictName
          );
          this.$set(
            this.form,
            'fkHospitalDistrictId',
            baseInfo.fkHospitalDistrictId
          );
        }
        res.object.wsFileOutVoList.forEach(item => {
          if (/gif|jpg|jpeg|png|bmp/i.test(item.fkFileName.toLowerCase())) {
            if (item.fileUrl.indexOf('ts-basics-bottom') == -1) {
              item.fileUrl =
                '/ts-document/attachment/downloadFile/' + item.fkFileId;
            }
            this.form.wsFileInputVo.push(item);
          } else if (
            /doc|docx|xls|xlsx|pdf|ppt|pptx|txt|mp4/i.test(
              item.fkFileName.toLowerCase()
            ) ||
            item.fkFileName.toLowerCase().indexOf('.mhtml') >= 0
          ) {
            this.form.wsFileList.push(item);
          }
        });
        // this.form.wsFileInputVo = res.object.wsFileOutVoList.map(item => {
        //   item.url = `${this.$store.state.common.baseHost}${item.fileUrl}`;
        //   return item;
        // });
        this.form.preWsFileInputVo = this.form.wsFileInputVo.map(item => {
          item.url = `${this.$store.state.common.baseHost}${item.fileUrl}`;
          return item;
        });
        this.form.faultAffectScope = baseInfo.faultAffectScope;
        this.form.faultAffectScopeName = baseInfo.faultAffectScopeValue;
        this.form.faultEmergency = baseInfo.faultEmergency;
        this.form.faultEmergencyName = baseInfo.faultEmergencyValue;
        this.form.requiredCompletionTime =
          baseInfo.requiredCompletionTime || '';
        this.form.remark = baseInfo.remark;
        this.form.repairPhone = baseInfo.repairPhone;
        this.form.workNumber = baseInfo.workNumber;
        this.form.pkWsTaskId = opt.pkWsTaskId;
        setTimeout(() => {
          this.form.fkUser = baseInfo.fkUserName || '';
          this.form.fkUserId = baseInfo.fkUserId;
          this.$nextTick(() => {
            this.$refs.baseForm.personLabel['fkUser'] = [
              {
                name: this.form.fkUser,
                userId: this.form.fkUserId
              }
            ];
          });
        });
      });
    } else {
      this.form.repairPhone = this.$store.state.common.userInfo.phone;
    }
    if (!this.form.repairManDeptId) {
      this.form.repairManDeptId = this.$store.state.common.userInfo.empDeptId;
      this.form.repairManDeptName = this.$store.state.common.userInfo.orgName;
    }
    if (!this.form.repairManId) {
      this.form.repairManId = this.$store.state.common.userInfo.empId;
      this.form.repairManName = this.$store.state.common.userInfo.employeeName;
    }

    if (config.multiHospitalDistrict == 1) {
      this.form.fkHospitalDistrictId
        ? null
        : (this.form.fkHospitalDistrictId = '');
      this.form.fkHospitalDistrictName
        ? null
        : (this.form.fkHospitalDistrictName = '');
      this.rules = {
        ...this.rules,
        fkHospitalDistrictName: [
          {
            required: true,
            message: '请选择报修院区',
            trigger: ''
          }
        ]
      };
      let list = await Promise.all([
        config.hospitalDistrictList.map(item => {
          return {
            label: item.hospitalDistrictName,
            value: item.pkHospitalDistrictId
          };
        })
      ]);
      this.formList.unshift({
        title: '报修院区',
        prop: 'fkHospitalDistrictName',
        propVal: 'fkHospitalDistrictId',
        type: 'select',
        mode: 'select',
        optionList: list[0],
        placeholder: '请选择报修院区',
        required: true
      });
    }

    if (config.repairAddressRequired != 1) {
      delete this.rules.repairDeptAddress;
      let repairePlaceIndex = this.formList.findIndex(
        item => item.prop == 'repairDeptAddress'
      );
      if (repairePlaceIndex >= 0) {
        let formListItem = this.formList[repairePlaceIndex];
        delete formListItem.required;
      }
    }

    if (this.isReception) {
      this.formList.unshift(
        {
          title: '报修科室',
          prop: 'repairManDeptName',
          propVal: 'repairManDeptId',
          type: 'select',
          mode: 'dept',
          selectMode: 'once',
          chooseType: 'radio',
          placeholder: '请选择报修科室',
          required: true,
          changeCallback: this.handleRepairManDeptChange
        },
        {
          title: '报修人',
          prop: 'repairManName',
          propVal: 'repairManId',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          getListType: 'scollSearch',
          searchApi: '/ts-basics-bottom/employee/getEmployeePageList',
          searchApiType: 'POST',
          searchType: 'fullApiPost',
          placeholder: '请选择报修人',
          personInfoProp: {
            name: 'employeeName',
            describe: 'orgName',
            key: 'employeeId',
            sex: 'gender',
            headImg: 'avatar'
          },
          searchParams: [
            {
              name: 'orgId',
              value: 'repairManDeptId'
            }
          ],
          required: true,
          changeCallback: this.handleRepairManChange
        }
      );
      this.rules = {
        ...this.rules,
        ...{
          repairManDeptName: [
            {
              required: true,
              message: '请选择报修科室',
              trigger: ''
            }
          ],
          repairManName: [
            {
              required: true,
              message: '请选择报修人',
              trigger: ''
            }
          ]
        }
      };

      this.formList.forEach(item => {
        if (item.title == '报修电话') {
          item.disabled = true;
        }
      });

      for (let key in orderReportConfig.formData || {}) {
        this.$set(this.form, key, orderReportConfig.formData[key] || '');
      }
    }
    this.getFaultTypeDeptList();
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
      if (this.finishedRenderType) {
        this.formList.find(f => f.propVal === 'fkFaultTypeId').relationProp = [];
        this.formList.forEach(item => {
          if (item.title != '业务类型') {
            item.disabled = true;
          }
        });
      }
    });
  },
  watch: {
    'form.businessDeptId': function() {
      this.handleBusinessDeptIdChange();
    }
  },
  methods: {
    qrCode() {
      let opt = this.opt;
      if (opt && opt.qrCode) {
        this.ajax
          .getSelectOne({ pkFaultEquipmentId: opt.qrCode })
          .then(async res => {
            if (res.object) {
              this.form.repairDeptAddress = res.object.equipmentRemark;
              this.form.faultEquipmentName = res.object.equipmentName;
              this.form.fkFaultEquipmentId = res.object.pkFaultEquipmentId;
              let deptObj = this.$refs.baseForm.selctAllObj.businessDeptName.find(
                e => e.label == '设备部'
              );
              this.form.businessDeptName = deptObj.label || '';
              this.form.businessDeptId = deptObj.value || '';
              if (this.form.businessDeptId) {
                let optionList = await this.getDatas(
                  `/faultType/selectFaultTypeList/1?fkDeptId=${this.form.businessDeptId}`
                );
                this.$set(
                  this.$refs.baseForm.selctAllObj,
                  'fkFaultType',
                  optionList
                );
                let option = optionList.find(e => e.label == '设备类');
                this.form.fkFaultTypeId = option.value || '';
                this.form.fkFaultType = option.label || '';
              }
            } else {
              this.$u.toast('未查询到设备信息');
            }
          });
      }
    },
    async getDatas(api) {
      let datas = await this.ajax.getDatas(api);
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    getFaultTypeDeptList() {
      this.ajax.getFaultTypeDeptList().then(res => {
        let fkFaultTypeDeptList = res.object.map(item => {
          return {
            value: item.deptId,
            label: item.deptName
          };
        });
        this.fkFaultTypeDeptList = res.object;

        let faultTypeDeptIndex = this.formList.findIndex(
          item => item.propVal == 'businessDeptId'
        );
        this.$set(
          this.formList[faultTypeDeptIndex],
          'optionList',
          fkFaultTypeDeptList
        );
        this.qrCode();
      });
    },
    //保存故障描述
    saveFaultCommon(e) {
      if (this.form[e.prop]) {
        this.ajax
          .saveFaultCommon({
            falutDeion: this.form[e.prop]
          })
          .then(res => {
            this.$u.toast('添加成功');
          });
      } else {
        this.$u.toast('故障描述不能为空');
      }
    },
    submit() {
      let fileItem = this.formList.find(item => item.title == '上传附件');
      if (
        fileItem.required &&
        !this.form.wsFileInputVo.length &&
        !this.form.wsFileList.length
      ) {
        this.$u.toast('请上传附件');
        return;
      }
      this.form.wsFileInputVo = (this.form.wsFileInputVo || []).concat(
        ...(this.form.wsFileList || [])
      );
      const handleResponse = (res) => {
        if (this.isReception) {
          uni.reLaunch({
            url: '/pages/work-order-reception/index',
          });
        } else {
          this.successShow = true;
          this.toastContent = res.object.workSheetCount
            ? `${res.object.businessDeptName}还有${res.object.workSheetCount}个工单待处理，请耐心等待`
            : '请稍候，工程师将尽快为您处理工单';
        }
      };

      if (!this.finishedRenderType) {
        this.ajax.saveWorkOrder(this.form).then(handleResponse);
      } else {
        this.ajax.updateByWorkNumber({
          workNumber: this.form.workNumber,
          fkFaultTypeId: this.form.fkFaultTypeId,
        }).then(handleResponse);
      }
    },
    jumpToMyWorkOrder() {
      uni.reLaunch({
        url: '/pages/my-work-order-list/index'
      });
    },
    goBack() {
      uni.removeStorageSync('orderReportConfig');
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      } else if (this.fromPage) {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/ts-mobile-work-order/pages/' + this.fromPage + '/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workBench'
      });
    },
    handlePreview(data) {
      let filePath = `${this.$documentPreviewHost}${
        data.file.fileUrl
      }?fullfilename=${data.file.fkFileId}.${data.file.fileSuffix ||
        data.file.fkFileName.split('.')[1]}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
      });
    },
    handleBusinessDeptIdChange() {
      this.fkFaultTypeDeptList &&
        this.fkFaultTypeDeptList.forEach(item => {
          if (item.deptId == this.form.businessDeptId) {
            this.formList.forEach(formItem => {
              if (formItem.title == '上传附件') {
                formItem.required = item.fileRequired ? true : false;
              }
            });
          }
        });
      if (this.isReception) {
        let handlerPersonIndex = this.formList.findIndex(
          item => item.title == '处理人'
        );
        if (
          this.form.businessDeptId ==
          this.$store.state.common.userInfo.empDeptId
        ) {
          if (handlerPersonIndex >= 0) {
            return;
          }
          let timeIndex = this.formList.findIndex(
            item => item.title == '要求完成时间'
          );
          if (timeIndex == -1) return;
          this.formList.splice(timeIndex, 0, {
            title: '处理人',
            prop: 'fkUser',
            propVal: 'fkUserId',
            type: 'select',
            mode: 'person',
            chooseType: 'radio',
            getListType: 'scollSearch',
            searchParams: [
              {
                name: 'faultTypeId',
                value: 'fkFaultTypeId',
                message: '请先选择故障类型'
              },
              {
                name: 'deptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/workSheetPeopple/getNoPagePeopleInfoList',
            placeholder: '请选择处理人',
            personInfoProp: {
              describe: 'deptName',
              name: 'name',
              nameFormatt: ['name', 'processCountString'],
              key: 'userId'
            },
            required: false
          });
        } else {
          if (handlerPersonIndex >= 0) {
            this.formList.splice(handlerPersonIndex, 1);
          }
        }
        this.form.fkUser = '';
        this.form.fkUserId = '';
      }
    },
    handleDeletFile(data) {
      if (this.form[data.prop.fileVal]) {
        this.form[data.prop.fileVal] = this.form[data.prop.fileVal].filter(
          item => {
            return item.fkFileId !== data.file.fkFileId;
          }
        );
      }
    },
    handleBaseFormFirstInit() {
      if (
        this.isReception &&
        this.$refs.baseForm &&
        this.$refs.baseForm.personLabel &&
        !(this.$refs.baseForm.personLabel.repairManName || []).length
      ) {
        this.$refs.baseForm.personLabel['repairManName'] = this.form.repairManId
          ? [
              {
                employeeName: this.form.repairManName,
                employeeId: this.form.repairManId
              }
            ]
          : [];
      }
      if (
        this.isReception &&
        this.$refs.baseForm &&
        this.$refs.baseForm.deptLabel &&
        !(this.$refs.baseForm.deptLabel.repairManDeptName || []).length
      ) {
        this.$refs.baseForm.deptLabel['repairManDeptName'] = this.form
          .repairManDeptId
          ? [
              {
                name: this.form.repairManDeptName,
                id: this.form.repairManDeptId
              }
            ]
          : [];
      }
    },
    handleRepairManChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].employeeName;
        propVal = data[0].employeeId;

        if (this.isReception) {
          this.$set(this.form, 'repairManDeptName', data[0].orgName);
          this.$set(this.form, 'repairManDeptId', data[0].orgId);
          this.$refs.baseForm.deptLabel = {
            repairManDeptName: [
              {
                name: data[0].orgName,
                id: data[0].orgId
              }
            ]
          };
        }
      }
      this.$set(this.form, props.prop, prop);
      this.$set(this.form, props.propVal, propVal);
    },
    handleRepairManDeptChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].name;
        propVal = data[0].id;
      }
      this.$set(this.form, props.prop, prop);
      this.$set(this.form, props.propVal, propVal);

      this.$set(this.form, 'repairManName', '');
      this.$set(this.form, 'repairManId', '');
      this.$refs.baseForm.personLabel = {};
    },
    async getWorkSheetSetConfig() {
      let res = await this.ajax.getWorkSheetSetConfig();
      if (!res.success || !res.object) {
        return {};
      }

      return {
        ...res.object
      };
    }
  }
};
