export default {
  data() {
    return {
      formList: [
        {
          title: '处理科室',
          prop: 'businessDeptName',
          propVal: 'businessDeptId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择处理科室',
          required: true,
          optionList: null,
          relationProp: [
            {
              prop: 'fkFaultType',
              propVal: 'fkFaultTypeId'
            }
          ]
        },
        {
          title: '业务类型',
          prop: 'fkFaultType',
          propVal: 'fkFaultTypeId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择故障类型',
          required: true,
          optionList: null,
          searchParams: [
            {
              name: 'fkDeptId',
              value: 'businessDeptId',
              message: '请先选择处理科室'
            }
          ],
          searchApi: '/faultType/selectFaultTypeList/1'
        },
        {
          title: '处理人',
          prop: 'fkUser',
          propVal: 'fkUserId',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          getListType: 'scollSearch',
          searchParams: [
            {
              name: 'faultTypeId',
              value: 'fkFaultTypeId',
              message: '请先选择故障类型'
            },
            {
              name: 'deptId',
              value: 'businessDeptId'
            }
          ],
          searchApi: '/workSheetPeopple/getNoPagePeopleInfoList',
          placeholder: '请选择处理人',
          personInfoProp: {
            describe: 'deptName',
            name: 'name',
            nameFormatt: ['name', 'processCountString'],
            key: 'userId'
          },
          required: true
        },
        {
          title: '转发说明',
          prop: 'remark',
          type: 'textarea',
          placeholder: '请输入转发说明',
          required: true
        }
      ],
      form: {
        businessDeptName: '',
        businessDeptId: '',
        fkFaultType: '',
        fkFaultTypeId: '',
        fkFormerUserId: '',
        fkUser: '',
        fkUserId: '',
        pkWsTaskId: '',
        remark: ''
      },
      rules: {
        businessDeptName: [
          {
            required: true,
            message: '请选择处理科室',
            trigger: ''
          }
        ],
        fkFaultType: [
          {
            required: true,
            message: '请选择故障类型',
            trigger: ''
          }
        ],
        fkUser: [
          {
            required: true,
            message: '请选择处理人',
            trigger: ''
          }
        ],
        remark: [
          {
            required: true,
            message: '请输入备注说明',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkFormerUserId = this.$store.state.common.userInfo.empId;
    this.oldBusinessDeptId = opt.businessDeptId || '';
    this.getWorkOrderDetail(opt.id);
    this.getFaultTypeDeptList();
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  watch: {
    'form.businessDeptId': {
      handler: function(val) {
        this.handleResendDeptIdChange(val);
      }
    }
  },
  methods: {
    getFaultTypeDeptList() {
      this.ajax.getFaultTypeDeptList().then(res => {
        let fkFaultTypeDeptList = res.object.map(item => {
          return {
            value: item.deptId,
            label: item.deptName
          };
        });
        this.$set(this.formList[0], 'optionList', fkFaultTypeDeptList);
      });
    },
    getWorkOrderDetail(id) {
      this.ajax.getWorkOrderDetail(id).then(res => {
        let baseInfo = res.object.wsWsSheetInfoOutVo;
        this.form.businessDeptName = baseInfo.businessDeptName;
        this.form.businessDeptId = baseInfo.businessDeptId;
        this.form.fkFaultType = baseInfo.fkFaultType;
        this.form.fkFaultTypeId = baseInfo.fkFaultTypeId;
      });
    },
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      !this.form.fkUserId ? (this.form.fkUserId = null) : null;
      !this.form.fkFaultTypeId ? (this.form.fkFaultTypeId = null) : null;
      this.ajax.workSheetResend(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/work-order-handle-list/index'
        });
      });
    },
    //处理科室改变时，修改表单展示数据
    handleResendDeptIdChange(val) {
      if (!this.oldBusinessDeptId) {
        return;
      }
      let dept = [
          {
            title: '业务类型',
            prop: 'fkFaultType',
            propVal: 'fkFaultTypeId',
            type: 'select',
            mode: 'select',
            placeholder: '请选择故障类型',
            required: true,
            optionList: null,
            searchParams: [
              {
                name: 'fkDeptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/faultType/selectFaultTypeList/1'
          },
          {
            title: '处理人',
            prop: 'fkUser',
            propVal: 'fkUserId',
            type: 'select',
            mode: 'person',
            chooseType: 'radio',
            getListType: 'scollSearch',
            searchParams: [
              {
                name: 'faultTypeId',
                value: 'fkFaultTypeId',
                message: '请先选择故障类型'
              },
              {
                name: 'deptId',
                value: 'businessDeptId'
              }
            ],
            searchApi: '/workSheetPeopple/getNoPagePeopleInfoList',
            placeholder: '请选择处理人',
            personInfoProp: {
              describe: 'deptName',
              name: 'name',
              nameFormatt: ['name', 'processCountString'],
              key: 'userId'
            },
            required: true
          }
        ],
        nowFormList = this.formList || [],
        fkFaultTypeIndex = nowFormList.findIndex(
          item => item.prop == 'fkFaultType'
        );
      if (val == this.oldBusinessDeptId) {
        if (fkFaultTypeIndex >= 0) {
          return;
        }

        nowFormList.splice(1, 0, ...dept);
        this.form.fkFaultTypeId = '';
        this.form.fkUser = '';
        this.form.fkUserId = '';
      } else {
        if (fkFaultTypeIndex >= 0) {
          nowFormList.splice(fkFaultTypeIndex, 2);
          this.form.fkFaultTypeId = '';
          this.form.fkUser = '';
          this.form.fkUserId = '';
        }
      }
      this.formList = nowFormList;
    }
  }
};
