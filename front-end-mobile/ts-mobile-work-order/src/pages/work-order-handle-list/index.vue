<template>
  <view class="ts-container">
    <u-navbar title="工单处理" title-bold :custom-back="goBack"></u-navbar>
    <user-evaluate
      :user-head="$store.state.common.userInfo.headImgUrl"
      v-model="personEvaluate"
      :rate-count="evaluateStarCount"
      :evaluate-list="evaluateList"
    ></user-evaluate>
    <view class="search-box">
      <u-search
        shape="square"
        v-model="keywords"
        :show-action="false"
        placeholder="输入工单号\故障关键词搜索"
        @search="search"
        @clear="searchClear"
      ></u-search>
      <view class="screen-icon">
        <u-icon
          name="shaixuan"
          custom-prefix="work-icon"
          size="36"
          @click="toggleScreenInfo"
          :class="{
            'has-search-inform':
              screenForm.workStatusValue ||
              screenForm.faultEmergency ||
              screenForm.repairManDept.length
          }"
        ></u-icon>
      </view>
    </view>

    <view class="work-order-list-box">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <work-order-item
          v-for="i in list"
          :key="i.workNumber"
          :item="i"
          @click="jumpToDetail"
        ></work-order-item>
      </mescroll>
      <view
        class="sort-select-content"
        v-show="showSort"
        @click.self="showSort = false"
      >
        <view class="sort-content">
          <view v-for="(item, index) of sortTypeList" :key="index"></view>
        </view>
      </view>
    </view>
    <screen
      ref="rightScreen"
      :show="isShowScreen"
      :form-list="screenFormList"
      :form-data.sync="screenForm"
      @reset="handleRightScreenReset"
      @change="handleSearchValueChange"
    ></screen>
  </view>
</template>

<script>
import index from './index.js';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import userEvaluate from './components/user-evaluate.vue';
import workOrderItem from './components/work-order-item.vue';
import screen from './components/screen.vue';

export default {
  name: 'my-work-order-list',
  mixins: [index],
  components: {
    mescroll,
    userEvaluate,
    workOrderItem,
    screen
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
  position: relative;
}
.search-box {
  @include vue-flex;
  position: relative;
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: $uni-spacing-row-lg;
    left: $uni-spacing-row-lg;
    height: 1px;
    background-color: #eeeeee;
  }
}
.oa-icon-shangxiayidongjiantou,
.screen-icon .u-icon {
  color: #949494;
}
.has-search-inform {
  color: $u-type-primary !important;
}
.screen-icon {
  margin-left: $uni-spacing-row-base;
}
.work-order-list-box {
  flex: 1;
  position: relative;
}
.oa-icon-shangxiayidongjiantou {
  margin-left: 8px;
  font-size: 36rpx;
}
.sort-select-content {
  position: absolute;
  top: -$uni-spacing-col-base;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
.sort-content {
  background: #fff;
  height: 100px;
}
</style>
