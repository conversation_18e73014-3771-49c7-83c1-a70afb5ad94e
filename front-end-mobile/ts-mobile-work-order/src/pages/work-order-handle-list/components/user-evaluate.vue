<template>
  <view class="user-evaluate-box" v-if="JSON.stringify(value) != '{}'">
    <view class="user-evaluate-main-box">
      <view class="user-img">
        <image
          style="height: 100%; width: 100%;"
          :src="userHead"
          mode="aspectFill"
        ></image>
      </view>
      <view class="user-evaluate-main">
        <u-rate
          class="user-evaluate-rate"
          disabled
          :count="rateCount"
          :size="rateSize"
          v-model="value.avg"
          :inactive-color="evaluateColor"
          :active-color="evaluateColor"
        ></u-rate>
        <view class="user-evaluate-text">
          <text>{{ `综合评分：${value.avg}分  共接单：${value.sum}单` }}</text>
          <view class="evaluate-switch" @click="toggleEvaluateInfo">
            {{ isShowEvaluateInfo | showEvaluateInfoText }}
            <u-icon
              :name="isShowEvaluateInfo | showEvaluateInfoIcon"
              color="#005bac"
              size="24"
            ></u-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="my-evaluate-info" v-if="isShowEvaluateInfo">
      <view
        class="evaluate-item"
        v-for="(item, index) in evaluateList"
        :key="index"
      >
        <text class="evaluate-item-text">{{ item.name }}</text>
        <u-rate
          disabled
          :count="rateCount"
          :size="rateSize"
          v-model="value[item.prop]"
          :inactive-color="evaluateColor"
          :active-color="evaluateColor"
        ></u-rate>
      </view>
    </view>
  </view>
</template>

<script>
import headImg from '../../../assets/img/headImg.png';
export default {
  name: 'user-evaluate',
  props: {
    userHead: {
      type: String,
      default: headImg
    },
    rateSize: {
      type: [Number, String],
      default: 28
    },
    rateCount: {
      type: [Number, String],
      default: 5
    },
    evaluateList: {
      type: Array,
      default() {
        return [];
      }
    },
    evaluateColor: {
      type: String,
      default: '#005bac'
    },
    value: {
      type: Object,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      isShowEvaluateInfo: false
    };
  },
  filters: {
    showEvaluateInfoIcon(val) {
      return val ? 'arrow-up' : 'arrow-down';
    },
    showEvaluateInfoText(val) {
      return val ? '收起' : '展开';
    }
  },
  methods: {
    toggleEvaluateInfo() {
      this.isShowEvaluateInfo = !this.isShowEvaluateInfo;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/flex.scss';
.user-evaluate-main-box,
.user-evaluate-text {
  @include vue-flex;
  align-items: center;
  background-color: #ffffff;
  color: $uni-color-title;
}
.user-evaluate-main-box,
.my-evaluate-info {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  font-size: 24rpx;
  position: relative;
}
.my-evaluate-info ::after {
  content: '';
  position: absolute;
  top: 0;
  right: $uni-spacing-row-lg;
  left: $uni-spacing-row-lg;
  height: 1px;
  background-color: #e2e4e8;
}
.user-evaluate-text {
  justify-content: space-between;
  font-size: 24rpx;
}
.user-img {
  width: 68rpx;
  height: 68rpx;
  margin-right: 20rpx;
}
.user-evaluate-main {
  flex: 1;
  font-size: 28rpx;
  line-height: 28rpx;
}
.evaluate-item-text {
  margin-right: 20rpx;
}
</style>
