<template>
  <view class="work-order-item" @tap.stop="onClick(item.workNumber)">
    <view class="item-top">
      <view class="item-title">
        {{ `[${item.workNumber}] ${item.faultDeion}` }}
      </view>
      <text class="item-status" :class="item.workStatus | statusColor">
        {{ item.workStatusName }}
      </text>
    </view>
    <view class="item-bottom">
      <view class="item-bottom-left">
        <view class="item-warning-tips">
          <text class="item-tag" :class="item.faultEmergency | emergencyLevel">
            {{ item.faultEmergency | emergencyText }}
          </text>
          <text
            class="item-tag"
            :class="item.faultAffectScope | affectScopeLevel"
          >
            {{ item.faultAffectScope | affectScopeText }}
          </text>
          <text
            class="item-tag"
            :class="item.cqDays | expireLevel"
            v-if="
              item.requiredCompletionTime &&
                item.workStatus == 3 &&
                item.cqDays &&
                item.cqDays >= -2
            "
          >
            {{ item.cqDays | expireText }}
          </text>
          <text class="item-tag tag-urge" v-if="item.hatenCount">
            {{ `催办${item.hatenCount}次` }}
          </text>
        </view>
        <view class="item-info">
          <text class="item-child-info">
            {{ `报修人：${item.repairManDeptName}-${item.repairManName}` }}
          </text>
          <text class="item-child-info">
            {{ `要求完成：${item.requiredCompletionTime || ''}` }}
          </text>
        </view>
        <view class="item-info" v-if="item.workStatus == 2">
          <text class="item-child-info">
            {{ `派单时间：${item.sendTime || ''}` }}
          </text>
          <text class="item-child-info">
            {{ `派单人：${item.sendPeopleName || ''}` }}
          </text>
        </view>
        <view class="item-info">
          <text class="item-child-info" v-if="item.workStatus == 4">
            {{ `完成时间：${item.actualCompletionTime}` }}
          </text>
          <text class="item-child-info" v-if="item.workStatus == 4">
            {{ `确认时间：${item.confirmTime}` }}
          </text>
          <text
            class="item-child-info"
            v-if="
              item.workStatus == 3 ||
                item.workStatus == 4 ||
                item.workStatus == 7
            "
          >
            {{ `已登工时：${item.workHours}H` }}
          </text>
          <text class="item-child-info" v-if="item.workStatus == 3">
            {{ `协助人：${item.assistName || ''}` }}
          </text>
        </view>
        <view class="item-info" v-if="item.workStatus == 7">
          {{ `暂停时间：${item.suspendedTime}` }}
        </view>
        <view class="item-info ellipsis-row-1" v-if="item.workStatus == 7">
          {{ `暂停原因：${item.suspendedRemark}` }}
        </view>
        <view class="item-info" v-if="item.workStatus == 8">
          {{ `终止时间：${item.terminationTime}` }}
        </view>
        <view class="item-info ellipsis-row-1" v-if="item.workStatus == 8">
          {{ `终止原因：${item.terminationRemark}` }}
        </view>
        <view class="item-info ellipsis-row-1" v-if="item.workStatus == 3">
          {{ item.fallbackRemark }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'work-order-item',
  props: {
    item: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  filters: {
    emergencyText(val) {
      let text = '';
      switch (val) {
        case 1:
          text = '非常紧急';
          break;
        case 2:
          text = '紧急';
          break;
        case 3:
          text = '常规处理';
          break;
      }
      return text;
    },
    emergencyLevel(val) {
      let level = '';
      switch (val) {
        case 1:
          level = 'most';
          break;
        case 2:
          level = 'more';
          break;
        case 3:
          level = 'normal';
          break;
      }
      return `tag-emergency-${level}`;
    },
    affectScopeText(val) {
      let text = '';
      switch (val) {
        case 1:
          text = '个人事件';
          break;
        case 2:
          text = '科室事件';
          break;
        case 3:
          text = '多科室事件';
          break;
        case 4:
          text = '全院事件';
          break;
      }
      return text;
    },
    affectScopeLevel(val) {
      let level = '';
      switch (val) {
        case 1:
          level = 'individual';
          break;
        case 2:
          level = 'single-dep';
          break;
        case 3:
          level = 'multiple-dep';
          break;
        case 4:
          level = 'whole-hospital';
          break;
      }
      return `tag-affect-${level}`;
    },
    expireLevel(val) {
      let level = '';
      if (val == 0) {
        level = 'today';
      } else if (val < 0) {
        level = 'will';
      } else if (val > 0) {
        level = 'over';
      }
      return `tag-expire-${level}`;
    },
    expireText(val) {
      if (val == 0) {
        return '今日到期';
      } else if (val < 0 && val >= -2) {
        return '即将到期';
      } else if (val > 0) {
        return '已超期';
      }
    },
    statusColor(val) {
      let colorClass = '';
      if (val == 7 || val == 8) {
        colorClass = 'status-warning';
      }
      return colorClass;
    }
  },
  methods: {
    onClick(id) {
      this.$emit('click', id);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
@import '../../../assets/css/flex.scss';
.work-order-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
}
.item-top {
  @include vue-flex;
}
.item-bottom {
  align-items: flex-end;
  @include vue-flex;
}
.item-bottom-left {
  flex: 1;
  min-width: 0;
}
.item-title {
  @include multiLineEllipsis;
  color: $uni-text-color;
  font-weight: bold;
  flex: 1;
}
.item-status {
  color: #8a94a6;
}
.status-warning {
  color: $uni-color-error;
}
.tag-emergency-most {
  background-color: $uni-color-error;
}
.tag-emergency-more {
  background-color: $uni-color-warning;
}
.tag-emergency-normal {
  background-color: $uni-color-normal;
}
.item-tag {
  border-radius: $uni-border-radius-sm;
  margin-right: $uni-spacing-row-xsm;
  color: #ffffff;
  font-size: $uni-font-size-xsm;
  padding: $uni-spacing-col-xsm $uni-spacing-row-base;
  &:last-child {
    margin-right: 0;
  }
}
.tag-affect-individual {
  background-color: #5aaaea;
}
.tag-affect-single-dep {
  background-color: #646cca;
}
.tag-affect-multiple-dep {
  background-color: #be6cf4;
}
.tag-affect-whole-hospital {
  background-color: #48c6a2;
}
.tag-urge {
  background-color: #fc8070;
}
.tag-expire-today {
  background-color: $uni-color-error;
}
.tag-expire-will {
  background-color: #f5780a;
}
.tag-expire-over {
  background-color: #f81a80;
}
.item-info {
  color: $uni-text-content-color;
  font-size: $uni-font-size-sm;
}
.item-child-info:nth-child(n) {
  margin-left: $uni-spacing-row-sm;
}
.item-child-info:first-child {
  margin-left: 0;
}
.item-operation-button {
  background-color: $uni-color-primary;
  font-size: $uni-font-size-sm;
  border-radius: 12px 12px 12px 0;
  width: 128rpx;
  height: 48rpx;
  line-height: 48rpx;
  color: #ffffff;
  text-align: center;
}
.acceptance-button {
  background-color: #2d76eb;
}
.evaluate-button {
  background-color: #f89e18;
}
.ellipsis-row-1 {
  min-width: 0;
  @include ellipsis;
}
</style>
