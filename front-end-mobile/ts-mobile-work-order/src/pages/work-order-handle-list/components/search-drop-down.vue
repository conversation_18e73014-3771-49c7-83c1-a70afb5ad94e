<template>
  <u-dropdown
    ref="searchDropDown"
    class="search-content"
    @close="handleDropDownClose"
  >
    <u-dropdown-item title="状态">
      <div class="select-content">
        <div
          v-for="(item, index) of statusOptions"
          :key="index"
          @click="selectStatus(item)"
          class="select-item"
          :class="
            statuList.indexOf(item.value) >= 0 ? 'active-select-item' : ''
          "
        >
          {{ item.label }}
          <view class="checked-box">
            <u-icon name="checkbox-mark" size="32"></u-icon>
          </view>
        </div>
      </div>
    </u-dropdown-item>
    <u-dropdown-item title="紧急程度" v-model="urgency" :options="urgencyList">
    </u-dropdown-item>
    <u-dropdown-item title="设备设施"> </u-dropdown-item>
    <u-dropdown-item
      :title="searchType ? '我参与的' : '我处理的'"
      v-model="searchType"
      :options="searchTypeOptions"
    >
    </u-dropdown-item>
  </u-dropdown>
</template>

<script>
export default {
  data() {
    return {
      //工单状态筛选数据
      statuList: [2, 3, 7],
      statusOptions: [
        {
          label: '待接单',
          value: 2
        },
        {
          label: '处理中',
          value: 3
        },
        {
          label: '已暂停',
          value: 7
        },
        {
          label: '待验收',
          value: 4
        },
        {
          label: '完结',
          value: '5,6,8'
        }
      ],

      //紧急程度筛选条件
      urgency: '',
      urgencyList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '非常紧急',
          value: 1
        },
        {
          label: '比较急',
          value: 2
        },
        {
          label: '常规处理',
          value: 3
        }
      ],

      //我处理的
      searchType: 0,
      searchTypeOptions: [
        {
          label: '我处理的',
          value: 0
        },
        {
          label: '我参与的',
          value: 1
        }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.searchDropDown.highlight(0);
    });
  },
  methods: {
    getSearchData() {
      return {
        workStatusValue: this.statuList.length ? this.statuList.join(',') : 0,
        faultEmergency: this.urgency,
        searchType: this.searchType
      };
    },
    handleDropDownClose(index) {
      let titleBoxList = this.$refs.searchDropDown.$el.querySelectorAll(
          '.u-dropdown__menu__item__text'
        ),
        iconList = this.$refs.searchDropDown.$el.querySelectorAll(
          '.u-iconfont'
        );
      setTimeout(() => {
        switch (index) {
          case 0:
            titleBoxList[0].style.color = this.statuList.length
              ? 'rgb(41, 121, 255)'
              : 'rgb(96, 98, 102)';
            iconList[0].style.color = this.statuList.length
              ? 'rgb(41, 121, 255)'
              : 'rgb(96, 98, 102)';
            break;
          case 1:
            titleBoxList[1].style.color = this.urgency
              ? 'rgb(41, 121, 255)'
              : 'rgb(96, 98, 102)';
            iconList[1].style.color = this.urgency
              ? 'rgb(41, 121, 255)'
              : 'rgb(96, 98, 102)';
            break;
          case 2:
            // titleBoxList[2].style.color = this.statuList.length
            //   ? 'rgb(41, 121, 255)'
            //   : 'rgb(96, 98, 102)';
            break;
          case 3:
            break;
        }
      });
    },
    selectStatus(item) {
      const index = this.statuList.indexOf(item.value);
      index >= 0
        ? this.statuList.splice(index, 1)
        : this.statuList.push(item.value);
      this.$emit('change', this.getSearchData());
    }
  }
};
</script>

<style lang="scss" scoped>
.search-content {
  background-color: #fff;
}
.select-item {
  width: calc(100% - 16px);
  line-height: 20px;
  font-size: 14px;
  padding: 12px 16px;
  padding-left: 0;
  margin-left: 16px;
  box-sizing: border-box;
  display: flex;
  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }

  .u-icon {
    display: none;
  }
}
.active-select-item {
  color: #005bac;
  .u-icon {
    display: flex;
  }
}
.checked-box {
  display: flex;
  flex: 1;
  justify-content: flex-end;
}
.select-content {
  background-color: #fff;
}
/deep/ {
  .u-cell {
    width: calc(100% - 16px);
    line-height: 20px;
    padding: 12px 16px;
    padding-left: 0;
    margin-left: 16px;
  }
}
</style>
