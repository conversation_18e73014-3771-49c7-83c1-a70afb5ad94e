export default {
  data() {
    return {
      fromPage: '',
      evaluateStarCount: 5,
      personEvaluate: {},
      evaluateList: [
        {
          name: '处理速度',
          prop: 'processSpeed'
        },
        {
          name: '服务态度',
          prop: 'serviceAttituude'
        },
        {
          name: '技术水平',
          prop: 'technicalLevel'
        }
      ],
      keywords: '',
      isShowScreen: false,
      screenFormList: [
        {
          title: '状态',
          type: 'select',
          mode: 'checkbox',
          chooseType: 'checkbox',
          prop: 'workStatusValue',
          optionList: [
            {
              name: '待接单',
              checked: true,
              value: '2'
            },
            {
              name: '处理中',
              checked: true,
              value: '3'
            },
            {
              name: '已暂停',
              checked: true,
              value: '7'
            },
            {
              name: '待验收',
              checked: false,
              value: '4'
            },
            {
              name: '待评价',
              value: '5'
            },
            {
              name: '已完成',
              checked: false,
              value: '6'
            }
          ]
        },
        {
          title: '紧急程度',
          type: 'select',
          mode: 'radio',
          chooseType: 'radio',
          prop: 'faultEmergency',
          optionList: [
            {
              value: '',
              name: '全部'
            },
            {
              value: '1',
              name: '非常紧急'
            },
            {
              name: '紧急',
              value: '2'
            },
            {
              name: '常规处理',
              value: '3'
            }
          ]
        },
        {
          title: '报修科室',
          type: 'select',
          mode: 'dept',
          chooseType: 'radio',
          prop: 'repairManDeptId',
          propVal: 'repairManDept'
        }
      ],
      screenForm: {
        workStatusValue: '2,3,7',
        faultEmergency: '',
        repairManDeptId: '',
        repairManDept: []
      },
      list: [],
      showSort: false,
      sortType: {
        sord: 'desc',
        sidx: 'a.required_completion_time'
      },
      sortTypeList: [
        {
          label: '报修时间',
          value: 'a.create_time'
        },
        {
          label: '要求日期',
          value: 'a.required_completion_time'
        },
        {
          label: '催办次数',
          value: 'haten_count'
        },
        {
          label: '紧急程度',
          value: ''
        },
        {
          label: '状态',
          value: ''
        }
      ]
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.selectMobileInfo();
  },
  methods: {
    selectMobileInfo() {
      this.ajax.selectMobileInfo().then(res => {
        this.personEvaluate = res.object;
      });
    },
    search(val) {
      this.keywords = val;
      this.datasInit();
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    searchClear() {},
    toggleScreenInfo() {
      this.isShowScreen = !this.isShowScreen;
      this.oldSearchData = JSON.stringify(this.screenForm);
      this.$refs.rightScreen.handleopen();
    },
    getListData(page, successCallback, errorCallback) {
      let {
        workStatusValue,
        faultEmergency,
        repairManDept = []
      } = this.screenForm;
      this.ajax
        .getMyWorkOrder({
          pageNo: page.num,
          pageSize: page.size,
          type: 2,
          fuzzy: this.keywords,
          workStatusValue: workStatusValue,
          faultEmergency,
          repairManDeptId: (repairManDept[0] && repairManDept[0].id) || '',
          workStatus: 0,
          ...this.sortType,
          sord: 'desc',
          sidx: 'a.create_time'
        })
        .then(res => {
          successCallback(res.rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    jumpToDetail(id) {
      uni.navigateTo({
        url: `/pages/work-order-detail/index?id=${id}&fromPage=work-order-handle-list`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    handleSearchValueChange() {
      this.isShowScreen = false;
      if (this.oldSearchData == JSON.stringify(this.screenForm)) {
        return;
      }
      this.datasInit();
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    handleRightScreenReset() {
      this.screenFormList[0].optionList = [
        {
          name: '待接单',
          checked: true,
          value: '2'
        },
        {
          name: '处理中',
          checked: true,
          value: '3'
        },
        {
          name: '已暂停',
          checked: true,
          value: '7'
        },
        {
          name: '待验收',
          checked: false,
          value: '4'
        },
        {
          name: '已完成',
          checked: false,
          value: '6'
        }
      ];
      this.screenForm = {
        workStatusValue: '2,3,7',
        faultEmergency: '',
        repairManDeptId: '',
        repairManDept: []
      };
    },
    handleShowSort() {
      this.showSort = !this.showSort;
    }
  }
};
