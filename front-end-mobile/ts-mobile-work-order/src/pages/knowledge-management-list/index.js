export default {
  data() {
    return {
      fromPage: '',
      keywords: '',
      tabList: [
        {
          name: '发布中',
          status: 4,
          list: []
        },
        {
          name: '待审核',
          status: 1,
          list: []
        },
        {
          name: '未通过',
          status: -1,
          list: []
        },
        {
          name: '已移除',
          status: 3,
          list: []
        },
        {
          name: '草稿箱',
          status: 0,
          list: []
        }
      ],
      currentTab: 0
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    if (opt && opt.tabIndex) {
      this.currentTab = opt.tabIndex;
    }
  },
  methods: {
    search(val) {
      this.keywords = val;
      this.$nextTick(() => {
        this.tabList.map((item, index) => {
          this.datasInit(index);
          this.$refs[`mescroll${index}`][0].downCallback();
        });
      });
    },
    clear() {},
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback, index) {
      this.ajax
        .getKnowledgeAllList({
          pageNo: page.num,
          pageSize: page.size,
          knowledgeStatus: this.tabList[index].status,
          knowledgeTitle: this.keywords,
          sord: 'desc',
          sidx: 'a.create_time'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        });
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
    },
    datasInit(index) {
      this.tabList[index]['list'] = [];
    },
    onClick(id) {
      uni.navigateTo({
        url: `/pages/knowledge-detail/index?id=${id}&fromPage=knowledge-management-list`
      });
    },
    jumpToWorkOrderReporting() {
      uni.navigateTo({
        url: '/pages/add-work-order-knowledge/index'
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    jumpToWorkOrderReporting() {
      uni.navigateTo({
        url: '/pages/add-work-order-knowledge/index?fromPage=knowledge-list'
      });
    }
  }
};
