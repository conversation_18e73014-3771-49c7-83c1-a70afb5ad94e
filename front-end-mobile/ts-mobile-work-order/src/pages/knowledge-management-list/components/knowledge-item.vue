<template>
  <view class="knowledge-item" @click="jumpToDetail()">
    <view class="knowledge-item-title-box">
      <view class="knowledge-item-title">
        {{ data.knowledgeTitle }}
      </view>
      <view class="knowledge-item-time" v-if="status == 0 || status == 1">
        {{ `${data.createTime.substring(0, 16)}` }}
      </view>
      <view
        class="knowledge-item-time"
        v-if="status == -1 || status == 3 || status == 4"
      >
        {{ `${data.reviewTime.substring(0, 16)}` }}
      </view>
    </view>

    <view class="knowledge-item-info-box">
      <view>
        {{ `类型：${data.fkKnowledgeTypeName}` }}
      </view>
      <view class="knowledge-item-info">
        <view
          class="info-item"
          v-if="status == 0 || status == 1 || status == 4"
        >
          {{ `工时：${data.recommendedWorkHours}H` }}
        </view>
        <view class="info-item" style="flex: 1">
          {{ `贡献人：${data.fkUserName}` }}
        </view>
        <view class="info-item" v-if="status == 3 || status == 4">
          {{ `赞 ${data.usefulNumbers}` }}
        </view>
      </view>
      <view class="u-type-error" v-if="status == -1">
        {{ `不通过原因：${data.backReason}` }}
      </view>
      <view v-if="status == 0 && data.remark">
        {{ `撤回原因：${data.remark}` }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'knowledge-item',
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    },
    status: {
      type: [Number, String],
      default: ''
    }
  },
  methods: {
    jumpToDetail() {
      this.$emit('click', this.data.pkKnowledgeBaseId);
    }
  }
};
</script>

<style scoped lang="scss">
@import '../../../assets/css/ellipsis.scss';
@import '../../../assets/css/flex.scss';
.knowledge-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
}
.knowledge-item-title-box {
  @include vue-flex;
  align-items: center;
}
.knowledge-item-title {
  flex: 1;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  @include multiLineEllipsis;
  font-weight: bold;
}
.knowledge-item-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
}
.knowledge-item-info-box {
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
}
.knowledge-item-icon-box {
  @include vue-flex;
  align-items: center;
}
.icon-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color-grey;
  margin-left: $uni-spacing-row-xsm;
}
.knowledge-item-info {
  @include vue-flex;
  align-items: flex-end;
}
.info-item {
  margin-right: 30rpx;
  &:last-child {
    margin: 0;
  }
}
</style>
