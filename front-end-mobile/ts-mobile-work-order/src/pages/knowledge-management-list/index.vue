<template>
  <view class="ts-container">
    <u-navbar title="知识管理" title-bold :custom-back="goBack"></u-navbar>
    <view class="search-box">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入故障关键词搜索"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="right-box">
        <u-icon
          name="plus"
          size="36"
          @click="jumpToWorkOrderReporting"
        ></u-icon>
      </view>
    </view>
    <view class="knowledge-management-box">
      <base-tabs-swiper
        ref="tabSwiper"
        class="tab-swiper-box"
        :list="tabList"
        :current="currentTab"
        :is-scroll="false"
        @change="changeTab"
      ></base-tabs-swiper>
      <swiper
        class="swiper-box"
        :current="currentTab"
        :duration="300"
        @change="onTabChange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in tabList"
          :key="index"
        >
          <mescroll
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <knowledge-item
              v-for="i in item.list"
              :key="i.pkKnowledgeBaseId"
              :data="i"
              :status="item.status"
              @click="onClick"
            ></knowledge-item>
          </mescroll>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import baseTabsSwiper from '../../components/base-tabs-swiper/base-tabs-swiper.vue';
import mescroll from '../../components/mescroll-swiper/mescroll.vue';
import knowledgeItem from './components/knowledge-item.vue';
export default {
  name: 'knowledge-management-list',
  mixins: [index],
  components: {
    baseTabsSwiper,
    mescroll,
    knowledgeItem
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.search-box {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.right-box {
  color: $uni-color-primary;
  margin-left: $uni-spacing-row-lg;
}
.knowledge-management-box {
  @include vue-flex(column);
  flex: 1;
}
.tab-swiper-box {
  margin-bottom: $uni-spacing-col-base;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
}
</style>
