export default {
  data() {
    return {
      formList: [
        {
          title: '本次处理工时',
          prop: 'workHours',
          type: 'number',
          maxlength: 5,
          placeholder: '请输入本次处理工时',
          callback: value => {
            return value.toString().match(/^\d*(\.?\d{0,1})/g)[0];
          }
        },
        {
          title: '本次处理说明',
          prop: 'remark',
          type: 'textarea',
          maxlength: 100,
          placeholder: '请输入本次处理说明',
          required: true
        }
      ],
      form: {
        fkUserId: '',
        pkWsTaskId: '',
        remark: '',
        workHours: '',
        yesOrNo: '1'
      },
      rules: {
        remark: [
          {
            required: true,
            message: '请输入本次处理说明',
            trigger: ''
          }
        ]
      },
      workHours: 0
    };
  },
  onLoad(opt) {
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkUserId = opt.handlerId;
    this.getWorkOrderDetail(opt.id);
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    getWorkOrderDetail(id) {
      this.ajax.getWorkOrderDetail(id).then(res => {
        this.workHours = res.object.wsWsSheetInfoOutVo.workHours;
      });
    },
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.workSheetUpdateProgress(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/work-order-handle-list/index'
        });
      });
    }
  }
};
