export default {
  data() {
    return {
      title: '',
      connectIncomingCall: 0,
      answeringRate: '0%',
      incomingSolve: 0,
      solveRate: '0%',
      uselessIncomingCall: 0,
      missedCalls: 0,
      missedDetialList: [],
      callOut: 0,
      todayNewOrder: 0,
      waitingOrder: 0,
      handingOrder: 0,
      unCreateOrder: 0,
      chartData: {},
      chartOption: {
        dataLabel: false,
        padding: [8, 8, 8, 8],
        legend: {
          float: 'right'
        },
        extra: {
          ring: {
            ringWidth: 20,
            border: false,
            center: ['40%', '50%']
          }
        },
        tooltip: {
          showBox: false
        },
        title: {
          name: ''
        },
        subtitle: {
          name: ''
        }
      }
    };
  },
  computed: {
    missedDetial: function() {
      return Math.ceil(this.missedDetialList.length / 2);
    }
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage || 'index';
    this.pkWorkReportId = opt.pkWorkReportId;
  },
  created() {
    this.getWorkOrderDaily();
  },
  methods: {
    getWorkOrderDaily() {
      this.ajax.getOrderDaily(this.pkWorkReportId).then(res => {
        if (res.success == false || !res.success) {
          this.title = deptName + this.$dayjs().format('MM月D日') + '运维日报';
          return;
        }
        let {
          deptName = '',
          answerPhone,
          answerPhoneRate,
          calls,
          invalidTelephone,
          newWorkOrderToday,
          noWorkOrder,
          numberOfMissedCalls,
          phoneSolve,
          phoneSolveRate,
          processing,
          snet,
          numberOfMissedCallsList,
          levelOneFaultTypeDatas = []
        } = res.object || {};

        this.title = deptName + this.$dayjs().format('MM月D日') + '运维日报';
        this.connectIncomingCall = answerPhone || 0;
        this.answeringRate = answerPhoneRate || 0;
        this.incomingSolve = phoneSolve || 0;
        this.solveRate = phoneSolveRate || 0;
        this.uselessIncomingCall = invalidTelephone || 0;
        this.missedCalls = numberOfMissedCalls || 0;
        this.missedDetialList = numberOfMissedCallsList || [];
        this.callOut = calls || 0;
        this.todayNewOrder = newWorkOrderToday || 0;
        this.waitingOrder = snet || 0;
        this.handingOrder = processing || 0;
        this.unCreateOrder = noWorkOrder || 0;

        let newData = [];
        levelOneFaultTypeDatas.forEach(item => {
          newData.push({
            name: item.categoryName,
            value: item.total
          });
        });

        this.chartData = {
          series: [
            {
              data: newData
            }
          ]
        };
      });
    }
  }
};
