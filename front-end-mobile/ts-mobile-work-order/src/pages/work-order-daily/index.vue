<template>
  <view class="ts-container">
    <u-navbar class="title-box" :title="title" :is-back="false"></u-navbar>
    <view class="content">
      <view class="content-title">通话情况</view>
      <view class="report-item-box">
        <view class="icon-img-box">
          <img
            :src="
              require('@/assets/img/work-order-daily/usefull_calling_phone.png')
            "
          />
        </view>
        <view class="inform-content">
          <view>
            接通来电
            <view class="inform-count">
              {{ connectIncomingCall }}
            </view>
            个，电话接听率
            <view class="inform-count"> {{ answeringRate }}%</view>
          </view>
          <view>
            接通解决
            <view class="inform-count">
              {{ incomingSolve }}
            </view>
            个，电话解决率
            <view class="inform-count"> {{ solveRate }}%</view>
          </view>
          <view>
            无效来电
            <view class="inform-count">
              {{ uselessIncomingCall }}
            </view>
            个
          </view>
        </view>
      </view>
      <view class="report-item-box">
        <view class="icon-img-box">
          <view style="position: relative;">
            <img
              :src="
                require('@/assets/img/work-order-daily/unusefull_calling_phone.png')
              "
            />
          </view>
        </view>
        <view class="inform-content">
          <view>
            未接来电
            <view class="inform-count">{{ missedCalls }}</view>
            个
          </view>
          <view v-for="index of missedDetial" :key="index">
            {{ missedDetialList[index - 1].fkUserDeptName }}
            {{ missedDetialList[index - 1].fkUserDeptName ? '-' : '' }}
            {{ missedDetialList[index - 1].fkUserName }}
            {{ missedDetialList[index - 1].count }}个
            {{
              (missedDetialList[index] || {}).fkUserDeptName
                ? '，' + missedDetialList[index].fkUserDeptName + '-'
                : ''
            }}
            {{ (missedDetialList[index] || {}).fkUserName }}
            {{
              (missedDetialList[index] || {}).count
                ? missedDetialList[index].count + '个'
                : ''
            }}
          </view>
        </view>
      </view>
      <view class="report-item-box">
        <view class="icon-img-box">
          <img
            :src="require('@/assets/img/work-order-daily/calling_out.png')"
          />
        </view>
        <view class="inform-content">
          <view>
            呼出
            <view class="inform-count">{{ callOut }}</view>
            个
          </view>
        </view>
      </view>
      <view class="content-title">工单情况</view>
      <view class="report-item-box">
        <view class="icon-img-box">
          <img :src="require('@/assets/img/work-order-daily/desck_book.png')" />
        </view>
        <view class="inform-content">
          <view>
            今日新增工单
            <view class="inform-count">
              {{ todayNewOrder }}
            </view>
            个
          </view>
          <view>
            当前待派单
            <view class="inform-count">
              {{ waitingOrder }}
            </view>
            个，处理中
            <view class="inform-count"> {{ handingOrder }}</view
            >个
          </view>
          <view>
            未建单
            <view class="inform-count">
              {{ unCreateOrder }}
            </view>
            个
          </view>
        </view>
      </view>
      <view
        class="report-item-box circle-content"
        v-if="chartData.series && chartData.series[0].data.length"
      >
        <qiun-data-charts
          type="ring"
          :chartData="chartData"
          background="none"
          :opts="chartOption"
        />
      </view>
    </view>
  </view>
</template>

<script>
import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import index from './index';

export default {
  name: 'work-order-daily',
  components: { qiunDataCharts },
  mixins: [index]
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
/deep/.title-box .u-navbar-content-title {
  display: flex;
  align-items: center;
  position: absolute;
  width: 100% !important;
  left: 0 !important;
}
/deep/.title-box .u-back-wrap {
  position: relative;
  z-index: 9;
}
.ts-container {
  height: 100%;
  overflow: scroll;
  // @include vue-flex(column);
}
.content {
  padding: 8px 16px;
}
.content-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: 22px;
}
.report-item-box {
  @include vue-flex;
  padding: 16px 0;
  min-height: 78px;
  background-color: #fff;
  margin: 8px 0;
  border-radius: 4px;
  .icon-img-box {
    @include vue-flex;
    align-items: center;
    padding: 0 16px;
    flex-shrink: 0;
    img {
      height: 40px;
      width: 40px;
    }
    /deep/.error-icon {
      position: absolute;
      color: #ff6565;
      right: 0;
      .u-iconfont {
        font-weight: bold !important;
      }
    }
  }
}
.inform-content {
  font-size: 14px;
  line-height: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  > uni-view {
    @include vue-flex;
    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
  .inform-count {
    color: #ff6565;
    font-size: 18px;
    font-weight: 600;
    line-height: 20px;
    padding: 0 4px;
  }
}
.circle-content {
  height: 176px;
  padding: 0 !important;
}

@media screen and (max-width: 750px) {
  .content {
    padding: 16rpx 32rpx;
  }
  .content-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    line-height: 44rpx;
  }
  .report-item-box {
    padding: 32rpx 0;
    min-height: 156rpx;
    margin-top: 16rpx;
    border-radius: 8rpx;
    .icon-img-box {
      padding: 0 32rpx;
      img {
        height: 80rpx;
        width: 80rpx;
      }
    }
  }
  .inform-content {
    font-size: 28rpx;
    line-height: 40rpx;
    > uni-view:not(:last-child) {
      margin-bottom: 8rpx;
    }
    .inform-count {
      font-size: 36rpx;
      line-height: 40rpx;
      padding: 0 8rpx;
    }
  }
  .circle-content {
    height: 352rpx;
  }
}
</style>
