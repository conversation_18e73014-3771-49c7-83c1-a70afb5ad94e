export default {
  data() {
    return {
      formList: [
        {
          title: '撤回原因',
          prop: 'remark',
          type: 'textarea',
          maxlength: 300,
          placeholder: '请输入撤回原因',
          required: true
        }
      ],
      form: {
        knowledgeStatus: 4,
        pkKnowledgeBaseId: '',
        remark: ''
      },
      rules: {
        remark: [
          {
            required: true,
            message: '请输入撤回原因',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.form.pkKnowledgeBaseId = opt.id;
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.withdrawKnowledge(this.form).then(res => {
        uni.redirectTo({
          url: '/pages/knowledge-management-list/index'
        });
      });
    }
  }
};
