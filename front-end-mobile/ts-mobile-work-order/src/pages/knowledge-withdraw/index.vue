<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar title="知识点撤回" title-bold></u-navbar>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
      @submit="submit"
    ></base-form>
    <view class="button-box" @click="onClick">
      提交
    </view>
  </view>
</template>

<script>
import index from './index.js';
import baseForm from '../../components/base-form/base-form.vue';
export default {
  name: 'work-order-inspect',
  mixins: [index],
  components: {
    baseForm
  }
};
</script>

<style lang="scss" scoped>
.button-box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  font-size: $uni-font-size-lg;
  background-color: #ffffff;
  text-align: center;
  height: 44px;
  font-weight: bold;
}
</style>
