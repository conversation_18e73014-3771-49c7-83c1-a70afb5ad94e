<template>
  <view class="knowledge-item" @click="jumpToDetail()">
    <view class="knowledge-item-title-box">
      <view class="knowledge-item-title">
        {{ data.knowledgeTitle }}
      </view>
      <view class="knowledge-item-time">
        {{ `${data.reviewTime.substring(0, 16)}` }}
      </view>
    </view>
    <view class="knowledge-item-info">
      {{ `类型：${data.fkKnowledgeTypeName}` }}
    </view>
    <view class="knowledge-item-info-box">
      <view class="knowledge-item-info">
        {{ `工时：${data.recommendedWorkHours}H` }}
      </view>
      <view class="knowledge-item-info" style="flex: 1">
        {{ `贡献人：${data.fkUserName}` }}
      </view>
      <view class="knowledge-item-info">
        {{ `赞 ${data.usefulNumbers}` }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'knowledge-item',
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  methods: {
    jumpToDetail() {
      this.$emit('click', this.data.pkKnowledgeBaseId);
    }
  }
};
</script>

<style scoped lang="scss">
@import '../../../assets/css/ellipsis.scss';
@import '../../../assets/css/flex.scss';
.knowledge-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
}
.knowledge-item-title-box {
  @include vue-flex;
  align-items: center;
}
.knowledge-item-title {
  flex: 1;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  @include multiLineEllipsis;
  font-weight: bold;
}
.knowledge-item-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
}
.knowledge-item-info-box {
  @include vue-flex;
  align-items: flex-end;
}
.knowledge-item-info {
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  margin-right: 30rpx;
  &:last-child {
    margin: 0;
  }
}
.knowledge-item-icon-box {
  @include vue-flex;
}
.icon-text {
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  margin-left: $uni-spacing-row-xsm;
}
</style>
