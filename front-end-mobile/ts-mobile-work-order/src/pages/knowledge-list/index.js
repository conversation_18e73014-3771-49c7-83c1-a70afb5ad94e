export default {
  data() {
    return {
      fromPage: '',
      showContent: false,
      keywords: '',
      tabslist: [
        {
          code: '',
          name: '全部',
          id: ''
        }
      ],
      currentTab: 0,
      knowledgeList: [],
      hotOfKnowledgeList: [],
      commonOfKnowledgeList: [],
      tabScroll: 0,
      ceiling: false
    };
  },
  async onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.getOneLevelOfKnowledgeTypeList();
    this.hotOfKnowledgeList = await this.getKnowledgeLikeRank({
      limit: 1
    });
    this.commonOfKnowledgeList = await this.getKnowledgeLikeRank({
      limit: 1,
      fkUserDeptId: this.$store.state.common.userInfo.empDeptId
    });
    this.showContent = true;
    this.$nextTick(function() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('#tabsBox')
        .boundingClientRect(data => {
          this.tabScroll = data.top;
        })
        .exec();
    });
  },
  methods: {
    //滚动距离获取
    scroll(e) {
      if (this.tabScroll - 44 <= e.scrollTop) {
        this.ceiling = true;
      } else {
        this.ceiling = false;
      }
    },
    async getKnowledgeLikeRank(params) {
      let list = [];
      await this.ajax.getKnowledgeLikeRank(params).then(res => {
        list = res.object;
      });
      return list;
    },
    search(val) {
      this.keywords = val;
      this.datasInit();
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    clear() {},
    getOneLevelOfKnowledgeTypeList() {
      this.ajax.getOneLevelOfKnowledgeTypeList().then(res => {
        this.tabslist = [...this.tabslist, ...res.object];
      });
    },
    changeTab(index) {
      this.currentTab = index;
      this.datasInit();
      this.$refs.mescroll.downCallback();
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getKnowledgeAllList({
          pageNo: page.num,
          pageSize: page.size,
          knowledgeStatus: 4,
          fkKnowledgeTypeId: this.tabslist[this.currentTab].id,
          knowledgeTitle: this.keywords,
          sord: 'desc',
          sidx: 'a.create_time'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount);
        });
    },
    setListData(rows, totalCount) {
      this.knowledgeList = this.knowledgeList.concat(rows);
    },
    datasInit() {
      this.knowledgeList = [];
    },
    onClick(id) {
      uni.navigateTo({
        url: `/pages/knowledge-detail/index?id=${id}&fromPage=knowledge-list`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    jumpToWorkOrderReporting() {
      uni.navigateTo({
        url: '/pages/add-work-order-knowledge/index?fromPage=knowledge-list'
      });
    }
  }
};
