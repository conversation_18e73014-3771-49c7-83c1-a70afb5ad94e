<template>
  <view class="ts-container">
    <u-navbar title="知识查阅" title-bold :custom-back="goBack"></u-navbar>
    <view class="mescroll-box" v-if="showContent">
      <mescroll
        ref="mescroll"
        :on-scroll="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
        @scroll="scroll"
      >
        <view class="search-box">
          <u-search
            v-model="keywords"
            :show-action="false"
            placeholder="输入故障关键词搜索"
            @search="search"
            @clear="clear"
          ></u-search>
          <view class="right-box">
            <u-icon
              name="plus"
              size="36"
              @click="jumpToWorkOrderReporting"
            ></u-icon>
          </view>
        </view>
        <view
          class="hot-knowledge-list-box"
          v-if="hotOfKnowledgeList.length > 0"
        >
          <view class="box-title">
            <u-icon
              name="remen"
              size="36"
              custom-prefix="work-icon"
              color="#fa3534"
            ></u-icon>
            <text class="box-title-text">近期热门</text>
          </view>
          <view class="knowledge-list-box">
            <knowledge-item
              v-for="item in hotOfKnowledgeList"
              :key="item.pkKnowledgeBaseId"
              :data="item"
              @click="onClick"
            ></knowledge-item>
          </view>
        </view>
        <view
          class="hot-knowledge-list-box"
          v-if="commonOfKnowledgeList.length > 0"
        >
          <view class="box-title">
            <u-icon
              name="changjian"
              size="36"
              custom-prefix="work-icon"
              color="#ff9900"
            ></u-icon>
            <text class="box-title-text">科室常见</text>
          </view>
          <view class="knowledge-list-box">
            <knowledge-item
              v-for="item in commonOfKnowledgeList"
              :key="item.pkKnowledgeBaseId"
              :data="item"
              @click="onClick"
            ></knowledge-item>
          </view>
        </view>
        <u-tabs
          id="tabsBox"
          class="tabs-box"
          :class="{ 'tabs-box-ceiling': ceiling }"
          :list="tabslist"
          :is-scroll="true"
          :showCount="false"
          :current="currentTab"
          @change="changeTab"
        ></u-tabs>
        <view class="knowledge-list-box">
          <knowledge-item
            v-for="item in knowledgeList"
            :key="item.pkKnowledgeBaseId"
            :data="item"
            @click="onClick"
          ></knowledge-item>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import mescroll from '../../components/mescroll-swiper/mescroll.vue';
import knowledgeItem from './components/knowledge-item.vue';
export default {
  name: 'knowledge-management-list',
  mixins: [index],
  components: {
    mescroll,
    knowledgeItem
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  min-height: 100%;
  @include vue-flex(column);
}
.mescroll-box {
  flex: 1;
  position: relative;
}
.search-box {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.right-box {
  color: $uni-color-primary;
  margin-left: $uni-spacing-row-lg;
}
.tabs-box {
  margin-bottom: $uni-spacing-col-base;
}
.knowledge-list-box {
  @include vue-flex(column);
  flex: 1;
}
.hot-knowledge-list-box {
  background-color: #ffffff;
  margin-bottom: $uni-spacing-col-base;
}
.box-title {
  font-size: $uni-font-size-lg;
  line-height: 1;
  font-weight: bold;
  color: $uni-text-color;
  padding: $uni-spacing-col-lg $uni-spacing-row-lg 0;
}
.box-title-text {
  margin-left: $uni-spacing-row-sm;
}
.tabs-box-ceiling {
  position: sticky;
  top: 0;
  z-index: 99;
}
</style>
