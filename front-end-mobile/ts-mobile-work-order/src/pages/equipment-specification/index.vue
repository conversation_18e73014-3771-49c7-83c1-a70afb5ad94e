<template>
  <view class="ts-container">
    <u-navbar
      :title="equipmentInfo.equipmentName"
      title-bold
      :custom-back="goBack"
    ></u-navbar>
    <view class="equipment-info-box"></view>
    <view
      class="equipment-maintenance-record-box"
      v-if="equipmentMaintenanceRecord.length"
    >
      <record-item
        class="record-item"
        v-for="(item, key) in equipmentMaintenanceRecord"
        :key="key"
        :content="item"
      >
      </record-item>
    </view>
    <view class="report-btn" @click="handleGotoReportOrder">
      我要报修
    </view>
  </view>
</template>
<script>
import index from './index.js';
export default {
  name: 'knowledge-detail',
  mixins: [index]
};
</script>
<style lang="scss" scoped>
@import '../../assets/css/ellipsis.scss';
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100vh;
  overflow: scroll;
  background-color: #fff;
  @include vue-flex;
  flex-direction: column;
}
.equipment-maintenance-record-box {
  padding: 8px 15px;
  flex: 1;
  overflow: scroll;
}
.record-item:not(:last-child) {
  padding-bottom: 24px;
}
.report-btn {
  @include vue-flex;
  justify-content: center;
  box-shadow: 0 1px 6px #ccc;
  height: 88rpx;
  font-size: 28rpx;
  line-height: 88rpx;
  font-weight: bold;
  color: $u-type-primary;
}
</style>
