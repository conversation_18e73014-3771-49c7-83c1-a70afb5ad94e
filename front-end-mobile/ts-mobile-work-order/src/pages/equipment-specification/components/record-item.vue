<template>
  <view class="record-item">
    <view class="item-content">
      <text class="item-log-node">{{ `【设备保修】` }}</text>
      <text class="item-log-operation">{{
        `操作人：${content.repairManName} ${content.requiredCompletionTime}`
      }}</text>
    </view>
    <view>{{ content.faultDeion }}</view>
    <view>
      {{ `工单状态：${content.workStatusValue}` }}
    </view>
    <view class="item-log-remark">
      {{ content.fkUserName ? '处理人：' : '' }}
      {{ content.fkUserName + '&emsp;' }}
      {{ content.workHours ? '登记工时：' + content.workHours + '&emsp;' : '' }}
      {{ content.actualCompletionTime ? '确认时间：' : '' }}
      {{ content.actualCompletionTime }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'record-item',
  props: {
    content: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  filters: {
    fomateText(text) {
      return text ? text.replace(/\#cut@/g, '，') : '';
    }
  }
};
</script>

<style lang="scss" scoped>
.record-item {
  padding-left: 15px;
  border-left: 1px solid #eee;
  font-size: 12px;
  color: #666666;
}
.item-content {
  position: relative;
  &::before {
    content: '';
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #005bac;
    position: absolute;
    left: -22px;
    top: 50%;
    transform: translateY(-50%);
  }
  .item-log-node {
    font-size: 14px;
    font-weight: bold;
    color: #666666;
  }
}
.record-item-node {
  font-size: $uni-font-size-base;
  font-weight: bold;
  color: $uni-text-content-color;
}
.record-item-operation,
.record-item-remark {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
.record-item-remark {
  margin-bottom: $uni-spacing-col-lg;
}
</style>
