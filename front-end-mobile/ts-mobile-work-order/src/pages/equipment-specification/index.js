import recordItem from './components/record-item.vue';
export default {
  components: {
    recordItem
  },
  data() {
    return {
      equipmentInfo: {},
      equipmentMaintenanceRecord: [],
      pkFaultEquipmentId: ''
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.pkFaultEquipmentId = opt.pkFaultEquipmentId;
    this.getEquipmentInfo(opt.pkFaultEquipmentId);
  },
  filters: {
    showRecord(val) {
      return val.length > 0;
    }
  },
  methods: {
    getEquipmentInfo(id) {
      this.ajax.getEquipmentInfo(id).then(res => {
        this.equipmentInfo = res.object.faultEquipmentOutVo;
        this.equipmentMaintenanceRecord =
          res.object.wsWsSheetInfoByFaultEquipmentOutVos;
      });
    },
    handleGotoReportOrder() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: `/ts-mobile-work-order/pages/work-order-reporting/index?equipmentName=${this.equipmentInfo.equipmentName}&fkFaultEquipmentId=${this.pkFaultEquipmentId}`
      });
    },
    goBack() {
      if (this.fromPage) {
        uni.redirectTo({
          url: `/pages/${this.fromPage}/index`
        });
      } else {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/workbench'
        });
      }
    }
  }
};
