export default {
  data() {
    return {
      list: [
        {
          show: false,
          formList: [
            {
              title: '协助人',
              prop: 'fkUser',
              propVal: 'fkUserIds',
              type: 'select',
              mode: 'person',
              chooseType: 'checkbox',
              getListType: 'scollSearch',
              searchParams: [
                {
                  name: 'faultTypeId',
                  value: 'fkFaultTypeId',
                  message: '表单错误'
                }
              ],
              searchApi: '/workSheetPeopple/getPeopleInfoList',
              placeholder: '请选择协助人',
              required: false
            },
            {
              title: '需协助内容',
              prop: 'remark',
              type: 'textarea',
              placeholder: '请输入需协助内容',
              maxlength: 100,
              required: true
            }
          ],
          form: {
            workNumber: '',
            remark: '',
            fkUserIds: '',
            fkFaultTypeId: ''
          },
          rules: {
            fkUser: [
              {
                required: true,
                message: '请选择协助人',
                trigger: ''
              }
            ],
            remark: [
              {
                required: true,
                message: '请备注说明',
                trigger: ''
              }
            ]
          }
        }
      ],
      options: [
        {
          text: '删除',
          style: {
            backgroundColor: '#dd524d'
          }
        }
      ],
      form: {
        workNumber: '',
        remarks: '',
        fkUserIds: '',
        fkFaultTypeId: ''
      },
      validateVal: false
    };
  },
  onLoad(opt) {
    this.form.workNumber = opt.id;
    this.form.fkFaultTypeId = opt.fkFaultTypeId;
    this.list.map(item => {
      item.form.workNumber = opt.id;
      item.form.fkFaultTypeId = opt.fkFaultTypeId;
    });
  },
  methods: {
    addAssist() {
      this.list.push({
        show: false,
        formList: [
          {
            title: '协助人',
            prop: 'fkUser',
            propVal: 'fkUserIds',
            type: 'select',
            mode: 'person',
            chooseType: 'checkbox',
            getListType: 'scollSearch',
            searchParams: [
              {
                name: 'faultTypeId',
                value: 'fkFaultTypeId',
                message: '表单错误'
              }
            ],
            searchApi: '/workSheetPeopple/getPeopleInfoList',
            placeholder: '请选择协助人',
            required: false
          },
          {
            title: '需协助内容',
            prop: 'remark',
            type: 'textarea',
            placeholder: '请输入需协助内容',
            required: true
          }
        ],
        form: {
          workNumber: this.form.workNumber,
          remark: '',
          fkUserIds: '',
          fkFaultTypeId: this.form.fkFaultTypeId
        },
        rules: {
          fkUser: [
            {
              required: true,
              message: '请选择协助人',
              trigger: ''
            }
          ],
          remark: [
            {
              required: true,
              message: '请备注说明',
              trigger: ''
            }
          ]
        }
      });
    },
    swipeClick(index) {
      this.list.splice(index, 1);
    },
    swipeOpen(index) {
      this.list[index].show = true;
      this.list.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    swipeClose(index) {
      this.list[index].show = false;
    },
    onClick() {
      let userArr = [];
      let remarkArr = [];
      for (var i = 0; i < this.list.length; i++) {
        let v = this.$refs[`baseForm${i}`][0].validate();
        if (v) {
          let ids = this.list[i].form.fkUserIds.split(',');
          ids.map(item => {
            userArr.push(item);
            remarkArr.push(this.list[i].form.remark);
          });
        } else {
          return false;
        }
      }
      this.$set(this.form, 'fkUserIds', userArr.join(','));
      this.$set(this.form, 'remarks', remarkArr.join(','));
      this.submit();
    },
    submit() {
      this.ajax.workSheetAssist(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/work-order-handle-list/index'
        });
      });
    }
  }
};
