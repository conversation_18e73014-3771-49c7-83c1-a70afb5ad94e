<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar title="申请协助" title-bold></u-navbar>
    <view class="assist-swiper-box">
      <u-swipe-action
        :show="item.show"
        :index="index"
        v-for="(item, index) in list"
        :key="item.id"
        @click="swipeClick"
        @open="swipeOpen"
        @close="swipeClose"
        :options="options"
      >
        <view class="item u-border-bottom">
          <base-form
            :ref="`baseForm${index}`"
            :formList="item.formList"
            :formData.sync="item.form"
            :rules="item.rules"
            :showSubmitButton="false"
          ></base-form>
        </view>
      </u-swipe-action>
      <view class="add-assist-box">
        <u-icon name="plus" size="24" @click="addAssist"></u-icon>
        <text @click="addAssist">添加协助人</text>
      </view>
    </view>
    <view class="button-box" @click="onClick">
      提交
    </view>
  </view>
</template>

<script>
import index from './index.js';
import baseForm from '../../components/base-form/base-form.vue';
export default {
  name: 'work-order-end',
  mixins: [index],
  components: {
    baseForm
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  overflow: hidden;
  @include vue-flex(column);
}
.assist-swiper-box {
  flex: 1;
  overflow: auto;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  background-color: #ffffff;
  height: 44px;
  font-size: $uni-font-size-lg;
  text-align: center;
  font-weight: bold;
}
.add-assist-box {
  text-align: center;
  font-size: $uni-font-size-base;
  color: $uni-color-primary;
  margin: $uni-spacing-col-base 0;
}
</style>
