<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar
      title="工单详情"
      title-bold
      title-width="500"
      :is-back="this.showDtail"
    ></u-navbar>
    <view
      class="work-order-tips"
      v-if="
        isShowTips(
          baseInfo.hatencount,
          baseInfo.fkUserId,
          baseInfo.assistId,
          this.$store.state.common.userInfo.empId
        )
      "
    >
      {{ `该工单已催办${baseInfo.hatencount}次，请尽快处理！` }}
    </view>
    <view class="work-order-content">
      <base-tabs-swiper
        ref="tabSwiper"
        class="tab-swiper-box"
        :list="list"
        :current="currentTab"
        :is-scroll="false"
        @change="changeTab"
      ></base-tabs-swiper>
      <swiper
        class="swiper-box"
        :current="currentTab"
        :duration="300"
        @change="onTabChange"
      >
        <swiper-item class="swiper-item">
          <view class="info-box">
            <view
              class="info-item"
              v-for="(item, index) in basicInfo"
              :key="index"
            >
              <view class="info-item-title">
                {{ item.title }}
              </view>
              <view
                class="info-item-row"
                v-for="(row, i) in item.list"
                :key="i"
              >
                <text class="row-label">{{ row.label }}</text>
                <text
                  class="row-text"
                  :class="row | rowClassFilter(baseInfo[row.propVal])"
                  @click="row.callback ? row.callback() : ''"
                  >{{
                    row.formatte
                      ? row.formatte(baseInfo, row)
                      : baseInfo[row.prop]
                  }}</text
                >
              </view>
            </view>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="resource-box">
            <view class="audio-box">
              <view class="file-title">报修录音</view>
              <view class="audio-list" v-if="audioList.length > 0">
                <zaudio
                  theme="theme4"
                  themeColor="#999"
                  v-for="(item, index) in audioList"
                  :key="index"
                ></zaudio>
              </view>
              <view class="resource-tip">
                暂无资源
              </view>
            </view>
            <view class="file-box">
              <view class="file-title">报修附件</view>
              <view class="">
                <resource-image
                  v-if="imgList.length > 0"
                  :list="imgList"
                ></resource-image>
                <resource-file
                  v-if="fileList.length > 0"
                  :list="fileList"
                  :titleSize="28"
                >
                  <template #right="{item}">
                    <view class="file-operation-button">
                      <text
                        class="file-operation-button-item"
                        @click="downloadFile(item)"
                      >
                        下载
                      </text>
                      <text
                        v-if="
                          item.fkFileName.toLowerCase().indexOf('.mhtml') == -1
                        "
                        class="file-operation-button-item"
                        @click="previewFile(item)"
                      >
                        预览
                      </text>
                    </view>
                  </template>
                </resource-file>
                <view
                  class="resource-tip"
                  v-if="imgList.length == 0 && fileList.length == 0"
                >
                  暂无资源
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item flex-column">
          <view class="all-money-content">填报经费共计 {{ allMoney }}元</view>

          <view class="mescroll-content">
            <mescroll
              :ref="`mescroll`"
              @getDatas="getWorkOrderCostDetailList"
              @setDatas="setWorkOrderCostDetailList"
              @datasInit="initCostList"
            >
              <cost-list-item
                v-for="(item, index) of costList"
                :key="index"
                :costData="item"
                @click.native="handleShowCostItemDetail(item)"
              ></cost-list-item>
            </mescroll>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="log-box">
            <base-log-list>
              <base-log-list-item v-for="(item, index) in logList" :key="index">
                <log-item-content :content="item"></log-item-content>
              </base-log-list-item>
            </base-log-list>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="work-order-operation-box">
      <view
        class="operation-button-item"
        v-for="(item, index) in showButtons"
        :key="index"
        :class="item.colorClass"
        @click="clickButton(item)"
      >
        {{ item.name }}
      </view>
    </view>
    <u-modal
      ref="uModal"
      v-model="modalShow"
      :show-cancel-button="true"
      :title="modalTitle"
      :async-close="modalAsyncClose"
      :confirm-text="modalConfirmText"
      :confirm-color="modalConfirmColor"
      @confirm="modalConfirm"
    >
      <rich-text :nodes="modalContent"></rich-text>
    </u-modal>
  </view>
</template>

<script>
import index from './index.js';
import zaudio from '@/components/uniapp-zaudio/zaudio';
import resourceImage from './components/resource-image.vue';
import resourceFile from './components/resource-file.vue';
import logItemContent from './components/log-item-content.vue';
import costListItem from './components/cost-list-item.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  name: 'my-work-order-detail',
  mixins: [index],
  components: {
    zaudio,
    resourceImage,
    resourceFile,
    logItemContent,
    costListItem,
    mescroll
  },
  data() {
    return {
      basicInfo: [
        {
          title: '',
          list: [
            {
              label: '工单编号',
              prop: 'workNumber'
            },
            {
              label: '处理科室',
              prop: 'businessDeptName'
            },
            {
              label: '业务类型',
              prop: 'fkFaultType'
            },
            {
              label: '故障设备',
              prop: 'faultEquipmentName'
            },
            {
              label: '设备描述',
              prop: 'equipmentRemark'
            },
            {
              label: '故障描述',
              prop: 'faultDeion',
              typeClass: 'text-warning'
            },
            {
              label: '紧急程度',
              prop: 'faultEmergencyValue',
              propVal: 'faultEmergency',
              typeClass: 'text-warning',
              typeClassRule: '1,2'
            },
            {
              label: '影响范围',
              prop: 'faultAffectScopeValue',
              propVal: 'faultAffectScope',
              typeClass: 'text-warning',
              typeClassRule: '2,3,4'
            },
            {
              label: '报修方式',
              prop: 'repairTypeValue'
            },
            {
              label: '要求完成',
              prop: 'requiredCompletionTime',
              typeClass: 'text-warning'
            }
          ]
        },
        {
          title: '报修信息',
          list: [
            {
              label: '报修人员',
              prop: 'repairManName'
            },
            {
              label: '报修科室',
              prop: 'repairManDeptName'
            },
            {
              label: '联系电话',
              prop: 'repairPhone',
              callback: () => {
                uni.makePhoneCall({
                  phoneNumber: this.baseInfo.repairPhone
                });
              }
            },
            {
              label: '报修地址',
              prop: 'repairDeptAddress',
              formatte: function(data, row) {
                return [data.hospitalDistrictName, data.repairDeptAddress]
                  .filter(item => item)
                  .join('-');
              }
            },
            {
              label: '建单人员',
              prop: 'createByName'
            },
            {
              label: '建单时间',
              prop: 'createTime'
            }
          ]
        },
        {
          title: '处理信息',
          list: [
            {
              label: '派单人',
              prop: 'dispatcher'
            },
            {
              label: '派单时间',
              prop: 'dispatchTime'
            },
            {
              label: '接单时间',
              prop: 'revTime'
            },
            {
              label: '处理人员',
              prop: 'fkUserName'
            },
            {
              label: '协助人员',
              prop: 'assist'
            },
            {
              label: '处理用时',
              prop: 'workHours'
            },
            {
              label: '完成时间',
              prop: 'actualCompletionTime'
            },
            {
              label: '确认时间',
              prop: 'confirmTime'
            }
          ]
        }
      ]
    };
  },
  computed: {
    list: function() {
      let fileCount = this.fileList.length + this.imgList.length;
      return [
        {
          name: '基本信息'
        },
        {
          name: '资源文件' + (fileCount > 0 ? `(${fileCount})` : '')
        },
        {
          name: '费用明细'
        },
        {
          name: '操作日志'
        }
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.work-order-tips {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
  margin-bottom: $uni-spacing-col-base;
  background-color: #ffffff;
  color: $uni-color-error;
}
.work-order-content {
  flex: 1;
  @include vue-flex(column);
}
.swiper-box {
  flex: 1;
  margin: $uni-spacing-col-base 0;
  margin-bottom: 0;
}
.info-box,
.log-box,
.resource-box {
  overflow: auto;
  height: 100%;
}
.log-box,
.resource-box {
  background-color: #ffffff;
}
.info-item-row {
  background-color: #ffffff;
  padding: $uni-spacing-col-lg $uni-spacing-row-lg;
  position: relative;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  @include vue-flex;
  &::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 30rpx;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
}
.row-text {
  flex: 1;
  text-align: right;
}
.text-warning {
  color: $uni-color-error;
}
.info-item:last-child {
  margin-bottom: 16rpx;
}
.info-item-title {
  padding: $uni-spacing-col-xsm $uni-spacing-row-lg;
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  font-weight: bold;
}
.audio-box,
.file-box {
  padding: 0 $uni-spacing-row-lg;
}
.file-title {
  margin: $uni-spacing-col-lg 0;
  color: $uni-text-color;
  font-size: $uni-font-size-lg;
  font-weight: bold;
  border-left: 4px solid $uni-color-primary;
  line-height: 1;
  padding-left: $uni-spacing-row-base;
}
.log-box {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.file-operation-button {
  font-size: $uni-font-size-base;
  color: $uni-color-primary;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
.work-order-operation-box {
  @include vue-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 -1px 6px #ccc;
  width: 100%;
  position: relative;
  z-index: 9;
}
.operation-button-item {
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  // padding: 0 $uni-spacing-row-base;
  height: 44px;
  line-height: 44px;
  flex: 1;
}
.error-color {
  color: $uni-color-error;
}
.normal-color {
  color: $uni-color-primary;
}
.warning-color {
  color: $uni-color-warning;
}

.flex-column {
  display: flex;
  flex-direction: column;
}
.mescroll-content {
  position: relative;
  flex: 1;
}
.all-money-content {
  background-color: #fff;
  padding: $u-interval-primary;
  margin-bottom: $u-interval-primary;
  font-size: 32rpx;
  color: #ec7b25;
}
</style>
