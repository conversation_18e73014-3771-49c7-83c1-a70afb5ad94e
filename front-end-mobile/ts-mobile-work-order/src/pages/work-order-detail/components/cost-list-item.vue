<template>
  <view class="cost-item">
    <view class="top-content">
      <view>{{ costData.costTime }}</view>
      <view>{{ costData.money | computedMoney }}元</view>
    </view>
    <view class="cost-desion">
      <u-icon v-if="costData.fileCount > 0" name="attach"></u-icon>
      {{ costData.costDeion }}
    </view>
    <view>填报人：{{ costData.fillUser }}</view>
    <view>填报时间：{{ costData.createTime }}</view>
  </view>
</template>

<script>
export default {
  name: 'cost-list-item',
  props: {
    costData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  },
  filters: {
    computedMoney: function(money) {
      let labelStr = money.toLocaleString();
      if (labelStr.split('.').length > 1) {
        labelStr.split('.')[1].length == 1 ? (labelStr += '0') : null;
      }
      labelStr.split('.').length == 1 ? (labelStr += '.00') : null;
      return labelStr;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/flex.scss';

.cost-item {
  padding: 0 16px;
  font-size: 24rpx;
  line-height: 36rpx;
  background-color: #fff;
  margin-bottom: $u-interval-primary;
  color: $u-tips-color;
  padding-bottom: 12px;
}
.top-content {
  @include vue-flex;
  align-items: center;
  justify-content: space-between;
  line-height: 68rpx;
  border-bottom: 1px solid $u-bg-color;
  color: $u-main-color;

  > view:last-child {
    font-size: 28rpx;
    color: $u-type-error;
  }
}
.cost-desion {
  font-size: 28rpx;
  line-height: 44rpx;
  margin-top: $u-interval-primary;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: $u-main-color;
  .u-icon {
    margin-right: 4px;
  }
}
</style>
