<template>
  <view class="log-item-content">
    <view class="item-content">
      <text class="item-log-node">{{ `【${content.taskNameVaule}】` }}</text>
      <text class="item-log-operation">{{
        `操作人：${content.createByName} ${content.createTime}`
      }}</text>
    </view>
    <view class="item-log-remark">
      {{ content.takeRemark | fomateText }}
    </view>
    <view>
      <resource-image
        v-if="content.picFiles.length > 0"
        :list="content.picFiles"
      ></resource-image>

      <resource-file
        v-if="content.textFiles.length > 0"
        :list="content.textFiles"
        :titleSize="28"
      >
        <template #right="{item}">
          <view class="file-operation-button">
            <text
              class="file-operation-button-item"
              @click="downloadFile(item)"
            >
              下载
            </text>
            <text
              v-if="item.fkFileName.toLowerCase().indexOf('.mhtml') == -1"
              class="file-operation-button-item"
              @click="previewFile(item)"
            >
              预览
            </text>
          </view>
        </template>
      </resource-file>
    </view>
  </view>
</template>

<script>
import resourceImage from './resource-image.vue';
import resourceFile from './resource-file.vue';
import Base64 from '@/assets/js/base64.min.js';

export default {
  name: 'log-item-content',
  components: {
    resourceImage,
    resourceFile
  },
  props: {
    content: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  watch: {
    content: {
      handler: function(item) {
        let textFiles = [],
          picFiles = [];
        item.files &&
          item.files.forEach(file => {
            if (/gif|jpg|jpeg|png|bmp/i.test(file.fkFileName.toLowerCase())) {
              picFiles.push({
                ...file,
                url: this.$store.state.common.baseHost + file.fileUrl
              });
            } else {
              textFiles.push(file);
            }
          });
        item.textFiles = textFiles;
        item.picFiles = picFiles;
      },
      immediate: true
    }
  },
  filters: {
    fomateText(text) {
      return text ? text.replace(/\#cut@/g, '，') : '';
    }
  },
  methods: {
    downloadFile(e) {
      //#ifdef H5
      // let el = document.createElement('a');
      // el.href = e.fileUrl;
      // el.target = '_blank';
      // el.download =
      //   uni.getSystemInfoSync().platform == 'ios'
      //     ? encodeURI(e.fkFileName)
      //     : e.fkFileName;
      // document.body.appendChild(el);
      // el.click();
      // el.remove();
      let xhr = new XMLHttpRequest();
      xhr.open('get', location.origin + e.fileUrl);
      xhr.responseType = 'blob';
      xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
          // 数据在 this.response 保存
          // excel 的 MIME 格式为 application/vnd.ms-excel
          var blob = new Blob([this.response], {
            type: 'application/vnd.ms-excel'
          });
          // 创建a链接 href链接地址 download为下载下来后文件的名称
          var a = document.createElement('a');
          a.href = URL.createObjectURL(blob);
          a.download = e.fkFileName;
          a.style.display = 'none'; //隐藏a标签 直接调用a标签的点击事件
          document.body.appendChild(a);
          a.click();
          a.remove();
        }
      };
      xhr.send();
      //#endif
      // #ifndef H5
      uni.downloadFile({
        url: e.fileUrl,
        success: res => {
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePaths,
              success: function(data) {
                this.$u.toast('下载完成');
              }
            });
          }
        }
      });
      // #endif
    },
    //文件预览
    previewFile(e) {
      let filePath = `${this.$documentPreviewHost}${e.fileUrl}?fullfilename=${
        e.fkFileId
      }.${e.fkFileName.split('.')[1]}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.item-log-node {
  font-size: $uni-font-size-base;
  font-weight: bold;
  color: $uni-text-content-color;
}
.item-log-operation,
.item-log-remark {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
.item-log-remark {
  margin-bottom: $uni-spacing-col-lg;
}
.file-operation-button {
  font-size: $uni-font-size-base;
  color: $uni-color-primary;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
</style>
