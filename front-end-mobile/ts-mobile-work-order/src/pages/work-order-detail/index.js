import Base64 from '../../assets/js/base64.min.js';
export default {
  data() {
    return {
      fromPage: '',
      currentTab: 0,
      baseInfo: {},
      logList: [],
      audioAction: {
        method: 'pause'
      },
      audioList: [],
      imgList: [],
      fileList: [],
      imgType: ['png', 'jpg', 'jpeg', 'gif', 'image'],
      operationButtons: {
        suspend: {
          name: '暂停',
          page: 'work-order-suspend',
          isShow: false,
          colorClass: 'warning-color'
        },
        end: {
          name: '终止',
          page: 'work-order-end',
          isShow: false,
          colorClass: 'error-color'
        },
        dispatch: {
          name: '派单',
          page: 'work-order-dispatch',
          isShow: false,
          colorClass: 'normal-color'
        },
        reDispatch: {
          name: '重派',
          page: 'work-order-dispatch',
          isShow: false,
          colorClass: 'normal-color'
        },
        claim: {
          name: '认领',
          page: 'work-order-claim',
          isShow: false,
          colorClass: 'normal-color'
        },
        edit: {
          name: '编辑',
          page: 'work-order-reporting',
          isShow: false,
          colorClass: 'normal-color'
        },
        respond: {
          name: '响应',
          page: 'work-order-respond',
          isShow: false,
          colorClass: 'normal-color'
        },
        urge: {
          name: '催办',
          type: 'workOrderModalUrge',
          isShow: false,
          colorClass: 'warning-color'
        },
        handle: {
          name: '处理',
          page: 'work-order-handle',
          isShow: false,
          colorClass: 'normal-color'
        },
        costRegister: {
          name: '费用登记',
          isShow: false,
          type: 'handleShowCostItemDetail',
          colorClass: 'normal-color'
        },
        callbackOrder: {
          name: '追回工单',
          isShow: false,
          type: 'handleOpenCallBackModal',
          colorClass: 'normal-color'
        },
        assist: {
          name: '协助处理',
          page: 'work-order-assist',
          isShow: false,
          colorClass: 'normal-color'
        },
        apply: {
          name: '申请协助',
          page: 'work-order-apply',
          isShow: false,
          colorClass: 'normal-color'
        },
        relay: {
          name: '转发',
          page: 'work-order-relay',
          isShow: false,
          colorClass: 'normal-color'
        },
        inspect: {
          name: '验收',
          page: 'work-order-inspect',
          isShow: false,
          colorClass: 'normal-color'
        },
        evaluate: {
          name: '评价',
          page: 'work-order-evaluate',
          isShow: false,
          colorClass: 'normal-color'
        },
        preserve: {
          name: '存入知识库',
          type: 'workOrderPreserve',
          isShow: false,
          colorClass: 'normal-color'
        },
        open: {
          name: '开启',
          type: 'workOrderModalOpen',
          isShow: false,
          colorClass: 'normal-color'
        },
        reback: {
          name: '撤回',
          type: 'workOrderModelReback',
          isShow: false,
          colorClass: 'normal-color'
        }
      },
      showButtons: [],
      modalTitle: '提示',
      modalShow: false,
      modalShowTitle: true,
      modalAsyncClose: true,
      modalConfirmText: '',
      modalConfirmColor: '#005bac',
      modalContent: '',
      modalConfirmFun: '',
      workNumber: '',
      costList: [], //费用明细列表
      allMoney: '0.00' //总登记费用
    };
  },
  filters: {
    rowClassFilter(row, val) {
      let classes = [];
      if (
        (row.typeClassRule && row.typeClassRule.indexOf(val) !== -1) ||
        (!row.typeClassRule && row.typeClass)
      ) {
        classes.push(row.typeClass);
      }
      if (row.callback) {
        classes.push('u-type-primary');
      }
      return classes.join(' ');
    }
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.showDtail = opt.showDtail ? false : true;
    this.workNumber = opt.id;
    await this.getWorkOrderDetail(opt.id);

    let res = await this.ajax.getWorkSheetUserInfo();
    this.$store.state.common.userInfo.workSheetAdmin =
      res.object.webSocket || false;
  },
  onShow() {
    this.$nextTick(() => {
      this.$refs.mescroll.downCallback();
    });
  },
  methods: {
    isShowTips(urgeCount, fkUserId, assistId, empId) {
      let isHandler = fkUserId === empId;
      let assistArr = assistId ? assistId.split(',') : [];
      let isAssister = assistArr.some(item => {
        return empId === item;
      });
      if (urgeCount && (isHandler || isAssister)) {
        return true;
      } else {
        return false;
      }
    },
    async getWorkOrderDetail(id) {
      await this.ajax.getWorkOrderDetail(id).then(res => {
        this.setOperationButtons(res.object.wsWsSheetInfoOutVo);
        this.baseInfo = res.object.wsWsSheetInfoOutVo;
        this.classifyFile(res.object.wsFileOutVoList);
        this.logList = res.object.wsTaskInfoOutVoList;
        this.showButtons = [];
        this.$nextTick(() => {
          Object.keys(this.operationButtons).forEach(item => {
            if (this.operationButtons[item]['isShow']) {
              this.showButtons.push(this.operationButtons[item]);
            }
          });
        });
      });
    },
    getWorkOrderCostDetailList(page, successCallback, errorCallback) {
      this.ajax
        .getWorkOrderCostDetailList({
          pageSize: 9999,
          pageNo: 1,
          workNumber: this.workNumber,
          sidx: 'create_time',
          sord: 'desc'
        })
        .then(res => {
          let datas = res.rows || res.object;
          let allMoney = 0;
          res.rows.forEach((item, index) => {
            allMoney += item.money;
          });
          let moneyStr = allMoney.toLocaleString();
          if (moneyStr.split('.').length > 1) {
            moneyStr.split('.')[1].length == 2 ? null : (moneyStr += '0');
          }
          moneyStr.split('.').length == 1 ? (moneyStr += '.00') : null;

          this.allMoney = moneyStr;
          successCallback(datas, res.totalCount);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setWorkOrderCostDetailList(rows, totalCount) {
      this.costList = this.costList.concat(rows);
    },
    initCostList() {
      this.costList = [];
    },
    handleShowCostItemDetail(item = '') {
      uni.setStorageSync('cost_detail', JSON.stringify(item));
      uni.setStorageSync('work_order_detail', JSON.stringify(this.baseInfo));
      uni.navigateTo({
        url: `/pages/work-order-register-cost/index?id=${this.workNumber}&formPage=work-order-detail`
      });
    },
    setOperationButtons(info) {
      if (!this.showDtail || this.fromPage == 'index') {
        let workStatus = Number(info.workStatus);
        switch (workStatus) {
          case 2: //待接单
            this.$store.state.common.userInfo.empId == info.fkUserId
              ? this.$set(this.operationButtons['respond'], 'isShow', true)
              : null;
            break;
          case 3: //处理中
            if (this.$store.state.common.userInfo.empId == info.fkUserId) {
              this.fromPage = 'work-order-handle-list';
              this.$set(this.operationButtons['suspend'], 'isShow', true);
              this.$set(this.operationButtons['handle'], 'isShow', true);
              this.$set(this.operationButtons['apply'], 'isShow', true);
              this.$set(this.operationButtons['relay'], 'isShow', true);
              if (
                (info.assistId || '').indexOf(
                  this.$store.state.common.userInfo.empId
                ) >= 0
              ) {
                this.$set(
                  this.operationButtons['costRegister'],
                  'isShow',
                  true
                );
              }
            } else if (
              (info.assistId || '').indexOf(
                this.$store.state.common.userInfo.empId
              ) >= 0
            ) {
              this.$set(this.operationButtons['assist'], 'isShow', true);
              this.$set(this.operationButtons['costRegister'], 'isShow', true);
            } else if (
              this.$store.state.common.userInfo.empId == info.repairManId
            ) {
              this.fromPage = 'my-work-order-list';
              this.$set(this.operationButtons['suspend'], 'isShow', true);
              this.$set(this.operationButtons['urge'], 'isShow', true);
              this.$set(this.operationButtons['end'], 'isShow', true);
            }
            break;
          case 4: //待验收
            if (this.$store.state.common.userInfo.empId == info.repairManId) {
              this.$set(this.operationButtons['inspect'], 'isShow', true);
            }
            if (
              (info.assistId || '').indexOf(
                this.$store.state.common.userInfo.empId
              ) >= 0 ||
              this.$store.state.common.userInfo.empId == info.fkUserId
            ) {
              this.$set(this.operationButtons['costRegister'], 'isShow', true);
            }
            break;
          case 7: //暂停
            if (
              this.$store.state.common.userInfo.empId == info.repairManId ||
              this.$store.state.common.userInfo.empId == info.fkUserId
            ) {
              this.fromPage =
                this.$store.state.common.userInfo.empId == info.repairManId
                  ? 'my-work-order-list'
                  : 'work-order-handle-list';
              this.$set(this.operationButtons['open'], 'isShow', true);
            }

            if (
              (info.assistId || '').indexOf(
                this.$store.state.common.userInfo.empId
              ) >= 0 ||
              this.$store.state.common.userInfo.empId == info.fkUserId
            ) {
              this.$set(this.operationButtons['costRegister'], 'isShow', true);
            }
            break;
          default:
            if (
              ['5,6,8'].indexOf(info.workStatus) >= 0 &&
              ((info.assistId || '').indexOf(
                this.$store.state.common.userInfo.empId
              ) >= 0 ||
                this.$store.state.common.userInfo.empId == info.fkUserId)
            ) {
              this.$set(this.operationButtons['costRegister'], 'isShow', true);
            }
            break;
        }
        return;
      }

      let assistArr = info.assistId ? info.assistId.split(',') : [];
      let isAssister = assistArr.some(item => {
        return this.$store.state.common.userInfo.empId === item;
      });

      //同一个人提单、处理工单的场景需将我的工单和工单处理操作分开
      if (info.workStatus == 1) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId &&
          this.fromPage == 'my-work-order-list'
        ) {
          this.$set(this.operationButtons['edit'], 'isShow', true);
          this.$set(this.operationButtons['end'], 'isShow', true);
        } else if (this.fromPage == 'work-order-reception') {
          if (this.$store.state.common.userInfo.workSheetAdmin) {
            this.$set(this.operationButtons['dispatch'], 'isShow', true);
            this.$set(this.operationButtons['edit'], 'isShow', true);
            this.$set(this.operationButtons['end'], 'isShow', true);
          }

          this.$set(this.operationButtons['claim'], 'isShow', true);
        }
      } else if (info.workStatus == 2) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId &&
          this.fromPage == 'my-work-order-list'
        ) {
          this.$set(this.operationButtons['end'], 'isShow', true);
        }
        if (
          this.$store.state.common.userInfo.empId == info.fkUserId &&
          this.fromPage == 'work-order-handle-list'
        ) {
          this.$set(this.operationButtons['respond'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-reception' &&
          this.$store.state.common.userInfo.workSheetAdmin
        ) {
          this.$set(this.operationButtons['reDispatch'], 'isShow', true);
          this.$set(this.operationButtons['edit'], 'isShow', true);
          this.$set(this.operationButtons['end'], 'isShow', true);
          this.$set(this.operationButtons['urge'], 'isShow', true);
        }
      } else if (info.workStatus == 3) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId &&
          this.fromPage == 'my-work-order-list'
        ) {
          this.$set(this.operationButtons['suspend'], 'isShow', true);
          this.$set(this.operationButtons['urge'], 'isShow', true);
          this.$set(this.operationButtons['end'], 'isShow', true);
        }
        if (
          this.$store.state.common.userInfo.empId == info.fkUserId &&
          this.fromPage == 'work-order-handle-list'
        ) {
          this.$set(this.operationButtons['suspend'], 'isShow', true);
          this.$set(this.operationButtons['handle'], 'isShow', true);
          this.$set(this.operationButtons['apply'], 'isShow', true);
          this.$set(this.operationButtons['relay'], 'isShow', true);
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-reception' &&
          this.$store.state.common.userInfo.workSheetAdmin
        ) {
          this.$set(this.operationButtons['reDispatch'], 'isShow', true);
          this.$set(this.operationButtons['urge'], 'isShow', true);
          this.$set(this.operationButtons['suspend'], 'isShow', true);
          this.$set(this.operationButtons['end'], 'isShow', true);
        }

        if (isAssister && this.fromPage == 'work-order-handle-list') {
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
          this.$set(this.operationButtons['assist'], 'isShow', true);
        }
      } else if (info.workStatus == 4) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId &&
          this.fromPage == 'my-work-order-list'
        ) {
          this.$set(this.operationButtons['inspect'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-reception' &&
          this.$store.state.common.userInfo.workSheetAdmin
        ) {
          this.$set(this.operationButtons['preserve'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-handle-list' &&
          (isAssister ||
            this.$store.state.common.userInfo.empId == info.fkUserId)
        ) {
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
          this.$set(this.operationButtons['callbackOrder'], 'isShow', true);
        }
      } else if (info.workStatus == 5) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId &&
          this.fromPage == 'my-work-order-list'
        ) {
          this.$set(this.operationButtons['evaluate'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-reception' &&
          this.$store.state.common.userInfo.workSheetAdmin
        ) {
          this.$set(this.operationButtons['preserve'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-handle-list' &&
          (isAssister ||
            this.$store.state.common.userInfo.empId == info.fkUserId)
        ) {
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
        }
      } else if (info.workStatus == 6) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId ||
          this.$store.state.common.userInfo.empId == info.fkUserId ||
          (this.fromPage == 'work-order-reception' &&
            this.$store.state.common.userInfo.workSheetAdmin)
        ) {
          this.$set(this.operationButtons['preserve'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-handle-list' &&
          (isAssister ||
            this.$store.state.common.userInfo.empId == info.fkUserId)
        ) {
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
        }
        this.$set(this.operationButtons['edit'], 'isShow', true);
      } else if (info.workStatus == 7) {
        if (
          this.$store.state.common.userInfo.empId == info.repairManId ||
          this.$store.state.common.userInfo.empId == info.fkUserId ||
          (this.fromPage == 'work-order-reception' &&
            this.$store.state.common.userInfo.workSheetAdmin)
        ) {
          this.$set(this.operationButtons['open'], 'isShow', true);
        }

        if (
          this.fromPage == 'work-order-handle-list' &&
          (isAssister ||
            this.$store.state.common.userInfo.empId == info.fkUserId)
        ) {
          this.$set(this.operationButtons['costRegister'], 'isShow', true);
        }
      } else if (info.workStatus == 8) {
        this.$set(this.operationButtons.reback, 'isShow', true);
      }
    },
    //文件分类
    classifyFile(list) {
      list.map(item => {
        if (item.fileSuffix == 'wav') {
          item.src = `${this.$store.state.common.baseHost}${item.fileUrl}`;
          this.audioList.push(item);
        } else {
          let list = item.fkFileName.split('.');
          item.fileSuffix = list[list.length - 1];
          let isImg = this.imgType.some(ext => {
            // 转为小写
            return ext.toLowerCase() === list[list.length - 1];
          });
          if (isImg) {
            if (item.fileUrl.indexOf('ts-basics-bottom') >= 0) {
              item.url = `${this.$store.state.common.baseHost}${item.fileUrl}`;
            } else {
              item.url =
                this.$store.state.common.baseHost +
                '/ts-document/attachment/downloadFile/' +
                item.fkFileId;
            }
            this.imgList.push(item);
          } else {
            this.fileList.push(item);
          }
        }
      });
      if (this.audioList.length > 0) {
        // 设置音频数据
        this.$zaudio.setAudio(this.audioList);
        //渲染第一首音频
        this.$zaudio.setRender(0);
      }
    },
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    //文件下载
    downloadFile(e) {
      let fileUrl = e.fileUrl;
      if (e.fileUrl.indexOf('ts-basics-bottom') == -1) {
        fileUrl =
          this.$store.state.common.baseHost +
          '/ts-document/attachment/downloadFile/' +
          e.fkFileId;
      }
      // #ifdef H5
      let ua = navigator.userAgent.toLowerCase();
      if (ua.indexOf('micromessenger') >= 0 && ua.match(/ipad|iphone|ipod/)) {
        uni.showModal({
          title: '提示',
          content: '请在浏览器中打开该页面进行下载',
          showCancel: false
        });
      } else {
        let el = document.createElement('a');
        el.href = fileUrl;
        el.target = '_blank';
        el.download = e.fkFileName;
        document.body.appendChild(el);
        el.click();
        el.remove();
      }
      // #endif
      // #ifndef H5
      uni.downloadFile({
        url: e.src,
        success: res => {
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePaths,
              success: function(data) {
                this.$u.toast('下载完成');
              }
            });
          }
        }
      });
      // #endif
    },
    //文件预览
    previewFile(e) {
      let filePath = `${this.$documentPreviewHost}${e.fileUrl}?fullfilename=${e.fkFileId}.${e.fileSuffix}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
      });
    },
    //按钮点击
    async clickButton(item) {
      if (item.page) {
        this.jumpPage(item.page);
      } else {
        await this[item.type]();
      }
    },
    jumpPage(page) {
      uni.navigateTo({
        url: `/pages/${page}/index?id=${this.baseInfo.workNumber}&pkWsTaskId=${this.baseInfo.pkWsTaskId}&fkFaultTypeId=${this.baseInfo.fkFaultTypeId}&fkUserId=${this.baseInfo.fkUserId}&fromPage=${this.fromPage}&workStatus=${this.baseInfo.workStatus}&businessDeptId=${this.baseInfo.businessDeptId}`
      });
    },
    async workOrderModalUrge() {
      let data = await this.ajax.getHastenInfo(this.baseInfo.workNumber);
      let htmlStr = '';
      htmlStr += '<div style="margin: 10px 15px; font-size: 14px;">';
      if (data.object) {
        htmlStr += `本工单共催办<span style="color: #fa3534">${data.object.counts}</span>次,<br>
					上次催办时间为：${data.object.createTime} `;
      } else {
        htmlStr += '本工单共催办<span style="color: #fa3534">0</span>次';
      }
      htmlStr += '</div>';
      this.modalContent = htmlStr;
      this.modalConfirmText = '催办';
      this.modalShow = true;
      this.modalConfirmFun = this.workOrderUrge;
    },
    modalConfirm() {
      this.modalConfirmFun();
    },
    workOrderUrge() {
      this.ajax
        .workSheetHasten({
          taskName: '处理中',
          workNumber: this.baseInfo.workNumber
        })
        .then(res => {
          if (res.object == 1) {
            this.modalShow = false;
            this.baseInfo.hatencount += 1;
            uni.showToast({
              icon: 'none',
              title: '催办成功'
            });
          }
        })
        .catch(err => {
          this.modalShow = false;
          uni.showToast({
            icon: 'none',
            title: '催办失败'
          });
        });
    },
    async workOrderModalOpen() {
      let data = await this.ajax.openNodeInfo(this.baseInfo.pkWsTaskId);
      let htmlStr = '';
      htmlStr += '<div style="margin: 10px 15px; font-size: 14px;">';
      htmlStr += `${data.object.createTime} ${data.object.createByName}已暂停工单<br>
							暂停原因：${data.object.takeRemark} `;
      htmlStr += '</div>';
      this.modalContent = htmlStr;
      this.modalConfirmText = '开启';
      this.modalShow = true;
      this.modalConfirmFun = this.workOrderOpen;
    },
    async workOrderOpen() {
      await this.ajax
        .workSheetHadRecovered({
          fkUserId: this.baseInfo.fkUserId,
          pkWsTaskId: this.baseInfo.pkWsTaskId
        })
        .then(async res => {
          this.modalShow = false;
          uni.showToast({
            icon: 'none',
            title: '开启成功'
          });
          let path = `/pages/${this.fromPage}/index`;
          if (this.fromPage == 'my-work-order-list') {
            path = `${path}?tabIndex=1`;
          }
          uni.reLaunch({
            url: path
          });
        })
        .catch(err => {
          this.modalShow = false;
          uni.showToast({
            icon: 'none',
            title: '开启失败'
          });
        });
    },
    workOrderModelReback() {
      let htmlStr = '';
      htmlStr += '<div style="margin: 10px 15px; font-size: 14px;">';
      htmlStr += '确定要撤回该工单吗？';
      htmlStr += '</div>';
      this.modalContent = htmlStr;
      this.modalConfirmText = '撤回';
      this.modalShow = true;
      this.modalConfirmFun = this.workOrderReback;
    },
    /**@desc 撤回工单 */
    workOrderReback() {
      this.ajax
        .handleRebcackOrder({
          workNumber: this.baseInfo.workNumber,
          pkWsTaskId: this.baseInfo.pkWsTaskId
        })
        .then(res => {
          this.modalShow = false;
          uni.showToast({
            icon: 'none',
            title: '撤回成功'
          });
          this.$set(this.operationButtons.reback, 'isShow', false);
          this.getWorkOrderDetail(this.workNumber);
        })
        .catch(err => {
          this.modalShow = false;
          uni.showToast({
            icon: 'none',
            title: '撤回失败'
          });
        });
    },
    claimModalOpen() {
      let html = `
      <div style="margin: 10px 15px; font-size: 14px; text-align: center;">确定认领该工单吗？</div>
      `;
      this.modalContent = html;
      this.modalConfirmText = '确定';
      this.modalShow = true;
      this.modalConfirmFun = this.handleClaimOrder;
    },
    handleClaimOrder() {
      this.ajax
        .claimWorkOrder({
          // businessDeptId: this.$store.state.common
          fkUserId: this.$store.state.common.userInfo.empId,
          pkWsTaskId: this.baseInfo.pkWsTaskId
        })
        .then(res => {
          uni.navigateBack();
        })
        .catch(error => {
          this.modalShow = false;
          this.$u.toast('认领失败');
        });
    },
    workOrderPreserve() {
      uni.navigateTo({
        url: `/pages/add-work-order-knowledge/index?workNumber=${this.baseInfo.workNumber}`
      });
    },
    handleOpenCallBackModal() {
      let html = `
      <div style="margin: 10px 15px; font-size: 14px; text-align: center;">确定要追回到处理中状态吗？</div>
      `;
      this.modalContent = html;
      this.modalConfirmText = '确定';
      this.modalShow = true;
      this.modalConfirmFun = this.handleCallBackWorkOrder;
    },
    /**@desc 追回工单 */
    handleCallBackWorkOrder() {
      this.ajax
        .handleCallbackOrder({
          pkWsTaskId: this.baseInfo.pkWsTaskId,
          fkUserId: this.$store.state.common.userInfo.empId
        })
        .then(() => {
          uni.navigateBack();
        })
        .catch(error => {
          this.modalShow = false;
          this.$u.toast('追回失败');
        });
    }
  }
};
