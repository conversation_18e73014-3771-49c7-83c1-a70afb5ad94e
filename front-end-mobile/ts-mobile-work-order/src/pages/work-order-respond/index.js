export default {
  data() {
    return {
      type: 'workSheetRespondAccept',
      fkUserId: '',
      pkWsTaskId: '',
      fkFormerUserId: '',
      workSheetRespondAccept: {
        formList: [
          {
            title: '备注',
            prop: 'remark',
            type: 'textarea',
            placeholder: '请备注说明',
            maxlength: 100,
            required: false
          }
        ],
        form: {
          fkUserId: '',
          pkWsTaskId: '',
          remark: ''
        },
        rules: {}
      },
      workSheetRespondBack: {
        formList: [
          {
            title: '退回原因',
            prop: 'remark',
            type: 'textarea',
            placeholder: '将退回至服务台重新派单，请输入退回原因',
            maxlength: 100,
            required: true
          }
        ],
        form: {
          fkUserId: '',
          pkWsTaskId: '',
          remark: ''
        },
        rules: {
          remark: [
            {
              required: true,
              message: '请输入退回原因',
              trigger: ''
            }
          ]
        }
      },
      workSheetRespondResend: {
        formList: [
          {
            title: '处理科室',
            prop: 'businessDeptName',
            propVal: 'businessDeptId',
            type: 'select',
            mode: 'select',
            placeholder: '请选择处理科室',
            required: true,
            optionList: null,
            relationProp: [
              {
                prop: 'fkFaultType',
                propVal: 'fkFaultTypeId'
              }
            ]
          },
          {
            title: '业务类型',
            prop: 'fkFaultType',
            propVal: 'fkFaultTypeId',
            type: 'select',
            mode: 'select',
            placeholder: '请选择故障类型',
            required: true,
            optionList: null,
            searchParams: [
              {
                name: 'fkDeptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/faultType/selectFaultTypeList/1'
          },
          {
            title: '处理人',
            prop: 'fkUser',
            propVal: 'fkUserId',
            type: 'select',
            mode: 'person',
            chooseType: 'radio',
            getListType: 'scollSearch',
            searchParams: [
              {
                name: 'faultTypeId',
                value: 'fkFaultTypeId',
                message: '请先选择故障类型'
              },
              {
                name: 'deptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/workSheetPeopple/getNoPagePeopleInfoList',
            placeholder: '请选择处理人',
            personInfoProp: {
              describe: 'deptName',
              name: 'name',
              nameFormatt: ['name', 'processCountString'],
              key: 'userId'
            },
            required: true
          },
          {
            title: '转发说明',
            prop: 'remark',
            type: 'textarea',
            placeholder: '请输入转发说明',
            required: false
          }
        ],
        form: {
          businessDeptName: '',
          businessDeptId: '',
          fkFaultType: '',
          fkFaultTypeId: '',
          fkFormerUserId: '',
          fkUser: '',
          fkUserId: '',
          pkWsTaskId: '',
          remark: ''
        },
        rules: {
          businessDeptName: [
            {
              required: true,
              message: '请选择处理科室',
              trigger: ''
            }
          ],
          fkFaultType: [
            {
              required: true,
              message: '请选择故障类型',
              trigger: ''
            }
          ],
          fkUser: [
            {
              required: true,
              message: '请选择处理人',
              trigger: ''
            }
          ]
        }
      },
      radioForm: [
        {
          title: '工单响应',
          prop: 'type',
          type: 'radio',
          required: true,
          radioCheckWrap: false,
          radioList: [
            {
              label: '接单',
              value: 'workSheetRespondAccept'
            },
            {
              label: '退回',
              value: 'workSheetRespondBack'
            },
            {
              label: '转发',
              value: 'workSheetRespondResend'
            }
          ],
          callback: value => {
            this.type = value;
          }
        }
      ],
      formList: [],
      form: {},
      rules: {}
    };
  },
  watch: {
    type: {
      handler(newVal) {
        this.initForm(newVal);
      }
    },
    'workSheetRespondResend.form.businessDeptId': {
      handler(val) {
        this.handleResendDeptIdChange(val);
      }
    }
  },
  onLoad(opt) {
    this.pkWsTaskId = opt.pkWsTaskId;
    this.fkFormerUserId = opt.fkUserId;
    this.oldBusinessDeptId = opt.businessDeptId || '';
    this.getFaultTypeDeptList();
    this.$nextTick(() => {
      this.initForm(this.type);
    });
  },
  methods: {
    getFaultTypeDeptList() {
      this.ajax.getFaultTypeDeptList().then(res => {
        let fkFaultTypeDeptList = res.object.map(item => {
          return {
            value: item.deptId,
            label: item.deptName
          };
        });
        this.$set(
          this.workSheetRespondResend.formList[0],
          'optionList',
          fkFaultTypeDeptList
        );
      });
    },
    initForm(typeVal) {
      this.formList = [...this.radioForm, ...this[typeVal].formList];
      this.form = this[typeVal].form;
      this.form.type = typeVal;
      this.rules = this[typeVal].rules;
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    },
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.form.pkWsTaskId = this.pkWsTaskId;

      if (this.type == 'workSheetRespondResend') {
        this.form.fkFormerUserId = this.fkFormerUserId;
      } else {
        this.form.fkUserId = this.fkFormerUserId;
      }
      if (!this.form.fkUserId) {
        delete this.form.fkUserId;
      }
      this.ajax[this.type](this.form).then(() => {
        uni.reLaunch({
          url: '/pages/work-order-handle-list/index'
        });
      });
    },
    handleResendDeptIdChange(val) {
      if (!this.oldBusinessDeptId) {
        return;
      }
      let dept = [
          {
            title: '业务类型',
            prop: 'fkFaultType',
            propVal: 'fkFaultTypeId',
            type: 'select',
            mode: 'select',
            placeholder: '请选择故障类型',
            required: true,
            optionList: null,
            searchParams: [
              {
                name: 'fkDeptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/faultType/selectFaultTypeList/1'
          },
          {
            title: '处理人',
            prop: 'fkUser',
            propVal: 'fkUserId',
            type: 'select',
            mode: 'person',
            chooseType: 'radio',
            getListType: 'scollSearch',
            searchParams: [
              {
                name: 'faultTypeId',
                value: 'fkFaultTypeId',
                message: '请先选择故障类型'
              },
              {
                name: 'deptId',
                value: 'businessDeptId',
                message: '请先选择处理科室'
              }
            ],
            searchApi: '/workSheetPeopple/getNoPagePeopleInfoList',
            placeholder: '请选择处理人',
            personInfoProp: {
              describe: 'deptName',
              name: 'name',
              nameFormatt: ['name', 'processCountString'],
              key: 'userId'
            },
            required: true
          }
        ],
        nowFormList = this.workSheetRespondResend.formList || [],
        fkFaultTypeIndex = nowFormList.findIndex(
          item => item.prop == 'fkFaultType'
        );
      if (val == this.oldBusinessDeptId) {
        if (fkFaultTypeIndex >= 0) {
          return;
        }

        nowFormList.splice(1, 0, ...dept);
        this.workSheetRespondResend.form.fkFaultTypeId = '';
        this.workSheetRespondResend.form.fkUser = '';
        this.workSheetRespondResend.form.fkUserId = '';
      } else {
        if (fkFaultTypeIndex >= 0) {
          nowFormList.splice(fkFaultTypeIndex, 2);
          this.workSheetRespondResend.form.fkFaultTypeId = '';
          this.workSheetRespondResend.form.fkUser = '';
          this.workSheetRespondResend.form.fkUserId = '';
        }
      }
      this.$set(this.workSheetRespondResend, 'formList', nowFormList);
      this.initForm('workSheetRespondResend');
    }
  }
};
