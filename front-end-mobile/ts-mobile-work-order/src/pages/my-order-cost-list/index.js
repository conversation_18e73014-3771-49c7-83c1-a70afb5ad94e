import date from '../../assets/js/date.js';

export default {
  data() {
    return {
      allMoney: '0.00',
      costList: [],
      costBeiginTime: '',
      costEndTime: '',
      showPicker: false,
      pickerProp: 'costBeiginTime',
      pickerParams: {
        year: true,
        month: true,
        day: true
      }
    };
  },
  filters: {
    computedMoney: function(money) {
      let labelStr = money.toLocaleString();
      if (labelStr.split('.').length > 1) {
        labelStr.split('.')[1].length == 1 ? (labelStr += '0') : null;
      }
      labelStr.split('.').length == 1 ? (labelStr += '.00') : null;
      return labelStr;
    }
  },
  onShow() {
    this.$nextTick(() => {
      this.$refs.mescroll && this.$refs.mescroll.downCallback();
    });
  },
  methods: {
    goBack() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    clickSearch() {
      this.$refs.mescroll.downCallback();
    },
    getDatas(page, successCallback, errorCallback) {
      let searchData = {
        pageNo: page.num,
        pageSize: page.size,
        sidx: 'create_time',
        sord: 'desc',
        fillUserId: this.$store.state.common.userInfo.empId,
        costBeiginTime: this.costBeiginTime,
        costEndTime: this.costEndTime
      };

      this.ajax.getWorkOrderCostSum(searchData).then(res => {
        if (!res.success) {
          return;
        }
        let moneyStr = (res.object || 0).toLocaleString();
        if (moneyStr.split('.').length > 1) {
          moneyStr.split('.')[1].length == 2 ? null : (moneyStr += '0');
        }
        moneyStr.split('.').length == 1 ? (moneyStr += '.00') : null;

        this.allMoney = moneyStr;
      });
      this.ajax
        .getWorkOrderCostDetailList(searchData)
        .then(res => {
          let datas = res.rows || res.object;
          successCallback(datas, res.totalCount);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setDatas(rows) {
      this.costList = this.costList.concat(rows);
    },
    datasInit() {
      this.costList = [];
    },
    clickDatePicker(prop) {
      this.showPicker = true;
      this.pickerProp = prop;
    },
    pickerConfirm(e) {
      this.$set(this, this.pickerProp, date.formatTime(e, 'yy-MM-dd'));
      if (
        this.costBeiginTime &&
        this.costEndTime &&
        this.costBeiginTime > this.costEndTime
      ) {
        this.costBeiginTime = '';
        this.costEndTime = '';
      }
    },
    handlePickerCancel() {
      this.$set(this, this.pickerProp, '');
    },
    async handleClickCostItem(item) {
      let res = await this.ajax.getWorkOrderDetail(item.workNumber);
      let workOrderInfo = res.object.wsWsSheetInfoOutVo;
      uni.setStorageSync('cost_detail', JSON.stringify(item));
      uni.setStorageSync('work_order_detail', JSON.stringify(workOrderInfo));
      uni.navigateTo({
        url: `/pages/work-order-register-cost/index?id=${this.workNumber}&formPage=work-order-detail`
      });
    }
  }
};
