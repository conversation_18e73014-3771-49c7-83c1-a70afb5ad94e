<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar title="我的费用" title-bold :custom-back="goBack"></u-navbar>
    <view class="date-picker-content">
      <view class="date-picker-item" @click="clickDatePicker('costBeiginTime')">
        <u-input
          placeholder="开始时间"
          type="select"
          v-model="costBeiginTime"
          :show-right-icon="false"
          @click="clickDatePicker('costBeiginTime')"
        />
        <u-icon name="riqi" custom-prefix="work-icon" color="#E0E6F0"></u-icon>
      </view>
      <view class="date-picker-connection">
        -
      </view>
      <view class="date-picker-item" @click="clickDatePicker('costEndTime')">
        <u-input
          placeholder="结束时间"
          type="select"
          v-model="costEndTime"
          :show-right-icon="false"
          @click="clickDatePicker('costEndTime')"
        />
        <u-icon name="riqi" custom-prefix="work-icon" color="#E0E6F0"></u-icon>
      </view>

      <u-button
        class="date-picker-search-button"
        type="primary"
        size="mini"
        @click="clickSearch"
      >
        搜索
      </u-button>
    </view>
    <u-picker
      mode="time"
      v-model="showPicker"
      :params="pickerParams"
      cancel-text="清除"
      @confirm="pickerConfirm"
      @cancel="handlePickerCancel"
    ></u-picker>

    <view class="all-money-content">填报经费共计 {{ allMoney }}元</view>

    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getDatas"
        @setDatas="setDatas"
        @datasInit="datasInit"
      >
        <view
          v-for="(cost, index) of costList"
          :key="index"
          @click="handleClickCostItem(cost)"
          class="cost-item"
        >
          <view class="top-content">
            <view>{{ cost.costTime }}</view>
            <view>{{ cost.money | computedMoney }}元</view>
          </view>
          <view class="cost-desion">
            <u-icon v-if="cost.fileCount > 0" name="attach"></u-icon>
            {{ cost.costDeion }}
          </view>
          <view>工单编号：{{ cost.workNumber }}</view>
          <view>填报时间：{{ cost.createTime }}</view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import indexJs from './index';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  name: 'my-order-cost-list',
  mixins: [indexJs],
  components: {
    mescroll
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.date-picker-content {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: $u-interval-primary;
  margin-bottom: $u-interval-primary;
}
.date-picker-item {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0 $uni-spacing-row-base;
  border: 1px solid #e0e6f0;
  border-radius: $uni-border-radius-base;
}
.date-picker-search-button {
  margin-left: $uni-spacing-row-base;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  height: 60rpx;
  line-height: 60rpx;
}
.mescroll-content {
  flex: 1;
  position: relative;
}
.all-money-content {
  background-color: #fff;
  padding: $u-interval-primary;
  margin-bottom: $u-interval-primary;
  font-size: 32rpx;
  color: #ec7b25;
}

.cost-item {
  padding: 0 16px;
  font-size: 24rpx;
  line-height: 36rpx;
  background-color: #fff;
  margin-bottom: $u-interval-primary;
  color: $u-tips-color;
  padding-bottom: 12px;
}
.top-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 68rpx;
  border-bottom: 1px solid $u-bg-color;
  color: $u-main-color;

  > view:last-child {
    font-size: 28rpx;
    color: $u-type-error;
  }
}
.cost-desion {
  font-size: 28rpx;
  line-height: 44rpx;
  margin-top: $u-interval-primary;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: $u-main-color;
  .u-icon {
    margin-right: 4px;
  }
}
</style>
