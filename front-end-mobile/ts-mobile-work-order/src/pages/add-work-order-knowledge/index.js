export default {
  data() {
    return {
      fromPage: '',
      title: '新增知识点',
      imgUploadUrl: '/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk',
      imgUploadParamName: 'file',
      formList: [
        {
          title: '知识类型',
          prop: 'fkKnowledgeType',
          propVal: 'fkKnowledgeTypeId',
          type: 'select',
          mode: 'select',
          placeholder: '请选择知识类型',
          required: true,
          optionList: null
        },
        {
          title: '推荐处理时长',
          prop: 'recommendedWorkHours',
          type: 'number',
          placeholder: '请输入推荐处理时长',
          maxlength: 30,
          rightSlot: 'H',
          rightSlotClass: 'u-type-primary u-font-base',
          required: false
        },
        {
          title: '知识主题	',
          prop: 'knowledgeTitle',
          type: 'text',
          placeholder: '请输入知识主题',
          maxlength: 30,
          required: true
        }
        // {
        // 	title: '描述及解决方案',
        // 	prop: 'knowledgeContent',
        // 	type: 'textarea',
        // 	placeholder: '请输入描述及解决方案',
        // 	required: false,
        // 	height: '600',
        // }
      ],
      form: {
        pkKnowledgeBaseId: '',
        knowledgeStatus: 0,
        fkKnowledgeType: '',
        fkKnowledgeTypeId: '',
        recommendedWorkHours: '',
        knowledgeTitle: '',
        knowledgeContent: ''
      },
      rules: {
        fkKnowledgeType: [
          {
            required: true,
            message: '请选择知识类型',
            trigger: ''
          }
        ],
        knowledgeTitle: [
          {
            required: true,
            message: '请输入知识主题',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.getKnowledgeType();
    if (opt && opt.id) {
      this.form.pkKnowledgeBaseId = opt.id;
      this.title = '编辑知识点';
      this.getKnowledgeInfo(opt.id);
    } else if (opt && opt.workNumber) {
      this.getWorkInfo(opt.workNumber);
    }
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    getKnowledgeType() {
      this.ajax.getKnowledgeTypeAllList().then(res => {
        let knowledgeTypeList = res.object.map(item => {
          return {
            value: item.id,
            label: item.name
          };
        });
        this.$set(this.formList[0], 'optionList', knowledgeTypeList);
      });
    },
    getKnowledgeInfo(id) {
      this.ajax.getKnowledgeBaseInfo(id).then(res => {
        this.form.knowledgeContent = res.object.knowledgeContent;
        this.form.fkKnowledgeType = res.object.fkKnowledgeTypeName;
        this.form.fkKnowledgeTypeId = res.object.fkKnowledgeTypeId;
        this.form.recommendedWorkHours = res.object.recommendedWorkHours;
        this.form.knowledgeTitle = res.object.knowledgeTitle;
        this.form.knowledgeStatus = res.object.knowledgeStatus;
      });
    },
    getWorkInfo(id) {
      this.ajax.getWorkOrderDetail(id).then(res => {
        this.form.knowledgeTitle = res.object.wsWsSheetInfoOutVo.faultDeion;
        this.form.recommendedWorkHours =
          res.object.wsWsSheetInfoOutVo.workHours;
      });
      this.ajax.getSubmitKnowledgeBaseInfo(id).then(res => {
        this.form.knowledgeContent = res.object.takeRemark;
      });
    },
    onEditorReady() {
      // #ifdef MP-BAIDU
      this.editorCtx = requireDynamicLib('editorLib').createEditorContext(
        'editorId'
      );
      // #endif

      // #ifdef APP-PLUS || H5 ||MP-WEIXIN
      uni
        .createSelectorQuery()
        .select('#editor')
        .context(res => {
          this.editorCtx = res.context;
        })
        .exec();
      // #endif
    },
    onClick(status) {
      this.form.knowledgeStatus = status;
      this.form.knowledgeContent = this.$refs.editor.getContent().html;
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.addKnowledge(this.form).then(res => {
        let path = `/pages/knowledge-management-list/index?tabIndex=${
          this.form.knowledgeStatus == 0 ? 4 : 1
        }`;
        uni.redirectTo({
          url: path
        });
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      } else {
        uni.navigateBack();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    }
  }
};
