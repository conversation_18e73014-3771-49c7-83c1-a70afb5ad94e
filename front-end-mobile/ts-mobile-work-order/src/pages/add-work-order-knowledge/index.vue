<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar :title="title" title-bold :custom-back="goBack"></u-navbar>
    <base-form
      ref="baseForm"
      :formList="formList"
      :formData.sync="form"
      :rules="rules"
      :showSubmitButton="false"
      @submit="submit"
    ></base-form>
    <view class="editor-box">
      <view class="editor-title">
        描述及解决方案
      </view>
      <base-editor
        ref="editor"
        :img-upload-host="`${$store.state.common.baseHost}`"
        :img-upload-url="`${imgUploadUrl}`"
        :img-upload-param-name="imgUploadParamName"
        tool-style="background-color: #fff;"
        :contents="form.knowledgeContent"
        placeholder="请输入描述及解决方案"
      ></base-editor>
    </view>
    <view class="button-box">
      <view
        class="button-item"
        v-if="form.knowledgeStatus == 0"
        @click="onClick(0)"
      >
        存草稿
      </view>
      <view class="button-item" @click="onClick(1)">
        提交
      </view>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import baseForm from '../../components/base-form/base-form.vue';
import baseEditor from '../../components/base-editor/base-editor.vue';
export default {
  name: 'work-order-evaluate',
  mixins: [index],
  components: {
    baseForm,
    baseEditor
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.button-box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  font-size: $uni-font-size-lg;
  background-color: #ffffff;
  text-align: center;
  height: 44px;
  font-size: 28rpx;
  @include vue-flex;
}
.button-item {
  flex: 1;
}
.editor-title {
  font-size: 28rpx;
  padding: 20rpx 30rpx;
  color: $uni-text-color;
  background-color: #ffffff;
}
</style>
