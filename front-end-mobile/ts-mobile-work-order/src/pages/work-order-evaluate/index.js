export default {
  data() {
    return {
      formList: [
        {
          title: '处理速度',
          prop: 'processSpeed',
          type: 'rate',
          inactiveColor: '#ff9900',
          activeColor: '#ff9900'
        },
        {
          title: '服务态度',
          prop: 'serviceAttituude',
          type: 'rate',
          inactiveColor: '#ff9900',
          activeColor: '#ff9900'
        },
        {
          title: '技术水平	',
          prop: 'technicalLevel',
          type: 'rate',
          inactiveColor: '#ff9900',
          activeColor: '#ff9900'
        }
      ],
      form: {
        pkWsTaskId: '',
        processSpeed: 5,
        serviceAttituude: 5,
        technicalLevel: 5
      },
      rules: {}
    };
  },
  onLoad(opt) {
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkUserId = this.$store.state.common.userInfo.empId;
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.workSheetToEvaluate(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/my-work-order-list/index?tabIndex=1'
        });
      });
    }
  }
};
