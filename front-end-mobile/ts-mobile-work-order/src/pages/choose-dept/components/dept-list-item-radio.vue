<template>
  <view class="dept-list-item-radio-group">
    <view
      class="dept-list-item-radio"
      v-for="item in list"
      :key="item[props.key]"
      @click="toggle(item)"
    >
      <view class="radio__icon-wrap" :class="item.checked | iconClass">
        <u-icon
          class="radio__icon-wrap__icon"
          name="checkbox-mark"
          :size="iconSize"
          :color="item.checked | iconColor"
        />
      </view>
      <dept-list-item-info
        class="radio__info-wrap"
        :dept="item"
      ></dept-list-item-info>
    </view>
  </view>
</template>

<script>
import deptListItemInfo from './dept-list-item-info.vue';
export default {
  name: 'dept-list-item-radio',
  components: {
    deptListItemInfo
  },
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    value: {
      type: String,
      default: ''
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    iconColor: {
      type: String,
      default: ''
    },
    props: {
      type: Object,
      default: () => {
        return {
          key: 'userId'
        };
      }
    }
  },
  filters: {
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'radio__icon-wrap--checked' : '';
    }
  },
  methods: {
    toggle(e) {
      let values = [];
      if (e.checked) {
        e.checked = !e.checked;
      } else {
        this.list.map(i => {
          if (i[this.props.key] == e[this.props.key]) {
            i.checked = true;
            values.push(i);
          } else {
            i.checked = false;
          }
        });
      }
      this.$emit('change', values);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/flex.scss';
.dept-list-item-radio {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  background-color: #ffffff;
  @include vue-flex;
  align-items: center;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
.radio__icon-wrap {
  color: $uni-text-content-color;
  @include vue-flex;
  flex: none;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 38rpx;
  height: 38rpx;
  color: transparent;
  text-align: center;
  transition-property: color, border-color, background-color;
  font-size: $uni-icon-size-base;
  border: 1px solid $uni-text-color-disable;
  border-radius: 100%;
  transition-duration: 0.2s;
}
.radio__icon-wrap--checked {
  border-color: $u-type-primary;
  background-color: $u-type-primary;
}
.radio__info-wrap {
  flex: 1;
  margin-left: $uni-spacing-row-base;
}
</style>
