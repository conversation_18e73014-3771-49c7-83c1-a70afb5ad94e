<template>
  <view class="ts-container">
    <u-navbar title="工单助手" title-bold :custom-back="goBack"></u-navbar>
    <view class="work-order-message-list-box">
      <mescroll
        ref="mescroll"
        v-if="showMessageListScroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          class="work-order-message-item-box"
          v-for="(item, key) in list"
          :key="key"
        >
          <view class="work-order-message-item-time">
            {{ item.createTime | indexTimeFilter }}
          </view>
          <view
            class="work-order-message-item"
            @tap="jumpToDetail(item.workNumber, item.pkWsMessageId)"
          >
            <view class="work-order-message-item-top">
              <text class="work-order-message-item-title">{{
                item.messageTitle
              }}</text>
            </view>
            <view class="work-order-message-item-content">
              <rich-text :nodes="item.messageContent"></rich-text>
            </view>
            <view class="work-order-message-item-bottom">查看详情</view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  name: 'work-order-message-list',
  mixins: [index],
  components: {
    mescroll
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
@import '../../assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.work-order-message-list-box {
  flex: 1;
  position: relative;
}
.work-order-message-item-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
  text-align: center;
  margin-top: 20rpx;
}
.work-order-message-item {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
}
.work-order-message-item-top {
  @include vue-flex;
  padding: 22rpx 30rpx 0;
}
.work-order-message-item-title {
  flex: 1;
  font-size: 30rpx;
  font-weight: bold;
  color: $uni-text-color;
  @include ellipsis;
}
.work-order-message-item-content {
  padding: 22rpx 30rpx;
  font-size: 28rpx;
  color: #757575;
  @include multiLineEllipsis(3);
}
.work-order-message-item-bottom {
  text-align: right;
  padding: 22rpx 30rpx;
  position: relative;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  &::after {
    position: absolute;
    content: '';
    top: 0;
    height: 1px;
    background-color: #eee;
    left: 30rpx;
    right: 30rpx;
    transform: scaleY(0.5);
  }
}
</style>
