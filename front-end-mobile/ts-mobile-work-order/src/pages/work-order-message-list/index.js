export default {
  data() {
    return {
      fromPage: '',
      list: [],
      showMessageListScroll: true
    };
  },
  onLoad(opt) {
    if (opt && opt.formPage) {
      this.fromPage = opt.formPage;
    }
  },
  onShow() {
    this.showMessageListScroll = false;
    setTimeout(() => {
      this.showMessageListScroll = true;
    }, 50);
  },
  methods: {
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getMessagePageList({
          pageNo: page.num,
          pageSize: page.size,
          isRead: 0,
          sord: 'desc',
          sidx: 'create_time'
        })
        .then(res => {
          if (res.errMsg) {
            successCallback([]);
            this.$nextTick(() => {
              let empDOM = this.$refs.mescroll.$el.querySelector('.empty-tip');
              empDOM.innerHTML =
                res.errMsg == 'request:fail timeout'
                  ? '请求超时，请刷新重试'
                  : '加载失败，请刷新重试';
            });
          } else {
            successCallback(res.object);
          }
        })
        .catch(res => {
          successCallback([]);
          this.$nextTick(() => {
            let empDOM = this.$refs.mescroll.$el.querySelector('.empty-tip');
            empDOM.innerHTML = '加载失败，请刷新重试';
          });
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    jumpToDetail(id, messageId) {
      this.ajax.readMessageDetail(messageId);
      uni.navigateTo({
        url: `/pages/work-order-detail/index?id=${id}&fromPage=index`
      });
    },
    goBack() {
      if (this.fromPage == 'index' || this.fromPage == '') {
        this.jumpToIndex();
      }
    },
    jumpToIndex() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/index'
      });
    }
  }
};
