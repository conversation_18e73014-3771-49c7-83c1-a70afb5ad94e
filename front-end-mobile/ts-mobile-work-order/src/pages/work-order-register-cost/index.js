import common from '../../util/common';
import { Base64 } from '@/assets/js/base64.min.js';

export default {
  data() {
    return {
      isEdit: false,
      modalShow: false,

      costDetail: {},
      workOrderDetail: {},
      enclosureList: [],
      previewImgList: [],
      businessId: '',
      deleteId: '',
      uToastContent: '',
      handleUToasConfirm: this.handleDeleteFile,

      formList: [
        {
          title: '金额(元)',
          prop: 'money',
          type: 'text',
          placeholder: '最多输入两位小数',
          required: true,
          callback: this.handleMoneyInput
        },
        {
          title: '发生时间',
          prop: 'costTime',
          type: 'select',
          mode: 'time',
          params: {
            year: true,
            month: true,
            day: true,
            hour: true,
            minute: true
          },
          field: 'yy-MM-dd HH:mm',
          required: true
        },
        {
          title: '费用描述',
          prop: 'costDeion',
          type: 'textarea',
          placeholder: '请输入200字以内的描述',
          maxlength: 200,
          required: true
        }
      ],
      rules: {
        money: [
          {
            validator: (rule, value, callback) => {
              if (value > 0) {
                callback();
              } else {
                callback('请填写上报金额');
              }
            }
          }
        ],
        costTime: [
          { required: true, message: '请选择费用发生时间', trigger: '' }
        ],
        costDeion: [{ required: true, message: '请填写费用描述', trigger: '' }]
      }
    };
  },
  computed: {
    pageTitle: function() {
      return this.isEdit ? '费用填报' : '费用详情';
    },
    register: function() {
      return (
        [this.costDetail.fillDeptName, this.costDetail.fillUser]
          .filter(item => item)
          .join('-') ||
        [
          this.$store.state.common.userInfo.orgName,
          this.$store.state.common.userInfo.employeeName
        ]
          .filter(item => item)
          .join('-')
      );
    },
    registerTime: function() {
      return (
        (!this.isEdit && this.costDetail.createTime) ||
        this.$dayjs().format('YYYY-MM-DD HH:mm')
      );
    },
    pageActionList: function() {
      if (this.isEdit) {
        return [
          {
            title: '提交',
            action: this.submit
          }
        ];
      }
      if (
        this.costDetail.fillUserId == this.$store.state.common.userInfo.empId
      ) {
        return [
          {
            title: '删除',
            class: 'delete-btn',
            action: this.handleShowToastDeleteCost
          },
          {
            title: '编辑',
            action: this.handleEdit
          }
        ];
      }
    }
  },
  onLoad(opt) {
    if (opt && opt.formPage) {
      this.fromPage = opt.formPage;
    }
    let costDetail = JSON.parse(uni.getStorageSync('cost_detail') || '""');
    this.workOrderDetail = JSON.parse(
      uni.getStorageSync('work_order_detail') || '{}'
    );
    if (costDetail) {
      this.costDetail = costDetail;
      this.businessId = costDetail.files;
      costDetail.files ? this.getFiles() : null;
    } else {
      this.costDetail = {
        costTime: this.$dayjs().format('YYYY-MM-DD HH:mm')
      };
      this.isEdit = true;
    }
    if (!this.businessId) {
      this.businessId = common.createUUID();
    }
  },
  methods: {
    goBack() {
      this.fromPage
        ? uni.navigateBack()
        : this.$parentTypeFun({
            type: 'redirectTo',
            path: '/workbench'
          });
    },
    async submit() {
      let validate = await this.$refs.baseForm.validate();
      if (!validate) {
        return;
      }

      this.costDetail.workNumber = this.workOrderDetail.workNumber;
      this.enclosureList.length
        ? (this.costDetail.files = this.businessId)
        : null;
      this.costDetail.costTime += ':00';
      this.ajax.saveEditCost(this.costDetail).then(res => {
        if (!res.success) {
          uni.showToast({
            icon: 'none',
            title: res.message || '保存失败'
          });
          return;
        }
        uni.showToast({
          icon: 'none',
          title: '保存成功'
        });
        setTimeout(() => {
          this.goBack();
        }, 1000);
      });
    },
    handleEdit() {
      this.isEdit = true;
    },
    handleMoneyInput(value, prop) {
      return (value.match(/\d+\.{0,1}(\d{1,2}){0,1}/g) || [''])[0];
    },
    handleUpload() {
      // #ifdef H5
      uni.chooseFile({
        count: 9,
        success: ({ tempFiles }) => {
          const fileList = [];
          for (let i = 0; i < tempFiles.length; i++) {
            let file = tempFiles[i];

            if (
              common.isDoc(file.name) ||
              common.isImg(file.name) ||
              file.name.toLowerCase().indexOf('.mhtml') >= 0
            ) {
              fileList.push(file);
            }
          }
          if (!fileList.length) {
            uni.showToast({
              icon: 'none',
              title: '未选中正确的文件类型'
            });
            return;
          }

          let ajaxList = [];
          fileList.forEach(file => {
            let formData = new FormData();
            formData.append('file', file);
            ajaxList.push(this.ajax.upload(this.businessId, formData));
          });

          Promise.all(ajaxList).then(resList => {
            let errorList = [],
              successList = [];
            resList.forEach((res, index) => {
              if (!res.success) {
                errorList.push(fileList[index].name);
              } else {
                successList.push(res.object[0]);
              }
            });
            if (errorList.length) {
              uni.showToast({
                icon: 'none',
                title: errorList.join(',') + '文件上传失败'
              });
            }

            this.enclosureList.push(...successList);
          });
        }
      });
      // #endif
    },
    handleShowToast(item) {
      this.deleteId = item.fileId;
      this.uToastContent = '删除后将无法恢复，确定要删除该文件吗？';
      this.handleUToasConfirm = this.handleDeleteFile;
      this.modalShow = true;
    },
    handleShowToastDeleteCost() {
      this.deleteId = this.costDetail.pkWsCostId;
      this.uToastContent = '删除后将无法恢复，确定要删除该记录吗？';
      this.handleUToasConfirm = this.handleDeleteCost;
      this.modalShow = true;
    },
    handleDeleteCost() {
      this.ajax.deleteCostData([this.deleteId]).then(res => {
        if (!res.success) {
          uni.showToast({
            icon: 'none',
            title: res.message || '删除失败'
          });
          return;
        }
        uni.showToast({
          icon: 'none',
          title: '删除成功'
        });
        setTimeout(() => {
          this.goBack();
        }, 1000);
      });
    },
    handleDeleteFile() {
      this.ajax.deleteFileId(this.deleteId).then(res => {
        if (!res.success) {
          uni.showToast({
            icon: 'none',
            title: res.message || '附件删除失败'
          });
          return;
        }
        uni.showToast({
          icon: 'none',
          title: '附件删除成功'
        });
        let index = this.enclosureList.findIndex(
          item => item.fileId == this.deleteId
        );
        this.enclosureList.splice(index, 1);
      });
    },
    getFiles() {
      this.ajax.getFileAttachmentByBusinessId(this.businessId).then(res => {
        if (!res.success) {
          uni.showToast({
            icon: 'none',
            title: res.message || '附件信息获取失败,请退出重试'
          });
          return;
        }
        this.enclosureList = res.object.map(item => ({
          fileId: item.id,
          filePath: item.realPath,
          fileRealName: item.originalName,
          fileName: item.realPath.split('/').pop() + `.${item.fileExtension}`
        }));
      });
    },
    //文件预览
    previewFile(e) {
      if (common.isDoc(e.fileRealName)) {
        let filePath = `${this.$documentPreviewHost}${e.filePath}?fullfilename=${e.fileName}`;
        uni.navigateTo({
          url: `/pages/webview/index?url=${
            this.$store.state.common.baseHost
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
        return;
      }

      this.previewImgList = this.enclosureList
        .filter(item => common.isImg(item.fileRealName))
        .map(item => ({ ...item, url: location.origin + item.filePath }));
      let eIndex = this.previewImgList.findIndex(
        item => item.filePath == e.filePath
      );

      this.$nextTick(() => {
        this.$refs.imagePreview.doPreviewImage(
          location.origin + e.filePath,
          eIndex
        );
      });
    }
  }
};
