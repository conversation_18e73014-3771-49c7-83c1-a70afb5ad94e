<template>
  <view class="ts-container safe-area-inset-bottom">
    <u-navbar :title="pageTitle" title-bold :custom-back="goBack"></u-navbar>
    <view class="order-content">
      <view>
        {{ workOrderDetail.faultDeion }}
      </view>
      <view>填报人：{{ register }}</view>
      <view>填报时间： {{ registerTime }}</view>
    </view>

    <u-upload
      ref="imagePreview"
      :disabled="true"
      :file-list="previewImgList"
    ></u-upload>

    <view v-if="isEdit">
      <base-form
        ref="baseForm"
        :formList="formList"
        :formData.sync="costDetail"
        :rules="rules"
        :showSubmitButton="false"
        @submit="submit"
      >
      </base-form>
      <u-form-item label="附件" class="form-item">
        <view class="input-icon">
          <u-icon name="plus-circle" size="32" @click="handleUpload"></u-icon>
        </view>
      </u-form-item>
      <view
        class="detail-content"
        :style="{ 'padding-top': enclosureList.length ? '11px' : '0' }"
      >
        <view
          v-for="(item, index) of enclosureList"
          :key="index"
          class="file-item"
        >
          <u-icon name="attach"></u-icon>
          <view class="file-item-name">{{ item.fileRealName }}</view>
          <view
            v-if="item.fileRealName.toLowerCase().indexOf('.mhtml') == -1"
            class="action-btn"
            @click="previewFile(item)"
          >
            预览
          </view>
          <a class="action-btn" :href="item.filePath">下载</a>
          <view
            v-if="isEdit"
            class="action-btn delete-btn"
            @click="handleShowToast(item)"
          >
            删除
          </view>
        </view>
      </view>
    </view>
    <view v-else class="detail-content">
      <view class="detail-item border-bottom">
        <view>金额(元)：</view>
        <view>{{ costDetail.money }}</view>
      </view>
      <view class="detail-item border-bottom">
        <view>发生时间：</view>
        <view>{{ costDetail.costTime }}</view>
      </view>
      <view class="detail-item">
        <view>费用描述：</view>
      </view>
      <view class="border-bottom" style="padding-bottom: 24rpx;">
        {{ costDetail.costDeion }}
      </view>

      <view class="detail-item">附件：</view>
      <view
        v-for="(item, index) of enclosureList"
        :key="index"
        class="file-item"
      >
        <u-icon name="attach"></u-icon>
        <view class="file-item-name">{{ item.fileRealName }}</view>
        <view
          v-if="item.fileRealName.toLowerCase().indexOf('.mhtml') == -1"
          class="action-btn"
          @click="previewFile(item)"
        >
          预览
        </view>
        <a class="action-btn" :href="item.filePath">下载</a>
      </view>
    </view>

    <view class="page-action-content">
      <view
        v-for="(item, index) of pageActionList"
        :key="index"
        class="page-action-btn"
        :class="item.class"
        @click="item.action()"
      >
        {{ item.title }}
      </view>
    </view>

    <u-modal
      ref="uModal"
      v-model="modalShow"
      :show-cancel-button="true"
      title="提示"
      :content="uToastContent"
      @confirm="handleUToasConfirm()"
    >
    </u-modal>
  </view>
</template>

<script>
import index from './index';

export default {
  name: 'work-order-register-cost',
  mixins: [index]
};
</script>

<style lang="scss" scoped>
.ts-container {
  padding-bottom: 44px;
}
.order-content {
  background-color: #fff;
  padding: 11px 16px;
  color: $u-tips-color;
  margin-bottom: $u-interval-primary;
  > view:first-child {
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
  }
}
.form-item {
  padding-left: $uni-spacing-row-lg;
  padding-right: $uni-spacing-row-lg;
  background-color: #fff;
}
.input-icon {
  width: 100%;
  text-align: right;
}
.detail-content {
  padding: 0 16px;
  background-color: #fff;
  .border-bottom {
    border-bottom: 1px solid $u-bg-color;
  }
}
.detail-item {
  display: flex;
  justify-content: space-between;
  line-height: 88rpx;
  > view:first-child {
    flex-shrink: 0;
  }
}
.file-item {
  display: flex;
  line-height: 44rpx;
  &:not(:last-child) {
    margin-bottom: 4px;
  }
  &:last-child {
    padding-bottom: 12px;
  }
  > view:nth-child(2) {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .action-btn {
    color: $u-type-primary;
    margin-left: $u-interval-primary;
    flex-shrink: 0;
  }
}
.delete-btn {
  color: $u-type-error !important;
}
.page-action-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 -1px 6px #ccc;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 9;
}
.page-action-btn {
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  font-size: $uni-font-size-base;
  color: $u-type-primary;
  height: 44px;
  line-height: 44px;
  flex: 1;
}
.file-item-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
