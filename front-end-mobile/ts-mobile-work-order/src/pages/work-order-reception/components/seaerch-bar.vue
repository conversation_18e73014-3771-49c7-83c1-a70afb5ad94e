<template>
  <view class="search-content">
    <u-search
      shape="square"
      v-model="keywords"
      :show-action="false"
      placeholder="输入工单号\故障关键词搜索"
      @search="handleSearchDataChange"
      @clear="handleSearchDataChange"
    ></u-search>

    <view
      class="work-icon work-icon-xinzeng has-more-condition"
      @click="handleAdd"
    ></view>

    <view
      class="work-icon work-icon-shaixuan"
      :class="hasCondition ? 'has-more-condition' : ''"
      @click="handleOpenMoreSearch"
    ></view>

    <screen
      ref="screen"
      :formList="searchFormList"
      :formData="searchData"
      @reset="handleSearchDataReset"
      @change="handleSearchDataChange"
    ></screen>
  </view>
</template>

<script>
import screen from '@/pages/work-order-handle-list/components/screen.vue';

export default {
  components: {
    screen
  },
  props: {
    firstLoadData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      keywords: '',
      searchData: {
        repairManDept: [],
        repairMan: [],
        repairTime: [],
        handlePerson: [],
        faultEmergency: '',
        workStatusValue: '1,2,3,6,7,8'
      },
      searchFormList: [
        {
          title: '状态',
          type: 'select',
          mode: 'checkbox',
          chooseType: 'checkbox',
          prop: 'workStatusValue',
          optionList: [
            {
              name: '待派单',
              checked: true,
              value: '1'
            },
            {
              name: '待接单',
              checked: true,
              value: '2'
            },
            {
              name: '处理中',
              checked: true,
              value: '3'
            },
            {
              name: '待验收',
              checked: false,
              value: '4'
            },
            {
              name: '待评价',
              checked: false,
              value: '5'
            },
            {
              name: '已完成',
              checked: true,
              value: '6'
            },
            {
              name: '已暂停',
              checked: true,
              value: '7'
            },
            {
              name: '已终止',
              checked: true,
              value: '8'
            }
          ]
        },
        {
          title: '紧急程度',
          type: 'select',
          mode: 'radio',
          chooseType: 'radio',
          prop: 'faultEmergency',
          optionList: [
            {
              value: '',
              name: '全部'
            },
            {
              value: '1',
              name: '非常紧急'
            },
            {
              name: '紧急',
              value: '2'
            },
            {
              name: '常规处理',
              value: '3'
            }
          ]
        },
        {
          title: '报修科室',
          type: 'select',
          mode: 'dept',
          chooseType: 'radio',
          prop: 'repairManDeptId',
          propVal: 'repairManDept'
        },
        {
          title: '报修人',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          prop: 'repairManId',
          propVal: 'repairMan',
          getListType: 'scollSearch',
          searchApi: '/ts-basics-bottom/employee/getEmployeePageList',
          searchApiType: 'POST',
          searchType: 'fullApiPost',
          personInfoProp: {
            name: 'employeeName',
            describe: 'orgName',
            key: 'employeeId',
            sex: 'gender',
            headImg: 'avatar'
          },
          searchParams: [
            {
              name: 'orgId',
              value: 'repairManDeptId'
            }
          ]
        },
        {
          title: '报修时间',
          type: 'range',
          mode: 'time',
          propVal: 'repairTime'
        },
        {
          title: '处理人',
          type: 'select',
          mode: 'person',
          chooseType: 'radio',
          propVal: 'handlePerson',
          getListType: 'scollSearch',
          searchApi: '/workSheetPeopple/getPeopleInfoList',
          searchParams: []
        }
      ],
      showMoreCondition: false
    };
  },
  computed: {
    hasCondition: function() {
      return (
        this.searchData.faultEmergency ||
        this.searchData.handlePerson.length ||
        this.searchData.repairTime.length ||
        this.searchData.repairMan.length ||
        this.searchData.repairManDept.length ||
        this.searchData.workStatusValue
      );
    }
  },
  watch: {
    'searchData.repairManDept': function() {
      this.searchData.repairMan = [];
    },
    'searchData.repairManDept': function() {
      this.searchData.repairManDeptId =
        (this.searchData.repairManDept[0] &&
          this.searchData.repairManDept[0].id) ||
        '';
    },
    firstLoadData: {
      handler: function(val) {
        this.searchData = { ...this.searchData, ...val };
        if (val.workStatusValue) {
          let statuList = val.workStatusValue.split(',');
          this.searchFormList[0].optionList.forEach(item => {
            item.checked = statuList.indexOf(String(item.value)) >= 0;
          });
          this.handleSearchDataChange();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getData() {
      let [beginTime = '', endTime = ''] = this.searchData.repairTime || [],
        repairManId = (this.searchData.repairMan[0] || {}).employeeId || '',
        repairManDeptId =
          (this.searchData.repairManDept[0] &&
            this.searchData.repairManDept[0].id) ||
          '',
        userId =
          (this.searchData.handlePerson[0] &&
            this.searchData.handlePerson[0].userId) ||
          '',
        faultEmergency = this.searchData.faultEmergency || '',
        workStatus = this.searchData.workStatusValue || '';
      return {
        fuzzy: this.keywords,
        beginTime, //报修开始时间
        endTime, //报修结束时间
        repairManId, //报修人ID
        repairManDeptId, //报修科室ID
        userId, //处理人ID
        faultEmergency, //紧急程度
        workStatus //工单状态
      };
    },
    handleAdd() {
      uni.setStorageSync(
        'orderReportConfig',
        JSON.stringify({
          isReception: true,
          formData: {}
        })
      );

      uni.navigateTo({
        url: `/pages/work-order-reporting/index?fromPage=work-order-reception`
      });
    },
    handleOpenMoreSearch() {
      this.$refs.screen.handleopen();
    },
    handleSearchDataReset() {
      this.searchData = {
        repairManDept: [],
        repairMan: [],
        repairTime: [],
        handlePerson: [],
        faultEmergency: '',
        workStatusValue: '1,2,3,6,7,8'
      };
      this.searchFormList[0].optionList = [
        {
          name: '待派单',
          checked: true,
          value: '1'
        },
        {
          name: '待接单',
          checked: true,
          value: '2'
        },
        {
          name: '处理中',
          checked: true,
          value: '3'
        },
        {
          name: '待验收',
          checked: false,
          value: '4'
        },
        {
          name: '待评价',
          checked: false,
          value: '5'
        },
        {
          name: '已完成',
          checked: true,
          value: '6'
        },
        {
          name: '已暂停',
          checked: true,
          value: '7'
        },
        {
          name: '已终止',
          checked: true,
          value: '8'
        }
      ];
    },
    handleSearchDataChange() {
      this.$emit('refresh', this.getData());
    }
  }
};
</script>

<style lang="scss" scoped>
.search-content {
  background: #fff;
  display: flex;
  padding: 12rpx 30rpx;
  align-items: center;
}
.work-icon-shaixuan,
.work-icon-xinzeng {
  margin-left: 16rpx;
}
.has-more-condition {
  color: $u-type-primary;
}
</style>
