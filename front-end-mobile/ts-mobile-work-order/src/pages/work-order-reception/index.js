export default {
  data() {
    return {
      activeMenu: 0,
      menuSearchFormList: [{}],
      tabList: [
        {
          name: '未建单',
          type: 1,
          count: 0,
          workStatus: '',
          list: []
        },
        {
          name: '待派单',
          type: 2,
          count: 0,
          workStatus: 12,
          list: []
        },
        {
          name: '处理中',
          type: 3,
          count: 0,
          workStatus: 10,
          list: []
        },
        {
          name: '已完成',
          type: 3,
          count: 0,
          workStatus: 11,
          list: []
        }
      ],
      currentTab: 1, //当前选中tab下标
      showToast: false, //标记无效来电提醒框显示控制
      markData: {}, //正在准备标记无效来电的数据
      firstLoadData: {} //首次进入页面搜索数据
    };
  },
  async onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }

    const tabName = opt.index || '',
      workStatusValue = opt.workStatusValue;
    if (workStatusValue) {
      this.firstLoadData.workStatusValue = `${workStatusValue}`;
    }
    if (opt.isWeChat) {
      this.firstLoadData.workStatusValue = '1,2,3,7';
    }

    let res = await this.ajax.getWorkSheetUserInfo();
    this.$store.state.common.userInfo.workSheetAdmin =
      res.object.webSocket || false;

    this.tabList = this.$store.state.common.userInfo.workSheetAdmin
      ? [
          {
            name: '未建单',
            type: 1,
            count: 0,
            workStatus: '',
            list: []
          },
          {
            name: '待派单',
            type: 2,
            count: 0,
            workStatus: 12,
            list: []
          },
          {
            name: '处理中',
            type: 3,
            count: 0,
            workStatus: 10,
            list: []
          },
          {
            name: '已完成',
            type: 3,
            count: 0,
            workStatus: 11,
            list: []
          }
        ]
      : [
          {
            name: '待派单',
            status: 12,
            type: 2,
            workStatus: 12,
            count: 0,
            list: []
          }
        ];
    this.currentTab = this.$store.state.common.userInfo.workSheetAdmin ? 1 : 0;

    if (tabName) {
      let newCurrent = this.tabList.findIndex(item => item.name == tabName);
      newCurrent >= 0 ? (this.currentTab = newCurrent) : null;
    }
  },
  onShow() {
    this.refresh();
  },
  filters: {
    filterTitle: function(deptName, userName, item) {
      let name = `${userName}(${item.visitPhone})`;
      return [deptName, name].filter(item => item).join('-');
    }
  },
  methods: {
    refresh(data) {
      this.tabList.forEach((item, index) => {
        this.$refs[`mescroll${index}`] &&
          this.$refs[`mescroll${index}`][0] &&
          this.$refs[`mescroll${index}`][0].downCallback();
      });
    },
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback, index) {
      let searchData = this.$refs.searchBar.getData(),
        workStatusValue = searchData.workStatus || '';

      if (this.tabList[index].name == '未建单') {
        let workStatus = '';

        this.ajax
          .getCallRecordsPageList({
            pageNo: page.num,
            pageSize: page.size,
            type: 1,
            ...searchData,
            workStatus,
            sord: 'desc',
            sidx: 'a.create_time'
          })
          .then(res => {
            successCallback(res.rows, res.totalCount, index);
            this.tabList[index].count = res.totalCount || 0;
            let audioList = res.rows
              .filter(item => item.fileUrl)
              .map(item => {
                return {
                  src: encodeURI(
                    `${
                      this.$store.state.common.baseHost
                    }/ts-basics-bottom/fileAttachment/readFile/${item.fileUrl
                      .split('/')
                      .pop()}`
                  ),
                  duration: Number(item.duration || 0),
                  key: item.pkCustometLogId
                };
              });
            page.num == 1
              ? this.$zaudio.setAudio(audioList)
              : this.$zaudio.updateAudio(audioList);
            this.$zaudio.setRender(0);
          })
          .catch(err => {
            errorCallback();
          });
      } else {
        let newWorkStatus = [];
        switch (this.tabList[index].name) {
          case '待派单':
            if (workStatusValue) {
              workStatusValue = workStatusValue.indexOf('1') >= 0 ? 1 : -1;
            }
            break;
          case '处理中':
            newWorkStatus = [];
            if (workStatusValue) {
              workStatusValue.indexOf('2') >= 0
                ? newWorkStatus.push('2')
                : null;
              workStatusValue.indexOf('3') >= 0
                ? newWorkStatus.push('3')
                : null;
              workStatusValue.indexOf('4') >= 0
                ? newWorkStatus.push('4')
                : null;
              workStatusValue.indexOf('7') >= 0
                ? newWorkStatus.push('7')
                : null;

              workStatusValue = newWorkStatus.join(',');
              workStatusValue ? null : (workStatusValue = -1);
            }
            break;
          case '已完成':
            newWorkStatus = [];
            if (workStatusValue) {
              workStatusValue.indexOf('5') >= 0
                ? newWorkStatus.push('5')
                : null;
              workStatusValue.indexOf('6') >= 0
                ? newWorkStatus.push('6')
                : null;
              workStatusValue.indexOf('8') >= 0
                ? newWorkStatus.push('8')
                : null;

              workStatusValue = newWorkStatus.join(',');
              workStatusValue ? null : (workStatusValue = -1);
            }
            break;
          default:
            break;
        }
        this.ajax
          .getWorkSheetList({
            pageNo: page.num,
            pageSize: page.size,
            ...searchData,
            workStatusValue: workStatusValue,
            workStatus: this.tabList[index].workStatus,
            sord: 'desc',
            sidx: 'a.create_time'
          })
          .then(res => {
            successCallback(res.rows, res.totalCount, index);
            this.tabList[index].count = res.totalCount || 0;
          })
          .catch(err => {
            errorCallback();
          });
      }
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      this.tabList[index]['count'] = totalCount;
    },
    datasInit(index) {
      this.tabList[index]['list'] = [];
    },
    jumpToDetail(id) {
      uni.navigateTo({
        url: `/pages/work-order-detail/index?id=${id}&fromPage=work-order-reception`
      });
    },
    handleShowToastModal(item) {
      this.markData = { ...item };
      this.showToast = true;
    },
    handleMarkUseless() {
      let data = {
        pkCustometLogId: this.markData.pkCustometLogId,
        callType: this.markData.callType,
        callWorkStatus: 2
      };

      this.ajax.markUseless(data).then(res => {
        if (res.success) {
          this.$u.toast('标记成功');
          this.$refs.mescroll0 &&
            this.$refs.mescroll0[0] &&
            this.$refs.mescroll0[0].downCallback();
        }
      });
    },
    handleToastModalCancel() {
      this.showToast = false;
      this.markData = {};
    },
    handleCreateOrder(item) {
      item.visitUserName == '未知来电' ? (item.visitUserName = '') : null;

      uni.setStorageSync(
        'orderReportConfig',
        JSON.stringify({
          isReception: true,
          formData: {
            repairPhone: item.visitPhone,
            repairManId: item.visitUserId,
            repairManName: item.visitUserName,
            repairManDeptName: item.visitUserDeptName,
            repairManDeptId: item.visitUserDeptId,
            pkCustometLogId: item.pkCustometLogId,
            callWorkStatus: 5
          }
        })
      );

      uni.navigateTo({
        url: `/pages/work-order-reporting/index?fromPage=work-order-reception`
      });
    },
    handleReCalling(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/workbench'
        });
      } else {
        this.$parentTypeFun({
          type: 'redirectTo',
          path: '/workBench'
        });
      }
    }
  }
};
