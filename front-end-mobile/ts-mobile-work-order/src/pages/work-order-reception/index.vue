<template>
  <view class="ts-container">
    <u-navbar title="服务台" title-bold :custom-back="goBack"> </u-navbar>
    <search-bar
      ref="searchBar"
      :firstLoadData="firstLoadData"
      @refresh="refresh"
    ></search-bar>
    <base-tabs-swiper
      ref="tabSwiper"
      class="tab-swiper-box"
      :list="tabList"
      badgeType="text"
      :font-size="28"
      :current="currentTab"
      :is-scroll="false"
      @change="changeTab"
    ></base-tabs-swiper>

    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="onTabChange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <mescroll
          :ref="`mescroll${index}`"
          :mescrollIndex="index"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <template v-if="item.name == '未建单'">
            <view
              v-for="i in item.list"
              :key="i.pkCustometLogId"
              class="no-create-order-item"
            >
              <view class="item-title">
                <view @click="handleReCalling(i.visitPhone)">
                  <img :src="require('@/assets/img/phone.png')" />
                  {{ i.visitUserDeptName | filterTitle(i.visitUserName, i) }}
                </view>
                <view class="no-create-item-toast" v-if="i.remark == '未接听'">
                  未接听
                </view>
              </view>
              <view class="visit-time">{{ i.visitTime }}</view>
              <view class="action-box">
                <view class="audio-content">
                  <zaudio
                    v-if="i.fileUrl"
                    :keyValue="i.pkCustometLogId"
                    theme="theme4"
                    themeColor="#999"
                  ></zaudio>
                </view>
              </view>
              <view class="no-create-action">
                <view @tap="handleShowToastModal(i)">标记无效来电</view>
                <view @tap="handleCreateOrder(i)">创建工单</view>
              </view>
            </view>
          </template>

          <template v-else>
            <my-work-order-item
              v-for="i in item.list"
              :key="i.workNumber"
              :item="i"
              :isReception="true"
              @click="jumpToDetail(i.workNumber)"
            >
              <template #bottomRight="i">
                <view>
                  <view
                    class="item-operation-button edit-button"
                    v-if="
                      i.workStatus == 1 &&
                        $store.state.common.userInfo.empId == i.repairManId
                    "
                    @click.stop="editWorkOrder(i.workNumber, i.pkWsTaskId)"
                  >
                    编辑
                  </view>
                  <view
                    class="item-operation-button acceptance-button"
                    v-if="
                      i.workStatus == 4 &&
                        $store.state.common.userInfo.empId == i.repairManId
                    "
                    @click.stop="
                      acceptanceWorkOrder(i.workNumber, i.pkWsTaskId)
                    "
                  >
                    验收
                  </view>
                  <view
                    class="item-operation-button evaluate-button"
                    v-if="
                      i.workStatus == 5 &&
                        $store.state.common.userInfo.empId == item.repairManId
                    "
                    @click.stop="evaluateWorkOrder(i.workNumber, i.pkWsTaskId)"
                  >
                    评价
                  </view>
                </view>
              </template>
            </my-work-order-item>
          </template>
        </mescroll>
      </swiper-item>
    </swiper>

    <u-modal
      v-model="showToast"
      content="确定标记为无效来电？"
      confirmText="确定"
      :show-cancel-button="true"
      @confirm="handleMarkUseless"
      @cancel="handleToastModalCancel"
    >
    </u-modal>
  </view>
</template>

<script>
import index from './index';
import searchBar from './components/seaerch-bar.vue';
import baseTabsSwiper from '@/components/base-tabs-swiper/base-tabs-swiper.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import myWorkOrderItem from '../my-work-order-list/components/my-work-order-item.vue';
import Zaudio from '@/components/uniapp-zaudio/zaudio.vue';

export default {
  name: 'work-order-reception',
  mixins: [index],
  components: {
    searchBar,
    baseTabsSwiper,
    mescroll,
    myWorkOrderItem,
    Zaudio
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.tab-swiper-box {
  margin-bottom: 16rpx;
}
.swiper-box {
  flex: 1;
}
.no-create-order-item {
  position: relative;
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
  min-height: 140rpx;

  .item-title {
    @include vue-flex;
    justify-content: space-between;
    margin-bottom: 8rpx;
    uni-view {
      font-weight: 600;
      line-height: 44rpx;
      font-size: 28rpx;
      &.no-create-item-toast {
        flex-shrink: 0;
        color: #f93534;
      }
      &:first-child {
        img {
          height: 32rpx;
          width: 32rpx;
          margin-right: 8rpx;
        }
        display: flex;
        align-items: center;
        color: $u-main-color;
      }
    }
  }

  .visit-time {
    color: $u-content-color;
    line-height: 36rpx;
    font-size: 24rpx;
    margin-bottom: 16rpx;
  }
  .action-box {
    @include vue-flex;
    justify-content: space-between;
  }

  .audio-content {
    min-width: 360rpx;
  }

  .no-create-action {
    position: absolute;
    bottom: 16rpx;
    right: 32rpx;
    display: flex;
    align-items: center;
    border-radius: 48rpx;
    height: 48rpx;
    overflow: hidden;
    line-height: 0;

    uni-view {
      display: flex;
      align-items: center;
      padding: 0 16rpx;
      font-size: 24rpx;
      color: #ffffff;
      height: 48rpx;
      background: #3465ef;
      &:first-child {
        background: #fe9800;
      }
    }
  }
}
/deep/ {
  .uni-slider-wrapper {
    min-height: 4rpx;
  }
  .uni-slider-tap-area {
    padding: 4rpx 0;
  }
}
</style>
