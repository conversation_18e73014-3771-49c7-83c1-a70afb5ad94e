<template>
  <view class="person-list-item-radio-group">
    <view
      class="person-list-item-radio"
      v-for="item in list"
      :key="item[key]"
      @click="toggle(item)"
    >
      <view class="radio__icon-wrap" :class="item.checked | iconClass">
        <u-icon
          class="radio__icon-wrap__icon"
          name="checkbox-mark"
          :size="iconSize"
          :color="item.checked | iconColor"
        />
      </view>
      <person-list-item-info
        class="radio__info-wrap"
        :person="item"
        :personProp="infoProp"
      ></person-list-item-info>
    </view>
  </view>
</template>

<script>
import personListItemInfo from './person-list-item-info.vue';
export default {
  name: 'person-list-item-radio',
  components: {
    personListItemInfo
  },
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    value: {
      type: String,
      default: ''
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    iconColor: {
      type: String,
      default: ''
    },
    infoProp: {
      type: Object
    }
  },
  filters: {
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'radio__icon-wrap--checked' : '';
    }
  },
  computed: {
    key: function() {
      return (this.infoProp && this.infoProp.key) || 'userId';
    }
  },
  methods: {
    toggle(e) {
      let values = [];
      if (e.checked) {
        e.checked = !e.checked;
      } else {
        this.list.map(i => {
          if (i[this.key] == e[this.key]) {
            i.checked = true;
            values.push(i);
          } else {
            i.checked = false;
          }
        });
      }
      this.$emit('change', values);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/flex.scss';
.person-list-item-radio {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  background-color: #ffffff;
  @include vue-flex;
  align-items: center;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
.radio__icon-wrap {
  color: $uni-text-content-color;
  @include vue-flex;
  flex: none;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 38rpx;
  height: 38rpx;
  color: transparent;
  text-align: center;
  transition-property: color, border-color, background-color;
  font-size: $uni-icon-size-base;
  border: 1px solid $uni-text-color-disable;
  border-radius: 100%;
  transition-duration: 0.2s;
}
.radio__icon-wrap--checked {
  border-color: $u-type-primary;
  background-color: $u-type-primary;
}
.radio__info-wrap {
  flex: 1;
  margin-left: $uni-spacing-row-base;
}
</style>
