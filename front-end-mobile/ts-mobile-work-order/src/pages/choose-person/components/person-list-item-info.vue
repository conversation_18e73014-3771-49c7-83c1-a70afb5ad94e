<template>
  <view class="person-list-item-info">
    <view class="left">
      <image
        class="person-head-image"
        v-if="person[personProp.headImg]"
        :src="person[personProp.headImg] | headImgFilter"
        mode="aspectFill"
      ></image>
      <view
        v-else
        class="person-head-image"
        :class="person[personProp.name] | sexClassFilter"
      >
        {{ person[personProp.name] | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view
        v-if="personProp.nameFormatt"
        class="person-name"
        v-html="formatt('nameFormatt', 'nameConcatSymbol')"
      >
      </view>
      <view v-else class="person-name">{{ person[personProp.name] }}</view>
      <view
        v-if="personProp.describeFormatt"
        class="person-description"
        v-html="formatt('describeFormatt', 'describeConcatSymbol')"
      >
      </view>
      <view v-else class="person-description">
        {{ person[personProp.describe] }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'person-list-item-info',
  props: {
    person: {
      type: Object,
      default() {
        return {};
      }
    },
    personProp: {
      type: Object,
      default: () => {
        return {
          name: 'name',
          describe: 'deptName',
          describeConcatSymbol: '-',
          key: 'userId',
          sex: 'sex',
          headImg: 'headImg'
        };
      }
    }
  },
  methods: {
    formatt(typeName, concatName) {
      let describeType = Object.prototype.toString.call(
        this.personProp[typeName]
      );
      if (describeType == '[object String]') {
        return this.person[this.personProp[typeName]];
      } else if (describeType == '[object Array]') {
        let content = [];
        this.personProp[typeName].forEach(item => {
          if (this.person[item]) {
            content.push(this.person[item]);
          }
        });
        return content.join(this.personProp[concatName] || '-');
      } else if (describeType == '[object Function]') {
        return this.personProp[typeName](this.person);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
.person-list-item-info {
  display: flex;
}
.right {
  flex: 1;
  padding-left: $uni-spacing-row-base;
}
.person-head-image {
  width: $uni-img-size-lg;
  height: $uni-img-size-lg;
  border-radius: 50%;
  background-color: $u-bg-color;
  text-align: center;
  line-height: $uni-img-size-lg;
  font-size: $uni-font-size-base;
  color: $uni-text-color-inverse;
}
.sex-man {
  background-color: $sexman-color;
}
.sex-woman {
  background-color: $sexwoman-color;
}
.person-name {
  font-size: $uni-font-size-base;
  color: $u-main-color;
}
.person-description {
  color: $u-content-color;
  font-size: $uni-font-size-sm;
  @include ellipsis;
}
</style>
