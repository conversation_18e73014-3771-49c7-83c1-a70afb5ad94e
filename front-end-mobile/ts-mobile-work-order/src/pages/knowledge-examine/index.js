export default {
  data() {
    return {
      formList: [
        {
          title: '审核结果',
          prop: 'knowledgeStatus',
          type: 'radio',
          required: true,
          radioCheckWrap: false,
          radioList: [
            {
              label: '通过',
              value: 2
            },
            {
              label: '不通过',
              value: -1
            }
          ],
          callback: value => {
            if (value == 2) {
              this.$set(this.form, 'backReason', '');
              this.formList = this.formList.filter(
                item => item.prop != 'backReason'
              );
              this.rules = {};
            } else {
              this.formList.push(this.backReason.file);
              this.rules.backReason = this.backReason.rule;
            }
          }
        }
      ],
      form: {
        pkKnowledgeBaseId: '',
        knowledgeStatus: 2,
        fkKnowledgeType: '',
        fkKnowledgeTypeId: '',
        recommendedWorkHours: '',
        knowledgeTitle: '',
        knowledgeContent: '',
        backReason: ''
      },
      rules: {},
      backReason: {
        file: {
          title: '不通过说明',
          prop: 'backReason',
          type: 'textarea',
          maxlength: 100,
          placeholder: '请输入不通过说明',
          required: true
        },
        rule: [
          {
            required: true,
            message: '请输入不通过说明',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.form.pkKnowledgeBaseId = opt.id;
    this.getKnowledgeInfo(opt.id);
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    getKnowledgeInfo(id) {
      this.ajax.getKnowledgeBaseInfo(id).then(res => {
        this.form.knowledgeContent = res.object.knowledgeContent;
        this.form.fkKnowledgeTypeId = res.object.fkKnowledgeTypeId;
        this.form.recommendedWorkHours = res.object.recommendedWorkHours;
        this.form.knowledgeTitle = res.object.knowledgeTitle;
      });
    },
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.addKnowledge(this.form).then(res => {
        uni.redirectTo({
          url: '/pages/knowledge-management-list/index'
        });
      });
    }
  }
};
