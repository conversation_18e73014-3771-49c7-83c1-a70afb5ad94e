export default {
  data() {
    return {
      formList: [
        {
          title: '是否完成',
          prop: 'yesOrNo',
          type: 'radio',
          placeholder: '请备注说明',
          required: true,
          radioCheckWrap: false,
          radioList: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ]
        },
        {
          title: '本次处理工时',
          prop: 'workHours',
          type: 'number',
          maxlength: 5,
          placeholder: '请输入本次处理工时',
          callback: value => {
            return value.toString().match(/^\d*(\.?\d{0,1})/g)[0];
          }
        },
        {
          title: '本次处理说明',
          prop: 'remark',
          type: 'textarea',
          maxlength: 100,
          placeholder: '请输入本次处理说明',
          required: true
        },
        {
          title: '上传附件',
          prop: 'files',
          propVal: 'oldfiles',
          type: 'file',
          name: 'file',
          header: {
            token: this.$store.state.common.token
          },
          action: `${this.$store.state.common.baseHost}/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk`,
          placeholder: '上传附件',
          required: false
        }
      ],
      form: {
        fkUserId: '',
        pkWsTaskId: '',
        remark: '已完成',
        workHours: '',
        yesOrNo: '1',
        files: []
      },
      rules: {
        remark: [
          {
            required: true,
            message: '请输入本次处理说明',
            trigger: ''
          }
        ]
      },
      workHours: 0
    };
  },
  onLoad(opt) {
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkUserId = opt.fkUserId;
    this.getWorkOrderDetail(opt.id);
    this.$nextTick(() => {
      //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
      this.$refs.baseForm.$refs.uForm.setRules(this.rules);
    });
  },
  methods: {
    getWorkOrderDetail(id) {
      this.ajax.getWorkOrderDetail(id).then(res => {
        this.workHours = res.object.wsWsSheetInfoOutVo.workHours;
      });
    },
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.workSheetProcessingComplete(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/work-order-handle-list/index'
        });
      });
    }
  }
};
