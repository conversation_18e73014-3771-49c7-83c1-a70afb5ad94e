export default {
  data() {
    return {
      formList: [
        {
          title: '验收确认',
          prop: 'yesOrNo',
          type: 'radio',
          required: true,
          radioCheckWrap: false,
          radioList: [
            {
              label: '已解决',
              value: 1
            },
            {
              label: '未解决',
              value: 0
            }
          ],
          callback: value => {
            if (value == 1) {
              this.$set(this.form, 'remark', '');
              this.formList = this.formList.filter(
                item => item.prop != 'remark'
              );
              this.rules = {};
            } else {
              this.formList.push(this.remark.file);
              this.rules.remark = this.remark.rule;
            }
          }
        }
      ],
      form: {
        pkWsTaskId: '',
        remark: '',
        yesOrNo: '1',
        fkUserId: ''
      },
      rules: {},
      remark: {
        file: {
          title: '不通过说明',
          prop: 'remark',
          type: 'textarea',
          maxlength: 100,
          placeholder: '将退回至处理人继续处理',
          required: true
        },
        rule: [
          {
            required: true,
            message: '请输入不通过说明',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad(opt) {
    this.form.pkWsTaskId = opt.pkWsTaskId;
    this.form.fkUserId = opt.fkUserId;
  },
  onReady() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.baseForm.$refs.uForm.setRules(this.rules);
  },
  methods: {
    onClick() {
      this.$refs.baseForm.submit();
    },
    submit() {
      this.ajax.workSheetAcceptance(this.form).then(res => {
        uni.reLaunch({
          url: '/pages/my-work-order-list/index?tabIndex=1'
        });
      });
    }
  }
};
