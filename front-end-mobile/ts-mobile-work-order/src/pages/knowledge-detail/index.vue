<template>
  <view
    class="ts-container safe-area-inset-bottom"
    v-if="JSON.stringify(knowledgeInfo) != '{}'"
  >
    <u-navbar title="知识内容" title-bold :custom-back="goBack"></u-navbar>
    <view class="knowledge-content-box">
      <view class="knowledge-content-top">
        <view class="knowledge-item-title-box">
          <view class="knowledge-title">
            {{ knowledgeInfo.knowledgeTitle }}
          </view>
          <view
            class="knowledge-item-time"
            v-if="
              knowledgeInfo.knowledgeStatus == 0 ||
                knowledgeInfo.knowledgeStatus == 1
            "
          >
            {{ `${knowledgeInfo.contributionTime.substring(0, 16)}` }}
          </view>
          <view
            class="knowledge-item-time"
            v-else-if="
              knowledgeInfo.knowledgeStatus == -1 ||
                knowledgeInfo.knowledgeStatus == 3 ||
                knowledgeInfo.knowledgeStatus == 2
            "
          >
            {{ `${knowledgeInfo.reviewTime.substring(0, 16)}` }}
          </view>
        </view>
        <view class="knowledge-info">
          {{ `类型：${knowledgeInfo.fkKnowledgeTypeName}` }}
        </view>
        <view class="knowledge-info-box">
          <view class="knowledge-info">
            {{ `工时：${knowledgeInfo.recommendedWorkHours}H` }}
          </view>
          <view class="knowledge-info" style="flex: 1">
            {{ `贡献人：${knowledgeInfo.fkUserName}` }}
          </view>
          <view class="knowledge-info">
            <u-icon name="thumb-up" size="32" color="#666666"></u-icon>
            <text class="icon-text">{{ knowledgeInfo.usefulNumbers }}</text>
          </view>
        </view>
      </view>
      <view class="knowledge-content ql-container">
        <u-parse
          class="ql-editor"
          :html="knowledgeInfo.knowledgeContent"
          lazy-load
        ></u-parse>
      </view>
    </view>
    <view class="knowledge-operation-box">
      <view
        class="operation-button-item"
        :style="{
          width: buttonWidth,
          color: item.activeStatus ? '#fa3534' : '#999999'
        }"
        v-for="(item, index) in showButtons"
        :key="index"
        @click="clickButton(item)"
      >
        <u-icon
          v-if="item.icon"
          :name="item.icon"
          custom-prefix="work-icon"
          :color="item.activeStatus ? '#fa3534' : '#999999'"
          size="36"
        ></u-icon>
        {{ item.activeStatus ? '已赞' : item.name }}
      </view>
    </view>
    <u-modal
      ref="uModal"
      v-model="modalShow"
      :show-cancel-button="true"
      :title="modalTitle"
      :async-close="modalAsyncClose"
      :confirm-text="modalConfirmText"
      :confirm-color="modalConfirmColor"
      :content="modalContent"
      @confirm="modalConfirm"
    >
    </u-modal>
  </view>
</template>

<script>
import index from './index.js';
export default {
  name: 'knowledge-detail',
  mixins: [index]
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/ellipsis.scss';
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  position: relative;
  @include vue-flex(column);
}
.knowledge-content-box {
  margin-top: $uni-spacing-col-lg;
  background-color: #ffffff;
  padding: 0 $uni-spacing-row-lg;
  flex: 1;
  overflow: auto;
}
.knowledge-content-top {
  padding: $uni-spacing-row-base 0;
  margin-bottom: $uni-spacing-col-base;
  border-bottom: 1px solid #eee;
}
.knowledge-item-title-box {
  @include vue-flex;
  align-items: center;
}
.knowledge-item-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
}
.knowledge-title {
  flex: 1;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  @include multiLineEllipsis;
  font-weight: bold;
}
.knowledge-info-box {
  @include vue-flex;
  align-items: flex-end;
}
.knowledge-info {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
  margin-right: 30rpx;
  &:last-child {
    margin: 0;
  }
}
.knowledge-icon-box {
  @include vue-flex;
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
}
.icon-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
  margin: 0 $uni-spacing-row-lg 0 $uni-spacing-row-base;
}
.knowledge-content {
  height: auto;
}
.knowledge-operation-box {
  @include vue-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ccc;
}
.operation-button-item {
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  // padding: 0 $uni-spacing-row-base;
  height: 44px;
  line-height: 44px;
}
</style>
