export default {
  data() {
    return {
      fromPage: '',
      knowledgeInfo: {},
      operationButtons: {
        like: {
          name: '点赞',
          type: 'knowledgeLike',
          icon: 'dianzan',
          activeStatus: true,
          isShow: false
        },
        forward: {
          name: '转发',
          type: 'knowledgeForward',
          isShow: false
        },
        remove: {
          name: '移除',
          type: 'knowledgeModalRemove',
          isShow: false
        },
        examine: {
          name: '审核',
          page: 'knowledge-examine',
          isShow: false
        },
        withdraw: {
          name: '撤回',
          page: 'knowledge-withdraw',
          isShow: false
        },
        reEdit: {
          name: '重新提交',
          page: 'add-work-order-knowledge',
          isShow: false
        },
        moveIn: {
          name: '移入',
          type: 'knowledgeModalMoveIn',
          isShow: false
        },
        submit: {
          name: '提交',
          type: 'knowledgeSubmit',
          isShow: false
        },
        delete: {
          name: '删除',
          type: 'knowledgeModalDelete',
          isShow: false
        }
      },
      showButtons: [],
      buttonWidth: '',
      modalTitle: '提示',
      modalShow: false,
      modalShowTitle: true,
      modalAsyncClose: true,
      modalConfirmText: '确定',
      modalConfirmColor: '#005bac',
      modalContent: '',
      modalConfirmFun: ''
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    this.getKnowledgeInfo(opt.id);
  },
  watch: {
    'showButtons.length': {
      handler(newVal) {
        if (newVal > 0) {
          let obj = uni
            .createSelectorQuery()
            .select('.knowledge-operation-box');
          obj
            .boundingClientRect(data => {
              let boxWidth = data.width;
              this.buttonWidth = `${boxWidth / newVal}px`;
            })
            .exec();
        }
      },
      deep: true
    }
  },
  methods: {
    getKnowledgeInfo(id) {
      this.ajax.getKnowledgeBaseInfo(id).then(res => {
        this.setOperationButtons(
          res.object.knowledgeStatus,
          res.object.peopleType,
          res.object.fkUserId,
          res.object.isLike
        );
        this.knowledgeInfo = res.object;
        this.knowledgeInfo.id = id;
        this.$nextTick(() => {
          Object.keys(this.operationButtons).forEach(item => {
            if (this.operationButtons[item]['isShow']) {
              this.showButtons.push(this.operationButtons[item]);
            }
          });
        });
      });
    },
    setOperationButtons(status, peopleType, fkUserId, isLike) {
      if (this.fromPage == 'knowledge-management-list') {
        //审核通过
        if (status == 2 && peopleType == 1) {
          // this.$set(this.operationButtons['forward'], 'isShow', true);
          this.$set(this.operationButtons['remove'], 'isShow', true);
        }
        //审核中
        if (status == 1 && peopleType == 1) {
          this.$set(this.operationButtons['examine'], 'isShow', true);
        }
        if (
          status == 1 &&
          fkUserId == this.$store.state.common.userInfo.empId
        ) {
          this.$set(this.operationButtons['withdraw'], 'isShow', true);
        }
        //未通过
        if (
          status == -1 &&
          fkUserId == this.$store.state.common.userInfo.empId
        ) {
          this.$set(this.operationButtons['reEdit'], 'isShow', true);
        }
        //已移除
        if (status == 3 && peopleType == 1) {
          this.$set(this.operationButtons['moveIn'], 'isShow', true);
        }
        //草稿箱
        if (
          status == 0 &&
          fkUserId == this.$store.state.common.userInfo.empId
        ) {
          this.$set(this.operationButtons['submit'], 'isShow', true);
          this.$set(this.operationButtons['delete'], 'isShow', true);
        }
      } else {
        this.$set(this.operationButtons['like'], 'isShow', true);
        if (isLike == 1) {
          this.$set(this.operationButtons['like'], 'activeStatus', true);
          this.$set(
            this.operationButtons['like'],
            'type',
            'knowledgeCancelLike'
          );
        } else {
          this.$set(this.operationButtons['like'], 'activeStatus', false);
          this.$set(this.operationButtons['like'], 'type', 'knowledgeLike');
        }
      }
    },
    clickButton(item) {
      if (item.page) {
        this.jumpPage(item.page);
      } else {
        this[item.type]();
      }
    },
    jumpPage(page) {
      uni.navigateTo({
        url: `/pages/${page}/index?id=${this.knowledgeInfo.id}`
      });
    },
    modalConfirm() {
      this.modalConfirmFun();
    },
    knowledgeModalRemove() {
      this.modalContent = '确定要移除知识点吗？';
      this.modalShow = true;
      this.modalConfirmFun = this.knowledgeRemove;
    },
    knowledgeRemove() {
      this.ajax
        .removeKnowledge({
          knowledgeStatus: 5,
          pkKnowledgeBaseId: this.knowledgeInfo.id
        })
        .then(res => {
          uni.navigateTo({
            url: '/pages/knowledge-management-list/index'
          });
        });
    },
    knowledgeModalMoveIn() {
      this.modalContent = '确定要移入知识库吗？';
      this.modalShow = true;
      this.modalConfirmFun = this.knowledgeMoveIn;
    },
    knowledgeMoveIn() {
      this.ajax
        .moveInKnowledge({
          knowledgeStatus: 1,
          pkKnowledgeBaseId: this.knowledgeInfo.id
        })
        .then(res => {
          uni.redirectTo({
            url: '/pages/knowledge-management-list/index'
          });
        });
    },
    knowledgeSubmit() {
      this.ajax
        .submitKnowledge({
          knowledgeStatus: 6,
          pkKnowledgeBaseId: this.knowledgeInfo.id
        })
        .then(res => {
          uni.redirectTo({
            url: '/pages/knowledge-management-list/index'
          });
        });
    },
    knowledgeModalDelete() {
      this.modalContent = '删除后不可恢复，确定要删除知识点吗？';
      this.modalShow = true;
      this.modalConfirmFun = this.knowledgeDelete;
    },
    knowledgeDelete() {
      this.ajax
        .deleteKnowledge({
          knowledgeStatus: 7,
          pkKnowledgeBaseId: this.knowledgeInfo.id
        })
        .then(res => {
          uni.redirectTo({
            url: '/pages/knowledge-management-list/index'
          });
        });
    },
    knowledgeLike() {
      this.ajax.likeKnowledge(this.knowledgeInfo.id).then(() => {
        this.knowledgeInfo.usefulNumbers += 1;
        this.$set(this.operationButtons['like'], 'activeStatus', true);
        this.$set(this.operationButtons['like'], 'name', '已赞');
        this.$set(this.operationButtons['like'], 'type', 'knowledgeCancelLike');
      });
    },
    knowledgeCancelLike() {
      this.ajax.cancelLikeKnowledge(this.knowledgeInfo.id).then(() => {
        this.knowledgeInfo.usefulNumbers -= 1;
        this.$set(this.operationButtons['like'], 'activeStatus', false);
        this.$set(this.operationButtons['like'], 'name', '点赞');
        this.$set(this.operationButtons['like'], 'type', 'knowledgeLike');
      });
    },
    goBack() {
      uni.redirectTo({
        url: `/pages/${this.fromPage}/index`
      });
    }
  }
};
