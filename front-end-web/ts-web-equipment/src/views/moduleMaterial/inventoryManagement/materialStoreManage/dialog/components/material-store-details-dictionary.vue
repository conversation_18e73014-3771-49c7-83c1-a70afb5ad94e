<template>
  <div class="material-store-details-dictionary">
    <table class="custom-form-table">
      <thead class="custom-form-table__header">
        <tr class="custom-form-table__header-row">
          <td
            class="custom-form-table__header-cell"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            v-for="item in materialDictionaryColumns"
            :key="item.prop"
          >
            <template>
              <span v-if="item.requiredIcon" class="required-icon">*</span>
              <span class="custom-form-table__header-cell-label">
                {{ item.label }}
              </span>
            </template>
          </td>
        </tr>
      </thead>
      <tbody class="custom-form-table__body">
        <tr
          class="custom-form-table__body-row"
          v-for="(item, index) in form.inbDtlList"
          :key="item.id"
          @click="handleClickRow(item)"
        >
          <td
            class="custom-form-table__body-cell"
            v-for="column in materialDictionaryColumns"
            :key="column.prop"
          >
            <template v-if="column.render === 'index'">
              <span>{{ index + 1 }}</span>
            </template>

            <template v-if="column.render === 'checkbox'">
              <input type="checkbox" v-model="item[column.prop]" />
            </template>

            <template v-if="column.render === 'text'">
              <span>{{ item[column.prop] }}</span>
            </template>

            <template v-if="column.render === 'input'">
              <comp-input
                v-model="item[column.prop]"
                :placeholder="column.placeholder"
                :maxlength="column.maxlength"
              />
            </template>

            <template v-if="column.render === 'date'">
              <comp-date
                v-model="item[column.prop]"
                :disabledDate="column.disabledDate"
              />
            </template>

            <template v-if="column.render === 'auto-height-input'">
              <comp-auto-height-input
                class="auto-height-input-container"
                :ref="`autoHeightInput-${index}`"
                v-model="item[column.prop]"
                :maxlength="column.maxlength"
                :placeholder="column.placeholder"
              />
            </template>

            <template v-else>
              <template v-if="column.prop === 'num'">
                <comp-integer-input
                  class="auto-input-styles"
                  v-model="item.num"
                  :prop="`inbDtlList.${index}.num`"
                  :rules="rules.required"
                  @blur="() => handleBlurComputedTotal(item)"
                />
              </template>

              <template v-if="column.prop === 'price'">
                <comp-decimal-input
                  class="auto-input-styles"
                  v-model="item.price"
                  :prop="`inbDtlList.${index}.price`"
                  :rules="rules.required"
                  @blur="() => handleBlurComputedTotal(item)"
                />
              </template>

              <template v-if="column.prop === 'totalAmt'">
                <span class="total-amount-item">
                  {{ numToLocaleStrFixed2(item.totalAmt) }}
                </span>
              </template>
            </template>
          </td>
        </tr>

        <tr class="total-amount" v-if="form.inbDtlList.length">
          <td
            class="footer-total-amount-item"
            v-for="(item, index) in materialDictionaryColumns"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            :key="item.prop"
          >
            <template v-if="index === 0">
              合计
            </template>

            <template v-if="item.prop === 'num'">
              {{ totalNum }}
            </template>

            <template v-if="item.prop === 'totalAmt'">
              {{ totalAmount }}
            </template>
          </td>
        </tr>
        <tr v-else>
          <td :colspan="materialDictionaryColumns.length" class="empty-row">
            <span>暂无数据</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import moment from 'moment';
import { Decimal } from 'decimal.js';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
import {
  baseMaterialDictionaryColumns,
  baseMaterialDictionaryColumns1
} from '@/views/moduleMaterial/inventoryManagement/config/dic.js';
import CompIntegerInput from '@/components/busi-form-table-components/comp-integer-input.vue';
import CompDecimalInput from '@/components/busi-form-table-components/comp-decimal-input.vue';
import CompInput from '@/components/busi-form-table-components/comp-input.vue';
import CompDate from '@/components/busi-form-table-components/comp-date.vue';
import CompAutoHeightInput from '@/components/busi-form-table-components/comp-auto-height-input.vue';

export default {
  name: 'MaterialStoreDetailsDictionary',
  components: {
    CompIntegerInput,
    CompDecimalInput,
    CompInput,
    CompDate,
    CompAutoHeightInput
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    },
    currencyTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      numToLocaleStrFixed2,
      materialDictionaryColumns: [
        ...baseMaterialDictionaryColumns,
        {
          prop: 'num',
          label: '数量',
          align: 'right',
          width: 75,
          requiredIcon: true
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'center',
          width: 105,
          requiredIcon: true
        },
        {
          prop: 'totalAmt',
          label: '金额(元)',
          align: 'right',
          width: 120
        },
        {
          prop: 'prodNo',
          label: '生产批号',
          align: 'center',
          render: 'input',
          width: 155,
          maxlength: 20,
          placeholder: '请输入生产批号'
        },
        {
          prop: 'prodDate',
          label: '生产日期',
          align: 'center',
          render: 'date',
          disabledDate: current => {
            return current && current > moment();
          },
          width: 145
        },
        {
          prop: 'expireDate',
          label: '失效日期',
          align: 'center',
          render: 'date',
          disabledDate: current => {
            return current && current < moment().subtract(1, 'day');
          },
          width: 145
        },
        ...baseMaterialDictionaryColumns1
      ]
    };
  },
  computed: {
    totalNum() {
      const totalNum = this.form.inbDtlList.reduce(
        (sum, item) => sum.plus(item.num || 0),
        new Decimal(0)
      );

      return totalNum;
    },
    totalAmount() {
      const totalAmt = this.form.inbDtlList.reduce(
        (sum, item) => sum.plus(item.totalAmt || 0),
        new Decimal(0)
      );
      return numToLocaleStrFixed2(totalAmt);
    }
  },
  methods: {
    getSelection() {
      return this.form.inbDtlList
        .filter(item => item.isCheck)
        .map(item => item.id);
    },

    // 回显自动高度输入框
    echoAutoInput() {
      for (const key in this.$refs) {
        if (key.includes('autoHeightInput')) {
          let { autoHeightInput } = this.$refs[key][0]?.$refs || {};
          autoHeightInput?.handleEcho?.();
          setTimeout(autoHeightInput?.adjustHeight, 100);
        }
      }
    },

    // 计算总金额
    handleBlurComputedTotal(row) {
      const num = parseFloat(row.num);
      const price = parseFloat(row.price);

      if (isNaN(num) || isNaN(price)) {
        row.totalAmt = 0;
        return;
      }

      this.$set(row, 'num', num);
      this.$set(row, 'price', price);
      let totalAmt = Decimal.mul(price, num).toNumber();
      this.$set(row, 'totalAmt', totalAmt);
    },

    handleClickRow(item) {
      item.isCheck = !item.isCheck;
    }
  }
};
</script>

<style lang="scss" scoped>
.material-store-details-dictionary {
  overflow-x: auto;

  ::v-deep {
    .auto-input-styles {
      width: 100%;
      .ts-input {
        min-width: 100% !important;
      }
    }

    .total-amount-item {
      width: 100%;
      display: inline-block;
      text-align: right !important;
    }

    .custom-form-table__body-cell {
      .auto-height-input-container {
        .el-form-item__content {
          line-height: 10px !important;
        }
      }
    }
  }

  .total-amount {
    height: 32px;
    border: 1px solid rgba(235, 238, 245, 1);
    border-top: none;
    background-color: #f3f6f9;

    .footer-total-amount-item {
      text-align: right;
      font-weight: bold;
      color: $warning-color;
    }
  }

  .delete-device {
    color: red;
    cursor: pointer;
  }

  .empty-row {
    text-align: center;
    color: #999;
    height: 50px;
  }
}
</style>
