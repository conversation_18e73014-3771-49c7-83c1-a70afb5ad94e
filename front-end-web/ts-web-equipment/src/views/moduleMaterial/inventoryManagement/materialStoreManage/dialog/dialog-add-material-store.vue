<template>
  <vxe-modal
    className="dialog-add-material-store"
    :title="title"
    v-model="visible"
    width="90%"
    height="85%"
    :before-hide-method="beforeHideMethod"
  >
    <template #default>
      <div class="content-container" v-loading="loading">
        <operate-dialog-btns
          :stat="form.inb?.stat"
          @btnOperate="handleBtnOperate"
          type="1"
        />

        <!-- 单据表单主体 -->
        <ts-form ref="form" :model="form" v-if="isAdd || isEdit">
          <div class="form-group-tips">
            <span>单据信息</span>
          </div>

          <material-store-basic-form
            ref="materialStoreBasicForm"
            :form="form"
            :rules="rules"
            :methodOptions="methodOptions"
          />

          <div class="form-group-tips">
            <span>入库物资明细</span>

            <div>
              <ts-button
                class="shallowButton"
                type="primary"
                @click="handleAddMaterialDictionary"
              >
                添加物资
              </ts-button>
              <ts-button type="danger" @click="handleRemoveMaterialDictionary">
                删除
              </ts-button>
            </div>
          </div>

          <material-store-details-dictionary
            ref="materialStoreDetailsDictionary"
            :form="form"
            :rules="rules"
          />
        </ts-form>

        <!-- 单据详情 -->
        <dialog-details-material-store
          v-if="isDetails"
          ref="dialogDetailsMaterialStore"
          :form="form"
        />

        <!-- 添加物资明细 -->
        <dialog-multiple-material-dictionary
          renderType="1"
          ref="dialogMultipleMaterialDictionary"
          @submit="handleSubmitMaterialDictionary"
        />

        <!-- 打印单据 -->
        <dialog-material-print renderType="1" ref="dialogMaterialPrint" />
      </div>
    </template>
  </vxe-modal>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import cloneDeep from 'lodash-es/cloneDeep';

import OperateDialogBtns from '@/views/moduleMaterial/inventoryManagement/components/operate-dialog-btns.vue';

import DialogDetailsMaterialStore from './dialog-details-material-store.vue';
import MaterialStoreBasicForm from './components/material-store-basic-form.vue';
import MaterialStoreDetailsDictionary from './components/material-store-details-dictionary.vue';

import DialogMultipleMaterialDictionary from '@/views/moduleMaterial/inventoryManagement/components/dialog-multiple-material-dictionary.vue';
import DialogMaterialPrint from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print.vue';
export default {
  components: {
    MaterialStoreBasicForm,
    MaterialStoreDetailsDictionary,
    DialogDetailsMaterialStore,
    DialogMultipleMaterialDictionary,
    OperateDialogBtns,
    DialogMaterialPrint
  },

  props: {
    methodOptions: {
      type: Array,
      default: () => []
    },
    whId: {
      type: String,
      default: ''
    },
    handleGetReceiptDetailApi: {
      type: Function,
      default: () => {}
    }
  },

  data() {
    return {
      visible: false,
      loading: false,

      whName: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },

  computed: {
    title() {
      return `【${this.whName}】【 ${this.form.inb?.flowNo || ''}】入库单`;
    },

    isAdd() {
      return this.form.inb?.stat == '0';
    },

    isEdit() {
      return this.form.inb?.stat == '1';
    },

    isDetails() {
      return this.form.inb?.stat == '2';
    }
  },

  methods: {
    async show({ data = {}, whName = '' }) {
      this.whName = whName;
      if (JSON.stringify(data) !== '{}') {
        this.$set(this, 'form', cloneDeep(data));
      } else {
        this.$set(this, 'form', this.initForm());
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        // 编辑单据 回显autoInput
        if (this.isEdit) {
          this.$refs.materialStoreDetailsDictionary.echoAutoInput();
        }
        // 详情单据 回显表单
        if (this.isDetails) {
          this.$refs.dialogDetailsMaterialStore.setTableData();
        }
      });
    },

    initForm() {
      let parentStoreInfo = this.$getParentStoreInfo();
      let userInfo = parentStoreInfo?.userInfo || {};
      return {
        inb: {
          stat: '0',
          whId: this.whId,
          mtdCodeId: '',
          supplyId: '',
          purchName: '',
          purchId: '',
          purchDate: '',
          invNo: '',
          invDate: '',

          // 入库人 入库日期
          createUserName: userInfo.employeeName,
          inbDate: this.$dayjs().format('YYYY-MM-DD'),

          isConsume: '0',

          applyDeptId: '',
          applyDeptName: '',
          applyUserId: '',
          applyUserName: ''
        },
        inbDtlList: []
      };
    },

    // 添加物资明细 弹窗
    handleAddMaterialDictionary() {
      let ignoreIdList = '';
      if (this.form.inbDtlList.length) {
        ignoreIdList = this.form.inbDtlList.map(item => item.id).join(',');
      }

      this.$refs.dialogMultipleMaterialDictionary.show({
        otherParams: {
          status: '1',
          warehouseId: this.whId,
          ignoreIdList
        }
      });
    },

    // 删除物资明细
    async handleRemoveMaterialDictionary() {
      let selection = this.$refs.materialStoreDetailsDictionary.getSelection();
      if (selection.length === 0) {
        this.$newMessage('error', '请选择要删除的物资');
        return;
      }

      try {
        await this.$newConfirm(
          '您是否确认【<span style="color: red">删除</span>】当前数据？'
        );

        this.form.inbDtlList = this.form.inbDtlList.filter(
          item => !selection.includes(item.id)
        );
      } catch (error) {
        console.error(error);
      }
    },

    // 添加物资明细 弹窗提交
    handleSubmitMaterialDictionary(data) {
      let formatData = this.handleFormatMaterialDictionary(data);
      formatData.sort((a, b) => a.flowNo.localeCompare(b.flowNo));
      this.form.inbDtlList.push(...formatData);
    },

    // 物资明细格式化数据
    handleFormatMaterialDictionary(data) {
      return data.map(item => {
        return {
          isCheck: false,
          id: item.id,
          skuId: item.id,
          flowNo: item.flowNo,
          name: item.name,
          categoryName: item.categoryName,
          model: item.model,
          unit: item.unit,
          unitShow: item.unitShow,

          num: '',
          price: item.price || 0,
          amount: 0,

          prodNo: '',
          prodDate: '',
          expireDate: '',

          regNo: item.regNo,
          brand: item.brand,
          manufacturerName: item.manufacturerName,
          remark: ''
        };
      });
    },

    handleBtnOperate(event) {
      this[event]();
    },

    formatSaveData(data) {
      data.inbDtlList = data.inbDtlList.map(item => {
        return {
          skuId: item.skuId,
          num: item.num,
          price: item.price,
          expireDate: item.expireDate,
          prodDate: item.prodDate,
          prodNo: item.prodNo,
          remark: item.remark
        };
      });

      return data;
    },

    // 保存单据
    async operateSave() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();

        if (this.form.inbDtlList.length === 0) {
          throw new Error('请添加入库单物资明细!');
        }

        let data = cloneDeep(this.form);
        let API = this.isEdit
          ? this.ajax.materialInbUpdate
          : this.ajax.materialInbSave;

        let title = this.isEdit ? '修改入库单据' : '新增入库单据';

        let saveData = this.formatSaveData(data);
        let res = await API(saveData);
        if (!res.success) {
          throw new Error(res.message || `${title}失败!`);
        }

        this.$newMessage('success', res.message || `${title}成功!`);
        let detailData = await this.handleGetReceiptDetailApi(res.object);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      } finally {
        this.submitLoading = false;
      }
    },

    // 新增单据
    operateAdd() {
      this.resetFormRender({});
    },

    // 清空单据 等于新增单据
    operateClear() {
      this.operateAdd();
    },

    // 删除单据
    async operateDel() {
      const confirmHtml = `确定【<span style="color: red">删除</span>】当前数据？`;
      await this.$newConfirm(confirmHtml, '提示');
      const res = await this.ajax.materialInbBatchDelete([this.form.inb.id]);
      if (!res.success) {
        throw new Error(res.message || '操作失败!');
      }
      this.$newMessage('success', '操作成功');
      this.operateAdd();
    },

    // 审核单据
    async operateAudit() {
      try {
        const confirmHtml = `确定【<span style="color: ${primaryBlue}">审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.materialInbBatchConfirm([this.form.inb.id]);
        if (!res.success) {
          throw new Error(res.message || '操作失败!');
        }
        this.$newMessage('success', '操作成功');

        let id = this.form.inb.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 取消审核
    async operateCancelAudit() {
      try {
        const confirmHtml = `确定【<span style="color: red">取消审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.mInbBRollbackConfirm([this.form.inb.id]);
        if (!res.success) {
          throw new Error(res.message || '操作失败!');
        }
        this.$newMessage('success', '操作成功');

        let id = this.form.inb.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 退货登记
    operateReturn() {
      console.log('return');
    },

    // 上一条 单据切换
    async operatePrevious() {
      this.handleDirection('prev');
    },

    // 下一条 单据切换
    async operateNext() {
      this.handleDirection('next');
    },

    async handleDirection(type) {
      try {
        let id = this.form.inb.id;
        let tips = `切换${type === 'prev' ? '上' : '下'}一条单据`;
        const res = await this.ajax.materialInbDirection(id, type);
        if (!res.success) {
          throw new Error(res.message || `${tips}失败!`);
        }
        this.$newMessage('success', `${tips}成功`);
        this.resetFormRender(res.object);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 打印单据
    operatePrint() {
      this.$refs.dialogMaterialPrint.show({
        data: {
          obj: this.form.inb,
          list: this.form.inbDtlList
        },
        whName: this.whName
      });
    },

    // 导出单据
    operateExport() {
      console.log('export');
    },

    resetFormRender(data) {
      this.show({
        whName: this.whName,
        data
      });
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    },

    beforeHideMethod() {
      this.$emit('submit');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-store {
  ::v-deep {
    .content-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: normal !important;
      background: #fff;

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        margin: 8px 0;
        background-color: #f2f3f4;
        border-radius: 4px;

        > span {
          font-size: 15px;
          font-weight: 600;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #5260ff;
            margin-right: 4px;
            border-radius: 4px;
          }
        }

        .shallowButton {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
