<template>
  <div class="material-outbound-details-container">
    <ts-form ref="form" :model="form">
      <div class="form-group-tips">
        <span>单据信息</span>
      </div>

      <div class="basic-info">
        <div class="item" v-for="item in basicInfoColumns" :key="item.prop">
          <span class="label">{{ item.label }}：</span>
          <span class="value">
            {{ form.outb[item.prop] }}
          </span>
        </div>
      </div>
      <div class="form-group-tips">
        <span>入库物资明细</span>
      </div>

      <ts-vxe-base-table
        class="material-outbound-details-table"
        id="material-outbound-details-table"
        ref="table"
        height="auto"
        min-height="auto"
        :hasPage="false"
        :columns="detailsColumns"
        show-footer
        :footer-data="footerData"
        footer-cell-class-name="footer-val-styles"
      />
    </ts-form>
  </div>
</template>

<script>
import Decimal from 'decimal.js';
import cloneDeep from 'lodash-es/cloneDeep';

import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
import {
  baseMaterialDictionaryColumns,
  baseMaterialDictionaryColumns1
} from '@/views/moduleMaterial/inventoryManagement/config/dic';

export default {
  name: 'DialogDetailsMaterialOutbound',
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      footerData: [],
      basicInfoColumns: [
        { label: '出库类型', prop: 'mtdCodeName' },
        { label: '出库科室', prop: 'outDeptName' },
        { label: '领用人', prop: 'applyUserName' },
        { label: '出库日期', prop: 'outDate' },
        { label: '出库人', prop: 'outerName' },
        { label: '备注', prop: 'remark' }
      ]
    };
  },
  computed: {
    detailsColumns() {
      let arr = cloneDeep([
        ...baseMaterialDictionaryColumns,
        {
          prop: 'batchNo',
          label: '批次号',
          align: 'center',
          width: 155
        },
        {
          prop: 'stock',
          label: '库存数量',
          align: 'right',
          width: 80
        },
        {
          prop: 'num',
          label: '出库数量',
          width: 105,
          align: 'right'
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'right',
          width: 105,
          render: (h, { row }) => {
            return h('span', numToLocaleStrFixed2(row.price));
          }
        },
        {
          prop: 'totalAmt',
          label: '金额(元)',
          align: 'right',
          width: 100,
          render: (h, { row }) => {
            return h('span', numToLocaleStrFixed2(row.totalAmt));
          }
        },
        {
          prop: 'prodNo',
          label: '生产批号',
          align: 'center',
          width: 100
        },
        {
          prop: 'prodDate',
          label: '生产日期',
          align: 'center',
          width: 85
        },
        {
          prop: 'expireDate',
          label: '失效日期',
          align: 'center',
          width: 85
        },
        ...baseMaterialDictionaryColumns1
      ]);

      // 处理列表 不显示指定列 固定左侧列
      arr = arr.filter(item => {
        return !['isCheck'].includes(item.prop);
      });

      arr.forEach(item => {
        if (['index', 'flowNo', 'name', 'categoryName'].includes(item.prop)) {
          item.fixed = 'left';
        }
        if (typeof item.render === 'string') {
          delete item.render;
        }
      });

      return arr;
    }
  },

  methods: {
    setTableData() {
      let rows = cloneDeep(this.form.outbDtlList);
      rows.forEach((item, index) => {
        item.index = index + 1;
      });

      this.$refs.table.refresh({
        rows
      });
      this.footerData = this.handleGetFooterData(rows);
    },

    handleGetFooterData(rows) {
      const calculateTotal = key => {
        return rows
          .map(row => row[key])
          .reduce((prev, curr) => {
            return !isNaN(curr) ? prev.plus(curr) : prev;
          }, new Decimal(0));
      };

      return [
        {
          index: '合计',
          num: calculateTotal('num'),
          totalAmt: numToLocaleStrFixed2(calculateTotal('totalAmt'))
        }
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
.material-outbound-details-container {
  padding: 0px;
  height: 100%;

  .ts-form {
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-group-tips {
      width: 100%;
      color: #333;
      font-weight: 800;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      > span {
        font-weight: 800;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: $primary-blue;
          margin-right: 8px;
          border-radius: 4px;
          transform: translateY(2px);
        }
      }
    }
    .basic-info {
      display: flex;
      flex-wrap: wrap;
      padding-top: 12px;
      background: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.14);
      border: 1px solid #e5e5e5;
      border-bottom: none;
      margin-bottom: 8px;

      .item {
        width: 33%;
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .label {
          width: 130px;
          text-align: right;
          color: #606266;
          font-size: 14px;
        }

        .value {
          flex: 1;
          color: #333;
          font-size: 14px;
        }
      }
    }

    .material-outbound-details-table {
      ::v-deep {
        .footer-val-styles {
          background-color: #eceef3;
          .vxe-cell--item {
            font-weight: bold;
            color: $warning-color;
          }
        }
      }
    }
  }
}
</style>
