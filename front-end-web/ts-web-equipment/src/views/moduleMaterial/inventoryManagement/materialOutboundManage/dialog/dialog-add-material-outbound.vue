<template>
  <vxe-modal
    className="dialog-add-material-outbound"
    :title="title"
    v-model="visible"
    width="90%"
    height="85%"
    :before-hide-method="beforeHideMethod"
  >
    <template #default>
      <div class="content-container">
        <operate-dialog-btns
          :stat="form.outb?.stat"
          @btnOperate="handleBtnOperate"
          type="2"
        />

        <ts-form ref="form" :model="form" v-if="isAdd || isEdit">
          <div class="form-group-tips">
            <span>单据信息</span>
          </div>

          <material-outbound-basic-form
            ref="materialOutboundBasicForm"
            :form="form"
            :rules="rules"
            :methodOptions="methodOptions"
            :deptTreeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
          />

          <div class="form-group-tips">
            <span>出库物资明细</span>

            <div>
              <ts-button
                class="shallowButton"
                type="primary"
                @click="handleAddMaterialDictionary"
              >
                添加物资
              </ts-button>
              <ts-button type="danger" @click="handleRemoveMaterialDictionary">
                删除
              </ts-button>
            </div>
          </div>

          <material-outbound-details-dictionary
            ref="materialOutboundDetailsDictionary"
            :form="form"
            :rules="rules"
            :isBatch="isBatch"
          />
        </ts-form>

        <!-- 单据详情 -->
        <dialog-details-material-outbound
          v-if="isDetails"
          ref="dialogDetailsMaterialOutbound"
          :form="form"
        />

        <dialog-multiple-material-dictionary
          renderType="2"
          ref="dialogMultipleMaterialDictionary"
          @submit="handleSubmitMaterialDictionary"
        />

        <dialog-material-print renderType="2" ref="dialogMaterialPrint" />
      </div>
    </template>
  </vxe-modal>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import cloneDeep from 'lodash-es/cloneDeep';

import OperateDialogBtns from '@/views/moduleMaterial/inventoryManagement/components/operate-dialog-btns.vue';

import DialogDetailsMaterialOutbound from './dialog-details-material-outbound.vue';
import MaterialOutboundBasicForm from './components/material-outbound-basic-form.vue';
import MaterialOutboundDetailsDictionary from './components/material-outbound-details-dictionary.vue';

import DialogMultipleMaterialDictionary from '@/views/moduleMaterial/inventoryManagement/components/dialog-multiple-material-dictionary.vue';
import DialogMaterialPrint from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print.vue';
export default {
  components: {
    MaterialOutboundBasicForm,
    MaterialOutboundDetailsDictionary,
    DialogMultipleMaterialDictionary,
    OperateDialogBtns,
    DialogMaterialPrint,
    DialogDetailsMaterialOutbound
  },

  props: {
    methodOptions: {
      type: Array,
      default: () => []
    },
    whId: {
      type: String,
      default: ''
    },
    handleGetReceiptDetailApi: {
      type: Function,
      default: () => {}
    }
  },

  data() {
    return {
      visible: false,
      loading: false,
      deptTreeData: [],
      defaultExpandedKeys: [],

      stkMtd: '',
      whName: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },

  computed: {
    title() {
      return `【${this.whName}】【 ${this.form.outb?.flowNo || ''}】出库单`;
    },

    isAdd() {
      return this.form.outb?.stat == '0';
    },

    isEdit() {
      return this.form.outb?.stat == '1';
    },

    isDetails() {
      return this.form.outb?.stat == '2';
    },

    isBatch() {
      return this.stkMtd == '0';
    }
  },

  methods: {
    async show({ data = {}, whName = '', stkMtd = '' }) {
      this.getDeptTree();
      this.whName = whName;
      this.stkMtd = stkMtd;

      if (JSON.stringify(data) !== '{}') {
        data.outbDtlList.forEach(item => {
          item.id = `${item.id}*${item.batchNo}`;
        });
        this.$set(this, 'form', cloneDeep(data));
      } else {
        this.$set(this, 'form', this.initForm());
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        // 详情单据 回显表单
        if (this.isDetails) {
          this.$refs.dialogDetailsMaterialOutbound.setTableData();
        }
      });
    },

    async getDeptTree() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw new Error(res.message || '科室数据获取失败!');
        }
        this.deptTreeData = res.object || [];
        this.defaultExpandedKeys = [this.deptTreeData[0]?.id];
      } catch (error) {
        throw error;
      }
    },

    initForm() {
      let parentStoreInfo = this.$getParentStoreInfo();
      let userInfo = parentStoreInfo?.userInfo || {};
      return {
        outb: {
          stat: '0',
          whId: this.whId,
          mtdCodeId: '',

          outDeptId: '',
          applyUserId: '',
          applyUserName: '',
          outDate: this.$dayjs().format('YYYY-MM-DD'),
          outerName: userInfo.employeeName,
          remark: ''
        },
        outbDtlList: []
      };
    },

    // 添加物资
    // isBatch true 按批次
    // isBatch false 按库存

    // 按批次 不需要过滤 ignoreIdList
    // 按库存 需要过滤 ignoreIdList
    handleAddMaterialDictionary() {
      let otherParams = {
        warehouseId: this.whId,
        queryType: this.isBatch ? 'batch' : 'stock'
      };
      // 按批次 取batchNo
      // 按库存 取skuId
      let arr = this.form.outbDtlList;
      if (this.isBatch) {
        otherParams.ignoreBatchNoList = arr.map(item => item.batchNo);
      } else {
        otherParams.ignoreIdList = arr.map(item => item.skuId);
      }

      this.$refs.dialogMultipleMaterialDictionary.show({
        otherParams
      });
    },

    // 删除选择了的物资字典
    async handleRemoveMaterialDictionary() {
      let selection = this.$refs.materialOutboundDetailsDictionary.getSelection();
      if (selection.length === 0) {
        this.$newMessage('error', '请选择要删除的物资');
        return;
      }

      try {
        await this.$newConfirm(
          '您是否确认【<span style="color: red">删除</span>】当前数据？'
        );

        this.form.outbDtlList = this.form.outbDtlList.filter(
          item => !selection.includes(item.id)
        );
      } catch (error) {
        console.error(error);
      }
    },

    // 添加物资 完成回调
    // 按批次 设置该物资字典的批次信息
    // 按库存
    handleSubmitMaterialDictionary(data) {
      let formatData = this.handleFormatMaterialDictionary(data);
      formatData.sort((a, b) => a.flowNo.localeCompare(b.flowNo));
      this.form.outbDtlList.push(...formatData);
    },

    handleFormatMaterialDictionary(data) {
      return data.map(item => {
        // 按批次 价格是批次的
        // 按库存 价格是物资字典的价格
        let price = this.isBatch ? item.batchPrice : item.price;
        return {
          isCheck: false,
          id: item.id,
          skuId: item.skuId,

          flowNo: item.flowNo,
          name: item.name,
          categoryName: item.categoryName,
          model: item.model,
          unit: item.unit,
          unitShow: item.unitShow,

          batchNo: item.batchNo,
          stock: item.stock,
          num: '',
          price: price || 0,
          amount: 0,

          prodNo: item.prodNo,
          prodDate: item.prodDate,
          expireDate: item.expireDate,

          regNo: item.regNo,
          brand: item.brand,
          manufacturerName: item.manufacturerName,
          remark: item.remark
        };
      });
    },

    handleBtnOperate(event) {
      this[event]();
    },

    formatSaveData(data) {
      data.outbDtlList = data.outbDtlList.map(item => {
        return {
          batchNo: item.batchNo,
          num: item.num,
          price: item.price,
          skuId: item.skuId
        };
      });
      return data;
    },

    // 保存单据
    async operateSave() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();

        if (this.form.outbDtlList.length === 0) {
          throw new Error('请添加出库单物资明细!');
        }

        let data = cloneDeep(this.form);
        let API = this.isEdit
          ? this.ajax.materialOutbUpdate
          : this.ajax.materialOutbSave;

        let title = this.isEdit ? '修改出库单据' : '新增出库单据';

        let saveData = this.formatSaveData(data);
        let res = await API(saveData);
        if (!res.success) {
          throw new Error(res.message || `${title}失败!`);
        }
        this.$newMessage('success', res.message || `${title}成功!`);
        let detailData = await this.handleGetReceiptDetailApi(res.object);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      } finally {
        this.submitLoading = false;
      }
    },

    // 新增单据
    operateAdd() {
      this.resetFormRender({});
    },

    // 清空单据 等于新增单据
    operateClear() {
      this.operateAdd();
    },

    // 删除单据
    async operateDel() {
      const confirmHtml = `确定【<span style="color: red">删除</span>】当前数据？`;
      await this.$newConfirm(confirmHtml, '提示');
      const res = await this.ajax.materialOutbBatchDelete([this.form.outb.id]);
      if (!res.success) {
        throw new Error(res.message || '操作失败!');
      }
      this.$newMessage('success', '操作成功');
      this.operateAdd();
    },

    // 审核单据
    async operateAudit() {
      try {
        const confirmHtml = `确定【<span style="color: ${primaryBlue}">审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.materialOutbBatchConfirm([
          this.form.outb.id
        ]);
        if (!res.success) {
          throw new Error(res.message || '操作失败!');
        }
        this.$newMessage('success', '操作成功');

        let id = this.form.outb.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 取消审核
    async operateCancelAudit() {
      try {
        const confirmHtml = `确定【<span style="color: red">取消审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.mOutbBRollbackConfirm([this.form.outb.id]);
        if (!res.success) {
          throw new Error(res.message || '操作失败!');
        }
        this.$newMessage('success', '操作成功');

        let id = this.form.outb.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 退库
    operateReturn() {
      console.log('return');
    },

    // 上一条 单据切换
    async operatePrevious() {
      this.handleDirection('prev');
    },

    // 下一条 单据切换
    async operateNext() {
      this.handleDirection('next');
    },

    async handleDirection(type) {
      try {
        let id = this.form.outb.id;
        let tips = `切换${type === 'prev' ? '上' : '下'}一条单据`;
        const res = await this.ajax.materialOutbDirection(id, type);
        if (!res.success) {
          throw new Error(res.message || `${tips}失败!`);
        }
        this.$newMessage('success', `${tips}成功`);
        this.resetFormRender(res.object);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 打印单据
    operatePrint() {
      this.$refs.dialogMaterialPrint.show({
        data: {
          outb: this.form.outb,
          list: this.form.outbDtlList
        },
        whName: this.whName
      });
    },

    operateExport() {
      console.log('export');
    },

    resetFormRender(data) {
      this.show({
        whName: this.whName,
        stkMtd: this.stkMtd,
        data
      });
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    },

    beforeHideMethod() {
      this.$emit('submit');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-outbound {
  ::v-deep {
    .content-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: normal !important;
      background: #fff;

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        margin: 8px 0;
        background-color: #f2f3f4;
        border-radius: 4px;

        > span {
          font-size: 15px;
          font-weight: 600;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #5260ff;
            margin-right: 4px;
            border-radius: 4px;
          }
        }

        .shallowButton {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
