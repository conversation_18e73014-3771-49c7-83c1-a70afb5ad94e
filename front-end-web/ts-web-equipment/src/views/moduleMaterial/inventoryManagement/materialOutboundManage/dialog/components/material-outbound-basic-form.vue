<template>
  <div class="material-outbound-basic-form">
    <ts-row>
      <ts-col :span="6">
        <ts-form-item
          label="出库类型"
          prop="outb.mtdCodeId"
          :rules="rules.required"
        >
          <ts-select
            style="width: 100%"
            v-model="form.outb.mtdCodeId"
            placeholder="请选择"
            filterable
            clearable
          >
            <ts-option
              v-for="item of methodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ts-select>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="出库科室"
          prop="outb.outDeptId"
          :rules="rules.required"
        >
          <input-tree
            v-model="form.outb.outDeptId"
            placeholder="请选择出库科室"
            :treeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
            key="deptId"
            @valuechange="handleSelectDept"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="领用人">
          <base-select
            style="width: 100%"
            v-model="form.outb.applyUserId"
            :inputText.sync="form.outb.applyUserName"
            :loadMethod="handleGetPersonList"
            label="empName"
            value="empCode"
            searchInputName="empName"
            :otherSearchParams="{
              empDeptCode: form.outb.outDeptId
            }"
            clearable
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="出库日期"
          prop="outb.outDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width:100%"
            v-model="form.outb.outDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择出库日期"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-row>
      <ts-col :span="6">
        <ts-form-item label="出库人">
          <ts-input v-model="form.outb.outerName" disabled />
        </ts-form-item>
      </ts-col>

      <ts-col :span="18">
        <ts-form-item label="备注">
          <ts-input
            v-model="form.outb.remark"
            placeholder="请输入备注"
            maxlength="255"
            type="textarea"
            class="textarea"
            showWordLimit
          />
        </ts-form-item>
      </ts-col>
    </ts-row>
  </div>
</template>

<script>
import InputTree from '@/components/input-tree/index.vue';
export default {
  name: 'MaterialOutboundBasicForm',
  components: {
    InputTree
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    methodOptions: {
      type: Array,
      default: () => []
    },
    deptTreeData: {
      type: Array,
      default: () => []
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    async handleGetPersonList(data) {
      try {
        const res = await this.ajax.getEmployeeList({
          pageSize: 15,
          status: 1,
          sidx: 'create_date',
          sord: 'desc',
          ...data
        });
        return res.rows;
      } catch (error) {
        console.error('获取人员列表失败:', error);
        return false;
      }
    },

    handleSelectDept(value) {
      this.$set(this.form.outb, 'applyUserId', '');
      this.$set(this.form.outb, 'applyUserName', '');
    }
  }
};
</script>

<style lang="scss" scoped>
.material-outbound-basic-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
