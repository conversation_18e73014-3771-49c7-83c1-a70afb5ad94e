<template>
  <div class="material-outbound-details-dictionary">
    <table class="custom-form-table">
      <thead class="custom-form-table__header">
        <tr class="custom-form-table__header-row">
          <td
            class="custom-form-table__header-cell"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            v-for="item in materialDictionaryColumns"
            :key="item.prop"
          >
            <template>
              <span v-if="item.requiredIcon" class="required-icon">*</span>
              <span class="custom-form-table__header-cell-label">
                {{ item.label }}
              </span>
            </template>
          </td>
        </tr>
      </thead>
      <tbody class="custom-form-table__body">
        <tr
          class="custom-form-table__body-row"
          v-for="(item, index) in form.outbDtlList"
          :key="item.id"
          @click="handleClickRow(item)"
        >
          <td
            class="custom-form-table__body-cell"
            v-for="column in materialDictionaryColumns"
            :key="column.prop"
          >
            <template v-if="column.render === 'index'">
              <span>{{ index + 1 }}</span>
            </template>

            <template v-if="column.render === 'checkbox'">
              <input type="checkbox" v-model="item[column.prop]" />
            </template>

            <template v-if="column.render === 'text'">
              <span>{{ item[column.prop] }}</span>
            </template>

            <template v-else>
              <template v-if="column.prop === 'num'">
                <comp-integer-input
                  class="auto-input-styles"
                  v-model="item.num"
                  :prop="`outbDtlList.${index}.num`"
                  :rules="rules.required"
                  @blur="() => handleBlurComputedTotal(item)"
                />
              </template>

              <template v-if="column.prop === 'price'">
                <span class="total-amount-item">
                  {{ numToLocaleStrFixed2(item.price) }}
                </span>
              </template>

              <template v-if="column.prop === 'totalAmt'">
                <span class="total-amount-item">
                  {{ numToLocaleStrFixed2(item.totalAmt) }}
                </span>
              </template>
            </template>

            <template v-if="column.render === 'operate'">
              <div class="delete-device" @click="() => handleDel(index)">
                移除
              </div>
            </template>
          </td>
        </tr>

        <tr class="total-amount" v-if="form.outbDtlList.length">
          <td
            class="footer-total-amount-item"
            v-for="(item, index) in materialDictionaryColumns"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            :key="item.prop"
          >
            <template v-if="index === 0">
              合计
            </template>

            <template v-if="item.prop === 'num'">
              {{ totalNum }}
            </template>

            <template v-if="item.prop === 'totalAmt'">
              {{ totalAmount }}
            </template>
          </td>
        </tr>
        <tr v-else>
          <td :colspan="materialDictionaryColumns.length" class="empty-row">
            <span>暂无数据</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { Decimal } from 'decimal.js';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
import {
  baseMaterialDictionaryColumns,
  baseMaterialDictionaryColumns1
} from '@/views/moduleMaterial/inventoryManagement/config/dic';

import CompIntegerInput from '@/components/busi-form-table-components/comp-integer-input.vue';
export default {
  name: 'MaterialOutboundDetailsDictionary',
  components: {
    CompIntegerInput
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    },
    isBatch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      numToLocaleStrFixed2
    };
  },
  computed: {
    totalNum() {
      const total = this.form.outbDtlList.reduce(
        (sum, item) => sum.plus(item.num || 0),
        new Decimal(0)
      );
      return total;
    },

    totalAmount() {
      const total = this.form.outbDtlList.reduce(
        (sum, item) => sum.plus(item.totalAmt || 0),
        new Decimal(0)
      );
      return numToLocaleStrFixed2(total);
    },

    materialDictionaryColumns() {
      let arr = cloneDeep([
        ...baseMaterialDictionaryColumns,
        {
          prop: 'batchNo',
          label: '批次号',
          align: 'center',
          render: 'text',
          width: 155
        },
        {
          prop: 'stock',
          label: '库存数量',
          align: 'right',
          render: 'text',
          width: 80
        },
        {
          prop: 'num',
          label: '出库数量',
          align: 'right',
          width: 105,
          requiredIcon: true
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'right',
          render: 'text',
          width: 105
        },
        {
          prop: 'totalAmt',
          label: '金额(元)',
          align: 'right',
          width: 100
        },
        {
          prop: 'prodNo',
          label: '生产批号',
          align: 'center',
          render: 'text',
          width: 100
        },
        {
          prop: 'prodDate',
          label: '生产日期',
          align: 'center',
          render: 'text',
          width: 85
        },
        {
          prop: 'expireDate',
          label: '失效日期',
          align: 'center',
          render: 'text',
          width: 85
        },
        ...baseMaterialDictionaryColumns1
      ]);

      if (!this.isBatch) {
        let filterArr = ['batchNo', 'prodNo', 'prodDate', 'expireDate'];
        arr = arr.filter(item => !filterArr.includes(item.prop));
      }
      return arr;
    }
  },
  methods: {
    getSelection() {
      return this.form.outbDtlList
        .filter(item => item.isCheck)
        .map(item => item.id);
    },

    // 计算总金额
    handleBlurComputedTotal(row) {
      if (Number(row.num) > Number(row.stock)) {
        row.num = row.stock;
        this.$newMessage('warning', '出库数量不能大于库存数量!');
      }

      const num = parseFloat(row.num);
      const price = parseFloat(row.price);

      if (isNaN(num) || isNaN(price)) {
        row.totalAmt = 0;
        return;
      }

      this.$set(row, 'num', num);
      this.$set(row, 'price', price);
      let totalAmt = Decimal.mul(price, num).toNumber();
      this.$set(row, 'totalAmt', totalAmt);
    },

    handleClickRow(item) {
      item.isCheck = !item.isCheck;
    }
  }
};
</script>

<style lang="scss" scoped>
.material-outbound-details-dictionary {
  overflow-x: auto;

  ::v-deep {
    .auto-input-styles {
      width: 100%;
      .ts-input {
        min-width: 100% !important;
      }
    }

    .total-amount-item {
      width: 100%;
      display: inline-block;
      text-align: right !important;
    }

    .custom-form-table__body-cell {
      .auto-height-input-container {
        .el-form-item__content {
          line-height: 10px !important;
        }
      }
    }
  }

  .total-amount {
    height: 32px;
    border: 1px solid rgba(235, 238, 245, 1);
    border-top: none;
    background-color: #f3f6f9;

    .footer-total-amount-item {
      text-align: right;
      font-weight: bold;
      color: $warning-color;
    }
  }

  .delete-device {
    color: red;
    cursor: pointer;
  }

  .empty-row {
    text-align: center;
    color: #999;
    height: 50px;
  }
}
</style>
