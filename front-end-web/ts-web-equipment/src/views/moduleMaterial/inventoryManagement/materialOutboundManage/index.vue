<template>
  <div class="material-outbound-manage-box">
    <warehouse-tabs
      ref="warehouseTabs"
      v-model="whId"
      :tabsName.sync="whName"
      @tab-click="handleTabClick"
    />

    <div class="receipt-container">
      <outbound-receipt-search
        ref="outboundReceiptSearch"
        @search="handleOutboundReceiptSearch"
        @add="operateAdd"
        @delete="operateDelete"
        @audit="operateAudit"
        @cancelAudit="operateCancelAudit"
        @returnStore="operateReturnStore"
        @print="operatePrint"
        @export="operateExport"
      />

      <outbound-receipt-table
        ref="outboundReceiptTable"
        @receiptEdit="operateEdit"
        @currentChange="handleCurrentChange"
        @refresh="handleRefreshOutboundReceiptTable"
      />
    </div>

    <receipt-details-table-container
      ref="receiptDetailsTable"
      type="2"
      :columns="detailsColumns"
      :parentCurrentRecord="parentCurrentRecord"
      :handleGetReceiptDetailApi="handleGetReceiptDetailApi"
    />

    <dialog-add-material-outbound
      ref="dialogAddMaterialOutbound"
      :whId="whId"
      :methodOptions="methodOptions"
      :handleGetReceiptDetailApi="handleGetReceiptDetailApi"
      @submit="handleRefreshOutboundReceiptTable"
    />

    <dialog-material-print
      renderType="2"
      ref="dialogMaterialPrint"
      @print="handlePrintRefresh"
    />
  </div>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import { detailsColumns } from './details/detailsColumns';
import WarehouseTabs from '../components/warehouse-tabs.vue';
import OutboundReceiptSearch from './receipt/outbound-receipt-search.vue';
import OutboundReceiptTable from './receipt/outbound-receipt-table.vue';

import ReceiptDetailsTableContainer from '../components/receipt-details-table-container.vue';
import DialogMaterialPrint from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print.vue';

import DialogAddMaterialOutbound from './dialog/dialog-add-material-outbound.vue';
export default {
  name: 'MaterialOutboundManage',
  components: {
    OutboundReceiptSearch,
    OutboundReceiptTable,
    ReceiptDetailsTableContainer,
    DialogAddMaterialOutbound,
    WarehouseTabs,
    DialogMaterialPrint
  },
  data() {
    return {
      detailsColumns,
      whId: '0',
      whName: '',
      methodOptions: []
    };
  },

  methods: {
    async refresh() {
      // 获取出库方式
      this.handleGetMaterialMethodCodeList();

      // 获取仓库列表
      await this.$refs.warehouseTabs?.handleGetAllWarehouseList();
      // 刷新出库单据列表
      this.handleRefreshOutboundReceiptTable();
    },

    // 出库单据列表查询
    handleOutboundReceiptSearch() {
      this.$refs.outboundReceiptTable.setPageNo(1);
      this.handleRefreshOutboundReceiptTable();
    },

    // 获取仓库出库方式
    handleGetStkMtd() {
      return this.$refs.warehouseTabs.getStkMtd();
    },

    // 新增出库单据
    operateAdd() {
      this.$refs.dialogAddMaterialOutbound.show({
        stkMtd: this.handleGetStkMtd(),
        whName: this.whName,
        data: {}
      });
    },

    // 单据操作dialog
    async operateEdit(row) {
      try {
        let data = await this.handleGetReceiptDetailApi(row.id);

        this.$refs.dialogAddMaterialOutbound.show({
          stkMtd: this.handleGetStkMtd(),
          whName: this.whName,
          data
        });
      } catch (error) {
        this.$newMessage('error', error.message);
      }
    },

    // 通用操作方法
    async handleBatchOperation({ action, confirmColor, apiMethod }) {
      const selection = this.$refs.outboundReceiptTable.getSelection();
      if (!selection.length) {
        this.$newMessage('warning', `请选择要${action}的出库单据！`);
        return;
      }

      try {
        const confirmHtml = `确定【<span style="color: ${confirmColor}">${action}</span>】选中的数据？`;
        await this.$newConfirm(confirmHtml, '提示');

        const inbIdList = selection.map(item => item.id);
        const res = await this.ajax[apiMethod](inbIdList);
        if (res.success === false) {
          this.$newMessage('error', res.message || '操作失败');
          return;
        }
        this.$newMessage('success', '操作成功');
        this.handleRefreshOutboundReceiptTable();
      } catch (error) {
        console.error(error);
      }
    },

    // 删除单据
    operateDelete() {
      this.handleBatchOperation({
        action: '删除',
        confirmColor: 'red',
        apiMethod: 'materialOutbBatchDelete'
      });
    },

    // 审核单据
    operateAudit() {
      this.handleBatchOperation({
        action: '审核',
        confirmColor: primaryBlue,
        apiMethod: 'materialOutbBatchConfirm'
      });
    },

    // 取消审核单据
    operateCancelAudit() {
      this.handleBatchOperation({
        action: '取消审核',
        confirmColor: 'red',
        apiMethod: 'mOutbBRollbackConfirm'
      });
    },

    // 退库登记
    operateReturnStore() {},

    // 单据打印
    async operatePrint() {
      try {
        const currentRecord = this.$refs.outboundReceiptTable.handleGetCurrentRecord();
        if (!currentRecord) {
          throw new Error('请选择要打印的出库单据！');
        }
        const data = await this.handleGetReceiptDetailApi(currentRecord.id);

        this.$refs.dialogMaterialPrint.show({
          data: {
            outb: data.outb,
            list: data.outbDtlList
          },
          whName: this.whName
        });
      } catch (error) {
        this.$newMessage('error', error.message || '打印失败');
      }
    },

    handlePrintRefresh() {
      // 清空详情表格数据
      this.$refs.receiptDetailsTable.clearTableData([]);
      this.$refs.outboundReceiptTable.handleSetCurrentRow(null);

      // 刷新出库单据列表
      this.handleRefreshOutboundReceiptTable();
    },

    // 导出
    operateExport() {},

    // 单据列表 刷新
    async handleRefreshOutboundReceiptTable() {
      if (!this.checkQueryParam()) {
        return;
      }

      let searchForm = this.$refs.outboundReceiptSearch.searchForm;
      let { date = [] } = searchForm,
        table = this.$refs.outboundReceiptTable,
        pageNo = table.getPageNo(),
        pageSize = table.getPageSize(),
        formData = {
          ...searchForm,
          pageNo,
          pageSize,
          whId: this.whId
        };

      const [startD, endD] = date;
      formData.outDateQuery =
        startD && endD ? `${startD} 00:00:00,${endD} 23:59:59` : '';
      !formData.outDateQuery && delete formData.outDateQuery;
      delete formData.date;

      let res = await this.ajax.materialOutbList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });

      table.setPageData({
        ...res,
        rows
      });

      // 设置第一行选中
      // 刷新单据详情
      if (Array.isArray(rows) && rows.length) {
        let currentRow = this.parentCurrentRecord();
        if (!currentRow) {
          this.$refs.outboundReceiptTable.handleSetCurrentRow(rows[0]);
          this.$refs.receiptDetailsTable.detailsRefresh();
        }
      } else {
        this.$refs.receiptDetailsTable.clearTableData([]);
      }
    },

    // 切换仓库
    handleTabClick() {
      // 清空搜索条件
      this.$refs.outboundReceiptSearch.clearSearchForm();

      // 清空详情表格数据
      this.$refs.receiptDetailsTable.clearSearchForm();
      this.$refs.receiptDetailsTable.clearTableData([]);

      // 获取仓库出库方式
      this.handleGetMaterialMethodCodeList();
      this.handleOutboundReceiptSearch();
    },

    // 出库单 高亮数据 切换
    handleCurrentChange() {
      this.$refs.receiptDetailsTable.detailsRefresh();
    },

    // 获取出库单据详情 （提供给 dialog 详情表格 使用）
    async handleGetReceiptDetailApi(id, params) {
      let isBatch = this.handleGetStkMtd() === '0';
      let searchParams = {
        withBatch: isBatch ? '1' : '0',
        ...params
      };
      const res = await this.ajax.materialOutbDetail(id, searchParams);
      if (!res.success) {
        throw new Error(res.message || '获取出库单据详情失败');
      }
      return res.object || {};
    },

    // 获取单据列表 当前选中行数据（提供给 详情表格 使用）
    parentCurrentRecord() {
      return this.$refs.outboundReceiptTable.handleGetCurrentRecord();
    },

    checkData([start, end]) {
      let num = 0;
      start && num++;
      end && num++;
      return num === 1;
    },

    checkQueryParam() {
      const { date = [] } = this.$refs.outboundReceiptSearch.searchForm;
      if (this.checkData(date)) {
        this.$newMessage('warning', '请选择出库日期完整的时间区间查询');
        return false;
      }
      return true;
    },

    // 获取出库方式
    async handleGetMaterialMethodCodeList() {
      try {
        const res = await this.ajax.getMaterialMethodCodeList({
          method: '2',
          status: '1',
          warehouseIdSet: this.whId,
          pageNo: 1,
          pageSize: 99999
        });

        let options = Array.isArray(res.rows) ? res.rows : [];
        options.forEach(item => {
          item.element = 'ts-option';
          item.label = item.name;
          item.value = item.id;
        });
        this.methodOptions = options;

        let searchOptions = [
          {
            label: '全部',
            value: '',
            element: 'ts-option'
          },
          ...options
        ];
        this.$refs.outboundReceiptSearch.setMethodOptions(searchOptions);
      } catch (error) {
        this.$newMessage('error', error.message || '获取出库方式失败!');
        this.methodOptions = [];
      }
    }
  }
};
</script>

<style scoped lang="scss">
.material-outbound-manage-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .type-tabs-container .el-tabs__item {
      width: auto !important;
    }

    .type-tabs-container .el-tabs__nav-wrap::after {
      height: 0;
    }
    .base-date-range-picker {
      display: inline-flex;
      align-items: center;
      .range-separator {
        margin: 0 4px;
      }
      .date-picker {
        display: flex;
        align-items: center;
        background: transparent;
        width: 140px !important;
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }

  .receipt-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid#295cf9;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }
}
</style>
