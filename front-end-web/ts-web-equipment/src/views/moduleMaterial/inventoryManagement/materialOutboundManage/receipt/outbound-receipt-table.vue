<template>
  <ts-vxe-base-table
    class="outbound-receipt-table form-table"
    id="outbound-receipt-table"
    ref="table"
    minHeight="100%"
    :columns="columns"
    :rowClassName="handleRowClassName"
    @refresh="refresh"
    @selection-change="handleSelectionChange"
    @current-change="handleCurrentChange"
  />
</template>

<script>
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';

export default {
  name: 'OutboundReceiptTable',
  data() {
    return {
      selection: [],
      columns: [
        {
          type: 'checkbox',
          align: 'center',
          fixed: 'left',
          width: 50
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          fixed: 'left',
          width: 50
        },
        {
          label: '单据状态',
          prop: 'statShow',
          align: 'center',
          fixed: 'left',
          width: 100
        },
        {
          label: '单据号',
          align: 'center',
          prop: 'flowNo',
          fixed: 'left',
          width: 140,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.$emit('receiptEdit', row);
                  }
                }
              },
              row.flowNo
            );
          }
        },
        {
          label: '出库类型',
          prop: 'mtdCodeName',
          align: 'center',
          width: 120
        },
        {
          label: '库房名称',
          prop: 'whName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '科室名称',
          prop: 'outDeptName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '领用人',
          prop: 'applyUserName',
          align: 'center',
          width: 90
        },
        {
          label: '金额(元)',
          prop: 'totalAmt',
          align: 'right',
          width: 120,
          render: (h, { row }) => {
            return h('span', numToLocaleStrFixed2(row.totalAmt));
          }
        },
        {
          label: '打印状态',
          prop: 'printStatShow',
          width: 70,
          align: 'center'
        },
        {
          label: '出库人',
          prop: 'createUserName',
          width: 75,
          align: 'center'
        },
        {
          label: '出库日期',
          prop: 'outDate',
          width: 90,
          align: 'center'
        },
        {
          label: '登记时间',
          prop: 'createDate',
          align: 'center',
          width: 90,
          render: (h, { row }) => {
            return h('span', this.$dayjs(row.createDate).format('YYYY-MM-DD '));
          }
        },
        {
          label: '审核人',
          prop: 'doerName',
          align: 'center',
          width: 75
        },
        {
          label: '审核时间',
          prop: 'doTime',
          align: 'center',
          width: 145
        },
        {
          label: '备注',
          prop: 'remark',
          align: 'center',
          minWidth: 120
        }
      ]
    };
  },
  methods: {
    getSelection() {
      return this.selection;
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    },

    handleCurrentChange(row) {
      this.$emit('currentChange', row);
    },

    handleSetCurrentRow(row) {
      this.$refs.table.handleSetCurrentRow(row);
    },

    handleGetCurrentRecord() {
      return this.$refs.table.handleGetCurrentRecord();
    },

    getPageNo() {
      return this.$refs.table.pageNo;
    },

    getPageSize() {
      return this.$refs.table.pageSize;
    },

    setPageNo(pageNo) {
      this.$refs.table.pageNo = pageNo;
    },

    setPageData(data) {
      this.$refs.table.refresh(data);
    },

    refresh() {
      this.$emit('refresh');
    },

    handleRowClassName({ row }) {
      let dic = {
        '1': 'row-all-back',
        '2': 'row-part-back'
      };

      return dic[row.status] || '';
    }
  }
};
</script>

<style lang="scss" scoped>
.outbound-receipt-table {
  ::v-deep {
    .row-all-back {
      color: #f64e4b;
    }

    .row-part-back {
      color: #f4a622;
    }
  }
}
</style>
