import { numToLocaleStrFixed2 } from '@/unit/commonHandle';

export const detailsColumns = [
  {
    label: '序号',
    prop: 'pageIndex',
    align: 'center',
    fixed: 'left',
    width: 50
  },
  {
    label: '单据号',
    align: 'center',
    prop: 'receiptFlowNo',
    fixed: 'left',
    width: 160
  },
  {
    label: '物资编码',
    prop: 'flowNo',
    align: 'center',
    fixed: 'left',
    width: 160
  },
  {
    prop: 'name',
    label: '物资名称',
    align: 'center',
    fixed: 'left',
    width: 150
  },
  {
    prop: 'categoryName',
    label: '物资分类',
    align: 'center',
    fixed: 'left',
    width: 135
  },
  {
    label: '规格型号',
    prop: 'model',
    align: 'center',
    minWidth: 140
  },
  {
    label: '单位',
    prop: 'unitShow',
    align: 'center',
    width: 90
  },
  {
    label: '批次号',
    prop: 'batchNo',
    align: 'center',
    width: 140
  },
  {
    label: '出库数量',
    prop: 'num',
    align: 'right',
    width: 75
  },
  {
    label: '单价',
    prop: 'price',
    align: 'right',
    width: 100,
    render: (h, { row }) => {
      return h('span', numToLocaleStrFixed2(row.price));
    }
  },
  {
    label: '金额(元)',
    prop: 'totalAmt',
    align: 'right',
    width: 120,
    render: (h, { row }) => {
      return h('span', numToLocaleStrFixed2(row.totalAmt));
    }
  },
  {
    label: '生产批号',
    prop: 'prodNo',
    width: 100,
    align: 'center'
  },
  {
    label: '生产日期',
    prop: 'prodDate',
    align: 'center',
    width: 90
  },
  {
    label: '失效日期',
    prop: 'expireDate',
    align: 'center',
    width: 90
  },
  {
    label: '注册证号',
    prop: 'regNo',
    align: 'center',
    width: 75
  },
  {
    label: '品牌',
    prop: 'brand',
    align: 'center',
    minWidth: 120
  },
  {
    label: '生产厂家',
    prop: 'manufacturerName',
    align: 'center',
    minWidth: 120
  },
  {
    label: '备注',
    prop: 'remark',
    align: 'center',
    minWidth: 120
  }
];
