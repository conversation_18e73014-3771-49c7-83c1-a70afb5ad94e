<template>
  <div class="receipt-details-table-container">
    <ts-search-bar-new
      v-model="searchForm"
      :formList="searchList"
      :resetData="resetData"
      @search="search"
    />

    <ts-vxe-base-table
      :id="tableId"
      class="form-table"
      ref="table"
      minHeight="100%"
      :hasPage="false"
      :columns="columns"
      @refresh="detailsRefresh"
      show-footer
      :footer-data="footerData"
      :footer-cell-class-name="footerCellClassName"
    />
  </div>
</template>

<script>
import Decimal from 'decimal.js';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
import { materialInventoryDic } from '@/views/moduleMaterial/inventoryManagement/config/dic.js';
export default {
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    },
    parentCurrentRecord: {
      type: Function,
      default: () => {}
    },
    handleGetReceiptDetailApi: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      tableId: `receipt-details-table-${materialInventoryDic[this.type] || ''}`,
      resetData: { name: '' },
      searchForm: { name: '' },
      searchList: [
        {
          label: '',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资编码或名称进行搜索'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      footerData: []
    };
  },
  methods: {
    clearTableData() {
      this.$refs.table.refresh([]);
      this.footerData = [];
    },

    search() {
      this.detailsRefresh();
    },

    clearSearchForm() {
      this.searchForm = this.resetData;
    },

    async detailsRefresh() {
      let data = this.parentCurrentRecord();
      if (!data) return;

      try {
        let res = await this.handleGetReceiptDetailApi(
          data.id,
          this.searchForm
        );

        let { obj, list } = this.formatData(res);

        if (Array.isArray(list) && list.length) {
          // 按照物资编码排序
          list.sort((a, b) => a.flowNo.localeCompare(b.flowNo));
          list.forEach((item, index) => {
            item.receiptFlowNo = obj.flowNo;
            item.pageIndex = index + 1;
          });

          // 刷新表格数据
          this.$refs.table.refresh(list);
          // 刷新表格底部数据
          this.footerData = this.handleGetFooterData(list);
        } else {
          this.clearTableData();
        }
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    formatData(res) {
      let keyMap = {
        1: {
          obj: 'inb',
          list: 'inbDtlList'
        },
        2: {
          obj: 'outb',
          list: 'outbDtlList'
        }
      };

      return {
        obj: res[keyMap[this.type].obj],
        list: res[keyMap[this.type].list]
      };
    },

    handleGetFooterData(rows) {
      const calculateTotal = key => {
        return rows
          .map(row => row[key])
          .reduce((prev, curr) => {
            return !isNaN(curr) ? prev.plus(curr) : prev;
          }, new Decimal(0));
      };

      return [
        {
          pageIndex: '合计',
          totalAmt: numToLocaleStrFixed2(calculateTotal('totalAmt')),
          num: calculateTotal('num')
        }
      ];
    },

    footerCellClassName(row) {
      return 'footer-val-styles';
    }
  }
};
</script>

<style lang="scss" scoped>
.receipt-details-table-container {
  height: 249px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid#295cf9;
  border-radius: 4px;
  padding: 8px;
  ::v-deep {
    .footer-val-styles {
      background-color: #eceef3;
      .vxe-cell--item {
        font-weight: bold;
        color: $warning-color;
      }
    }
  }
}
</style>
