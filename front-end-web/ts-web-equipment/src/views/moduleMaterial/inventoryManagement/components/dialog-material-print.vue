<template>
  <vxe-modal
    className="dialog-material-print"
    width="1280"
    height="85%"
    :title="`打印${hospitalName}（${whName}）${printTitle}`"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="material-print-table" ref="PrintContent">
        <div class="hospital-name-print">
          {{ hospitalName }}（{{ whName }}）{{ printTitle }}
        </div>

        <div class="print-table-top">
          <div class="val-row unit">单位(元)</div>
          <template v-if="isStore">
            <div class="between">
              <div class="val-row">
                <span class="label">供货商：</span>
                <span class="val">供货商</span>
              </div>
              <div class="val-row">
                <span class="label">入库类型：</span>
                <span class="val">入库类型</span>
              </div>
            </div>

            <div class="between">
              <div class="val-row">
                <span class="label">入库日期：</span>
                <span class="val">入库日期</span>
              </div>

              <div class="val-row">
                <span class="label">总计：</span>
                <span class="val">
                  1998.00
                </span>
              </div>
            </div>
          </template>

          <template v-if="isOutbound">
            <div class="between">
              <div class="val-row">
                <span class="label">出库类型：</span>
                <span class="val">出库类型</span>
              </div>
              <div class="val-row">
                <span class="label">出库科室：</span>
                <span class="val">出库科室</span>
              </div>
            </div>

            <div class="between">
              <div class="val-row">
                <span class="label">出库日期：</span>
                <span class="val">出库日期</span>
              </div>

              <div class="val-row">
                <span class="label">总计：</span>
                <span class="val">
                  1998.00
                </span>
              </div>
            </div>
          </template>

          <template v-if="isReturnGoods">
            <div class="between">
              <div class="val-row">
                <span class="label">供应商：</span>
                <span class="val">供应商</span>
              </div>
              <div class="val-row">
                <span class="label">退货日期：</span>
                <span class="val">退货日期</span>
              </div>
            </div>

            <div class="between">
              <div class="val-row"></div>

              <div class="val-row">
                <span class="label">总计：</span>
                <span class="val">
                  1998.00
                </span>
              </div>
            </div>
          </template>

          <template v-if="isReturnStore">
            <div class="between">
              <div class="val-row">
                <span class="label">退库科室：</span>
                <span class="val">退库科室</span>
              </div>
              <div class="val-row">
                <span class="label">退库日期：</span>
                <span class="val">退货日期</span>
              </div>
            </div>

            <div class="between">
              <div class="val-row"></div>

              <div class="val-row">
                <span class="label">总计：</span>
                <span class="val">
                  1998.00
                </span>
              </div>
            </div>
          </template>

          <div class="between">
            <div class="val-row">
              <span class="label">备注：</span>
              <span class="val">备注说明</span>
            </div>

            <div class="val-row">
              <span class="label">NO：</span>
              <span class="val">TH202507140005</span>
            </div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th class="w20">物资名称</th>
              <th class="w15">规格型号</th>
              <th class="w15">单位</th>
              <th class="w15">数量</th>
              <th class="w15">单价</th>
              <th class="w15">金额</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in form.list" :key="index">
              <td class="w20">{{ item.name }}</td>
              <td class="w15">{{ item.model }}</td>
              <td class="w15">{{ item.unit }}</td>
              <td class="w15">{{ item.num }}</td>
              <td class="w15 text-r">
                {{ numToLocaleStrFixed2(item.price) }}
              </td>
              <td class="w15 text-r">
                {{ numToLocaleStrFixed2(item.totalAmt) }}
              </td>
            </tr>

            <tr>
              <td class="text-c">
                合计 金额大写：
              </td>
              <td class="text-l" colspan="2">
                {{ dealBigMoney(form.obj?.money) }}
              </td>
              <td class="text-c">
                金额小写：
              </td>
              <td class="text-l" colspan="2">
                ¥{{ Number(form.obj?.money).toLocaleString('zh-CN') }}
              </td>
            </tr>
          </tbody>
        </table>

        <div class="footer-container">
          <div class="val-row">
            <span class="label">制单人：</span>
            <span class="val">制单人</span>
          </div>

          <template v-if="isStore">
            <div class="val-row">
              <span class="label">采购验收：</span>
              <span class="val">采购人</span>
            </div>
            <div class="val-row">
              <span class="label">仓库验收：</span>
              <span class="val">仓库验收</span>
            </div>
            <div class="val-row">
              <span class="label">审核：</span>
              <span class="val">审核人</span>
            </div>
            <div class="val-row">
              <span class="label">验收结论：</span>
              <span class="val">验收结论</span>
            </div>
          </template>

          <template v-if="isOutbound">
            <div class="val-row">
              <span class="label">发货人：</span>
              <span class="val">发货人</span>
            </div>
            <div class="val-row">
              <span class="label">领用人：</span>
              <span class="val">领用人</span>
            </div>
          </template>

          <template v-if="isReturnGoods">
            <div class="val-row">
              <span class="label">审核：</span>
              <span class="val">审核人</span>
            </div>
            <div class="val-row">
              <span class="label">供应商：</span>
              <span class="val">供应商</span>
            </div>
          </template>

          <template v-if="isReturnStore">
            <div class="val-row">
              <span class="label">审核：</span>
              <span class="val">审核人</span>
            </div>
            <div class="val-row">
              <span class="label">退库科室：</span>
              <span class="val">退库科室</span>
            </div>
          </template>

          <div class="val-row">
            <span class="label">打印时间：</span>
            <span class="val">{{ printUserTime }}</span>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <ts-button class="shallowButton" @click="handlePrint">
        打印
      </ts-button>
      <ts-button class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>
<script>
import print from '@/unit/print.js';
import moment from 'moment/moment';

import { dealBigMoney } from '@/util';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
import {
  materialInventoryDic,
  materialPrintDic
} from '@/views/moduleMaterial/inventoryManagement/config/dic.js';
export default {
  props: {
    renderType: {
      type: String,
      default: () => ''
    },
    details: {
      type: Object,
      default: () => {}
    },
    detailsList: {
      type: Array,
      default: () => []
    }
  },
  filters: {
    formatterDate(val) {
      return val ? moment(val).format('YYYY-MM-DD') : '';
    }
  },
  computed: {
    renderTypeDic() {
      return materialInventoryDic[this.renderType] || '';
    },

    isStore() {
      return this.renderTypeDic === materialInventoryDic[1]; // 物资入库
    },
    isOutbound() {
      return this.renderTypeDic === materialInventoryDic[2]; // 物资出库
    },
    isReturnGoods() {
      return this.renderTypeDic === materialInventoryDic[3]; // 物资退货
    },
    isReturnStore() {
      return this.renderTypeDic === materialInventoryDic[4]; // 物资退库
    },

    printTitle() {
      return materialPrintDic[this.renderType] || '';
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,

      hospitalName: '',
      printUserTime: '',

      whName: '', // 库房名称
      form: {} // 单据详情
    };
  },
  methods: {
    dealBigMoney,
    numToLocaleStrFixed2,

    async show({ data, whName }) {
      let parentStoreInfo = this.$getParentStoreInfo();
      let globalSetting = parentStoreInfo?.globalSetting || {};
      this.hospitalName = globalSetting?.webTitle;
      this.printUserTime = moment().format('YYYY-MM-DD HH:mm');

      this.form = data;
      this.whName = whName;
      this.visible = true;
    },

    handlePrint() {
      let dom = document.querySelector('.material-print-table');
      dom.classList.add('print');
      print(this.$refs.PrintContent, {
        mediaPrint: `
          @page { 
            margin: 16px 8px 0 8px !important;
            height: auto !important;
          }
        `
      });

      setTimeout(() => {
        dom.classList.remove('print');
      }, 1000);
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss">
.material-print-table {
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.4);
  padding: 16px 32px;

  .val-row {
    &.unit {
      position: absolute;
      bottom: 0;
      right: 18%;

      text-align: right;
      color: #000;
      font-size: 16px;
      font-family: 'FangSong', 'SimHei', 'KaiTi' !important;
    }

    .label,
    .val {
      color: #000;
      font-size: 16px;
      font-family: 'FangSong', 'SimHei', 'KaiTi' !important;
    }
  }

  &.print {
    box-shadow: none;
    page-break-after: always;
  }

  .hospital-name-print {
    color: #000;
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 6px;
    text-align: center;
    font-family: 'FangSong', 'SimHei', 'KaiTi' !important;
  }

  .print-table-top {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;

    .val {
      display: inline-block;
      min-width: 105px;
    }

    .between {
      display: flex;
      flex-direction: column;

      div {
        margin: 2px 0;
        color: #000;
      }
    }
  }

  table {
    table-layout: fixed;
    width: 100%;
    border: 1px solid #333;
    margin: 8px 0;
    thead {
      height: 24px;
      background: #e8e8e8;
      border-bottom: 1px solid #666;
      th {
        &:last-child {
          border-right: 0px;
        }
        border-right: 1px solid #666;
        text-align: center;
        font-size: 16px;
        color: #000;
        font-family: 'FangSong', 'SimHei', 'KaiTi' !important;
      }
    }

    tbody {
      tr {
        height: 24px;
        border-bottom: 1px solid #666;
        &:last-child {
          border-bottom: 0px;
        }
        td {
          &:last-child {
            border-right: 0px;
          }
          border-right: 1px solid #666;
          overflow: hidden;
          line-height: 16px;
          font-size: 16px;
          padding: 2px;
          color: #000;
          text-align: center;

          word-wrap: break-word;
          word-break: break-all;
          white-space: normal;
          font-family: 'FangSong', 'SimHei', 'KaiTi' !important;
        }
      }
    }
  }

  .w5 {
    width: 10%;
  }

  .w10 {
    width: 10%;
  }

  .w12 {
    width: 12%;
  }

  .w15 {
    width: 15%;
  }

  .w20 {
    width: 20%;
  }

  .text-l {
    text-align: left;
  }

  .text-c {
    text-align: center;
  }
  .text-r {
    text-align: right;
  }

  .flex-end {
    display: flex;
    justify-content: flex-end;
  }

  .as-end {
    align-self: end;
  }

  .footer-container {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
  }
}
</style>
