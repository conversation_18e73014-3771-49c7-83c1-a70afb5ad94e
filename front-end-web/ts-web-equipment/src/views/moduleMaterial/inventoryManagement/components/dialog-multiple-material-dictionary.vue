<template>
  <vxe-modal
    className="dialog-multiple-material-dictionary"
    title="选择物资"
    v-model="visible"
    width="85%"
    height="80%"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <new-base-search-tree
          v-if="visible"
          class="node-tree"
          ref="searchTree"
          title="物资分类"
          :apiFunction="apiFunction"
          :activeId="treeNode ? treeNode.id : ''"
          placeholder="请输入分类名称进行搜索"
          @beforeClick="clickItemTree"
        />

        <div class="content-center">
          <ts-search-bar-new
            v-model="searchForm"
            :formList="searchList"
            @search="search"
          />

          <ts-vxe-base-table
            id="table_material_dictionary"
            class="form-table"
            ref="table"
            auto-resize
            minHeight="100%"
            :columns="columns"
            :pageSizes="[1, 100, 200, 500, 1000]"
            :rowClassName="rowClassName"
            :checkbox-config="{
              reserve: true,
              showHeader: true,
              checkMethod: showCheckboxMethod
            }"
            @selection-change="handleSelectionChange"
            @refresh="handleRefreshTable"
          />
        </div>

        <div class="sel-container">
          <div class="dictionary-container">
            <SelectModule
              :data="selectionList"
              selectTitle="已选物资"
              @handle-clear="handleClearSelectDictionary"
              @handle-delete="handleDeleteSelectDictionary"
            />
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">
          确 定
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import SelectModule from './select-module.vue';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';
export default {
  components: {
    SelectModule
  },
  props: {
    renderType: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入资产名称查询'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],

      visible: false,

      selectionList: [],

      treeNode: null,
      apiFunction: null,
      allId: '99999',
      otherParams: {}
    };
  },
  computed: {
    isStore() {
      return this.renderType === '1'; // 物资入库
    },
    isOutbound() {
      return this.renderType === '2'; // 物资出库
    },
    isReturnGoods() {
      return this.renderType === '3'; // 物资退货
    },
    isReturnStore() {
      return this.renderType === '4'; // 物资退库
    },
    columns() {
      let arr = [
        { type: 'checkbox', width: 50, align: 'center', fixed: 'left' },
        {
          label: '序号',
          prop: 'pageIndex',
          width: 50,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '物资编码',
          align: 'center',
          prop: 'flowNo',
          width: 160,
          fixed: 'left'
        },
        {
          label: '物资名称',
          align: 'center',
          prop: 'name',
          minWidth: 150
        },
        {
          label: '批次号',
          align: 'center',
          prop: 'batchNo',
          width: 135
        },
        {
          label: '库存数量',
          align: 'center',
          prop: 'stock',
          width: 80
        },
        {
          label: '规格型号',
          align: 'center',
          prop: 'model',
          minWidth: 120
        },
        { label: '单位', align: 'center', prop: 'unitShow', width: 70 },
        {
          label: '参考单价',
          align: 'center',
          prop: 'price',
          width: 100,
          render: (h, { row }) => {
            return h('span', null, numToLocaleStrFixed2(row.price));
          }
        },
        { label: '品牌', align: 'center', prop: 'brand', minWidth: 140 },
        {
          label: '生产厂家',
          align: 'center',
          prop: 'manufacturerName',
          width: 140
        },
        { label: '供应商', align: 'center', prop: 'supplyName', minWidth: 140 }
      ];

      if (this.isStore) {
        let filterArr = ['batchNo'];
        arr = arr.filter(item => !filterArr.includes(item.prop));
      }
      return arr;
    }
  },
  methods: {
    rowClassName({ row }) {
      return row.stock == '0' ? 'disabled-row' : '';
    },
    show({ otherParams = {} }) {
      this.otherParams = otherParams;

      this.selectionList = [];
      this.treeNode = null;
      this.apiFunction = this.handleGetTree;

      this.visible = true;
      this.$nextTick(async () => {
        this.$refs.table.pageNo = 1;
        await this.handleRefreshTable();
      });
    },

    showCheckboxMethod({ row }) {
      // 物资出库 库存为0 禁止勾选
      if (this.isOutbound && row.stock == '0') {
        return false;
      }

      return true;
    },

    // 优化后的：提供给树组件的接口
    async handleGetTree() {
      try {
        const res = await this.ajax.warehouseCategoryTreeByWhId(
          this.otherParams.warehouseId
        );
        if (!res.success) {
          this.$newMessage('error', res.message || '获取分类列表失败');
          return {
            success: false,
            object: [],
            statusCode: 500
          };
        }

        const treeData = Array.isArray(res.object) ? res.object : [];
        const allNode = {
          id: this.allId,
          name: '全部',
          open: true,
          children: treeData.map(item => ({
            ...item,
            pid: this.allId
          }))
        };

        // 第一层节点展开
        allNode.children.forEach(item => {
          item.open = true;
        });
        return {
          object: treeData.length ? [allNode] : [],
          success: true,
          statusCode: 200
        };
      } catch (error) {
        this.$newMessage('error', '获取分类列表异常');
        return {
          success: false,
          object: [],
          statusCode: 500
        };
      }
    },

    // 树 item点击
    clickItemTree(select) {
      this.treeNode = select;
      this.search();
    },

    tableInstance() {
      return this.$refs.table.tsVxeTableRef();
    },

    handleSelectionChange(selection) {
      this.selectionList = selection;
    },

    handleDeleteSelectDictionary({ id }) {
      this.tableInstance().setCheckboxRow({ id }, false);
      let index = this.selectionList.findIndex(f => f.id === id);
      this.selectionList.splice(index, 1);
    },

    handleClearSelectDictionary() {
      this.tableInstance().clearCheckboxRow();
      this.tableInstance().clearCheckboxReserve();
      this.selectionList = [];
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let data = {
        ...this.searchForm,
        pageNo,
        pageSize,
        categoryId: this.treeNode?.id || '',
        ...this.otherParams
      };
      if (data.categoryId === this.allId) delete data.categoryId;

      let tableAPI = null;
      if (this.isStore) {
        tableAPI = this.ajax.getMaterialSkuList;
      }
      if (this.isOutbound) {
        tableAPI = this.ajax.getMaterialSkuStock;
      }

      let res = await tableAPI(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '表格数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, index) => {
        return {
          ...item,
          pageIndex: index + 1 + (pageNo - 1) * pageSize
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows: this.formatRows(rows)
      });
    },

    formatRows(rows) {
      if (this.isOutbound) {
        // 物资出库 添加物资 按批次 主键是ID*批次号
        // 生产日期 失效日期 格式化
        rows.forEach(item => {
          item.id = `${item.id}*${item.batchNo}`;
          item.prodDate = this.formatProdDate(item.prodDate);
          item.expireDate = this.formatProdDate(item.expireDate);
        });
      }

      return rows;
    },

    formatProdDate(date) {
      return date ? this.$dayjs(date).format('YYYY-MM-DD') : '';
    },

    submit() {
      this.$emit('submit', this.selectionList || []);
      this.close();
    },

    close() {
      this.selectionList = [];
      this.$refs.table.clearSelection();
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-multiple-material-dictionary {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        > .vxe-modal--content {
          > .content {
            width: 100%;
            height: 100%;

            display: flex;
            gap: 8px;

            .search-tree-box {
              width: 200px;
              min-height: 200px;
              overflow: hidden;
            }

            .content-center {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .form-table {
                flex: 1;
                overflow: hidden;

                .disabled-row {
                  // background-color: #ec7b25 !important;
                }
              }
            }

            .sel-container {
              width: 300px;
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .dictionary-container {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>
