<template>
  <ts-tabs
    v-model="activeValue"
    class="type-tabs-container"
    :type="null"
    @tab-click="handleTabStatusClick"
  >
    <ts-tab-pane v-for="item in warehouseList" :key="item.id" :name="item.id">
      <span slot="label">{{ item.name }}</span>
    </ts-tab-pane>
  </ts-tabs>
</template>

<script>
export default {
  name: 'WarehouseTabs',
  props: {
    value: {
      type: String,
      default: ''
    },
    tabsName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      warehouseList: []
    };
  },
  computed: {
    activeValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('update:tabsName', this.getTabsName());
        this.$emit('input', val);
      }
    }
  },
  methods: {
    getTabsName() {
      return (
        this.warehouseList.find(item => item.id === this.value)?.name || ''
      );
    },

    getStkMtd() {
      return this.warehouseList.find(item => item.id === this.value)?.stkMtd;
    },

    handleTabStatusClick(tab) {
      this.activeValue = tab.name;
      this.$emit('tab-click');
    },

    // 获取所有库房
    async handleGetAllWarehouseList() {
      try {
        const res = await this.ajax.materialWarehouseList({
          pageNo: 1,
          pageSize: 9999,
          status: '1'
        });
        this.warehouseList = Array.isArray(res.rows) ? res.rows : [];

        // 如果当前未选中tab或选中的tab已不存在，则默认选中第一个
        let isNoSelect = !this.value || this.value === '0';
        let isNoExist = !this.warehouseList.some(s => s.id === this.value);

        if (this.warehouseList.length && (isNoSelect || isNoExist)) {
          this.activeValue = this.warehouseList[0].id;
          this.$emit('update:tabsName', this.getTabsName());
        }
      } catch (e) {
        this.$newMessage && this.$newMessage('error', '获取库房列表失败');
        this.warehouseList = [];
      }
    }
  }
};
</script>
