import { request } from '@/api/ajax';

const getMaterialSkuStock = function(params) {
  return request({
    url: `/ts-ams/api/material/sku/stock`,
    method: 'get',
    params
  });
};

// 物料出库列表
const materialOutbList = function(params) {
  return request({
    url: `/ts-ams/api/material/outb/list`,
    method: 'get',
    params
  });
};

// 批量删除出库单据
const materialOutbBatchDelete = function(data) {
  return request({
    url: `/ts-ams/api/material/outb/batch/delete`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

// 批量审核出库单据
const materialOutbBatchConfirm = function(data) {
  return request({
    url: `/ts-ams/api/material/outb/batch/confirm`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

// 批量取消审核出库单据
const mOutbBRollbackConfirm = function(data) {
  return request({
    url: `/ts-ams/api/material/outb/batch/rollback-confirm`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialOutbSave = function(data) {
  return request({
    url: '/ts-ams/api/material/outb/save',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialOutbUpdate = function(data) {
  return request({
    url: '/ts-ams/api/material/outb/update',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialOutbDetail = function(id, params) {
  return request({
    url: `/ts-ams/api/material/outb/${id}`,
    params,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'get'
  });
};

const materialOutbDelete = function(id) {
  return request({
    url: `/ts-ams/api/material/outb/delete/${id}`,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'post'
  });
};

const materialOutbDirection = function(id, type) {
  return request({
    url: `/ts-ams/api/material/outb/${id}?direction=${type}`,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'get'
  });
};

export default {
  getMaterialSkuStock,
  materialOutbList,
  materialOutbSave,
  materialOutbUpdate,
  materialOutbDetail,
  materialOutbDelete,
  materialOutbBatchDelete,
  materialOutbBatchConfirm,
  mOutbBRollbackConfirm,
  materialOutbDirection
};
