<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.trasen</groupId>
		<artifactId>ts-homs</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>ts-homs-Communicate</artifactId>
	<name>ts-homs-Communicate</name>
	<description>数字化运营平台外网访问服务</description>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.build.locales>zh_CN</project.build.locales>
		<project.build.jdk>1.8</project.build.jdk>
		<maven.compiler.source>${project.build.jdk}</maven.compiler.source>
		<maven.compiler.target>$project.build.jdk</maven.compiler.target>
		<plugin.mybatis.generator>1.3.2</plugin.mybatis.generator>
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
		<maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
		<maven-source-plugin.version>3.2.1</maven-source-plugin.version>
		<skipTests>true</skipTests>
	</properties>
	<dependencies>
		<!--业务依赖jar包-->
		<!--<dependency>
			<groupId>cn.trasen</groupId>
			<artifactId>ts-homs-core</artifactId>
		</dependency>-->
		<dependency>
			<groupId>cn.trasen</groupId>
			<artifactId>ts-homs-workflow</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.trasen</groupId>
			<artifactId>ts-homs-feign</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.trasen</groupId>
			<artifactId>ts-homs-message</artifactId>
		</dependency>
		<!--第三方包-->
		<dependency>
			<groupId>commons-dbutils</groupId>
			<artifactId>commons-dbutils</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.15</version>
		</dependency>-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jetty</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
		</dependency>
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>

		<!--<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>-->
		<!--<dependency>
			<groupId>com.ramostear</groupId>
			<artifactId>Happy-Captcha</artifactId>
		</dependency>-->
		<!--人大金仓数据库驱动-->
		<dependency>
			<groupId>com.kingbase8</groupId>
			<artifactId>kingbase8</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.3</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>ts-homs-Communicate</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>/template/**</include>
				</includes>
			</resource>
		</resources>
		<!-- 打包编译 -->
		<plugins>
			<!--<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${project.build.jdk}</source>
					<target>${project.build.jdk}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>analyze</id>
						<goals>
							<goal>analyze-only</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<archive>
						<addMavenDescriptor>false</addMavenDescriptor>
						<manifest>
							<mainClass>cn.trasen.homs.CommsServiceApplication</mainClass>
							<!--是否指定项目classpath下的依赖-->
							<addClasspath>true</addClasspath>
							<!--指定依赖的时候声明前缀-->
							<classpathPrefix>../lib</classpathPrefix>
							<useUniqueVersions>false</useUniqueVersions>
						</manifest>
						<manifestEntries>
							<Class-Path>.</Class-Path>
							<Built-By>${user.name}</Built-By>
							<Build-Jdk>${java.version}</Build-Jdk>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<!-- 拷贝依赖的jar包到lib目录 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>