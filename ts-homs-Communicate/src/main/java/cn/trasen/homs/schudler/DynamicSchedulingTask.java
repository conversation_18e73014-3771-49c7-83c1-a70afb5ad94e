package cn.trasen.homs.schudler;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.bpm.model.WfDefinitionInfo;
import cn.trasen.homs.bpm.service.WorkflowDefinitionService;
import cn.trasen.homs.feign.message.NoticeService;
 
/**
 * 
 * @ClassName: DynamicSchedulingTask
 * @Description: TODO
 * <AUTHOR>
 * @date 2023-07-18 03:15:08
 */
@Component
@EnableScheduling
public class DynamicSchedulingTask implements SchedulingConfigurer {
 
    public static String cron;
    
    @Resource
    private WorkflowDefinitionService workflowDefinitionService;
 
    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        //项目启动时候，会在这里执行一次,从数据库拿到数据库
    	List<WfDefinitionInfo> allDefinitionInfoList = workflowDefinitionService.getAllDefinitionInfoList();
    	
    	for (WfDefinitionInfo wfDefinitionInfo : allDefinitionInfoList) {
    		 	cron = wfDefinitionInfo.getCron();
    		 	if(StringUtils.isNotBlank(cron)){
    		 		Runnable task = () -> {
    		 			if(StringUtils.isBlank(wfDefinitionInfo.getRecipient())){
    		 				wfDefinitionInfo.setRecipient("all");
    		 			}
        	            NoticeReq noticeVo = NoticeReq.builder()
        						.content(wfDefinitionInfo.getMessageContent())
        						.noticeType("3")
        						.subject("流程提醒")
        						.sender("admin")
        						.senderName("admin")
        						.receiver(wfDefinitionInfo.getRecipient())
        						.wxSendType("2")
        						.source("流程提醒")
        						.build();
        				NoticeService.sendNotice(noticeVo);
        	        };
        	        Trigger trigger = (triggerContext) -> {
        	            //任务触发 ，每次触发都会执行这里面的方法一次，重新获取下一次的更新时间
        	            cron = wfDefinitionInfo.getCron();
        	            CronTrigger cronTrigger = new CronTrigger(cron);
        	            Date nextExec = cronTrigger.nextExecutionTime(triggerContext);
        	            return nextExec;
        	        };
        	        scheduledTaskRegistrar.addTriggerTask(task,trigger);
    		 	}
		}
    	
       
    }
}