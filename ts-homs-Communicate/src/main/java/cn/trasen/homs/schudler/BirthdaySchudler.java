//package cn.trasen.homs.schudler;
//
//import java.util.List;
//import java.util.Map;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
//import cn.trasen.homs.message.service.SmsService;
//
//@Service
//public class BirthdaySchudler {
//	
//	private Logger logger = LoggerFactory.getLogger(BirthdaySchudler.class);
//	
//	@Autowired
//	SmsService smsService;
//
//	@Scheduled(cron = "0 0 8 * * ? ")
//	public void pushBirthday() {
//    	 logger.info("=================生日提醒==================");
//    	 List<Birthday> list = BirthdayMessageUtil.pushBirthday();
//    	 for (Birthday map : list) {
//    		 String content = "尊敬的" + map.getName() + "职工：今天是您的生日，在这美好的日子里，我们送上最诚挚的祝福，愿您工作顺利，身体安康，家庭和美，万事顺心！生日快乐！院工会";
//	    	 
//    		 logger.info("生日提醒内容：" + content);
//    		 
//	    	 smsService.sendRequest(content, map.getPhone(), null, null, null, null);
//	    	 
//		}
//    	 logger.info("==================生日提醒结束==================");
//	}
//	
//}
