package cn.trasen.homs.schudler;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.trasen.homs.bean.hrms.Employee;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.DiskUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.email.model.EmailInternal;
import cn.trasen.homs.email.service.EmailInternalService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.oa.HrmClientService;
import cn.trasen.homs.message.model.MessageBasicSetting;
import cn.trasen.homs.message.model.MessageInternal;
import cn.trasen.homs.message.model.MessageInternalStatus;
import cn.trasen.homs.message.model.MessageSysSetting;
import cn.trasen.homs.message.model.TblSmsSent;
import cn.trasen.homs.message.service.ChuangNanSmsService;
import cn.trasen.homs.message.service.MessageBasicSettingService;
import cn.trasen.homs.message.service.MessageInternalService;
import cn.trasen.homs.message.service.MessageInternalStatusService;
import cn.trasen.homs.message.service.MessageSysSettingService;
import cn.trasen.homs.message.service.SysUsageService;
import cn.trasen.homs.push.Contants.Contants;
import cn.trasen.homs.push.dao.CommHolidayYearMapper;
import cn.trasen.homs.push.model.CommHolidayYear;
import cn.trasen.homs.push.model.DruidMonitoringRecords;
import cn.trasen.homs.utils.CommonUtils;
import tk.mybatis.mapper.entity.Example;

@Service
public class JobSchudler {
	
	private Logger logger = LoggerFactory.getLogger(JobSchudler.class);
	
	@Autowired
	private HrmClientService hrmFeignClient;
	
	@Autowired
	private MessageInternalStatusService messageInternalStatusService;
	
	@Autowired
	private ChuangNanSmsService chuangNanSmsService;
	
	@Autowired
	private EmailInternalService emailInternalService;
	
	@Autowired
	private MessageSysSettingService messageSysSettingService;
	
	@Autowired
	private MessageBasicSettingService messageBasicSettingService;
	
	@Autowired
	private MessageInternalService messageInternalService;
	
	@Autowired
	private SysUsageService sysUsageService;
	
	@Value("${smsProxySwitch}")
	String smsProxySwitch;
	
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;
	
	@Autowired
	private CommHolidayYearMapper commHolidayYearMapper;


	/**
	 * 
	 * @Title: tblSmsOutBoxTask
	 * @Description: 发送短信
	 * @param  参数
	 * @return void 返回类型
	 * @throws
	 */
//	FIXME:消息定时任务暂时关停，后续移到专门的定时任务中
	@Scheduled(cron = "0/15 * * * * ? ")
	public void tblSmsOutBoxTask() {
		if(!"1".equals(smsProxySwitch)) return;
		logger.info("=================开始执行定时任务:发送短信==================");
		chuangNanSmsService.sendMessage();
		logger.info("=================发送短信定时任务执行结束==================");
	}
	
	/**
	 * 
	 * @Title: pullReportMessage
	 * @Description: 拉取状态报告
	 * @param  参数
	 * @return void 返回类型
	 * @throws
	 */
	@Scheduled(cron = "0 0/1 * * * ? ")
	public void pullReportMessage() {
		if(!"1".equals(smsProxySwitch)) return;
		logger.info("=================开始执行定时任务:拉取短信状态报告==================");
		chuangNanSmsService.pullReportMessage();
		logger.info("=================拉取短信状态报告定时任务结束==================");
	}
	
	/**
	 * 
	 * @Title: pullUploadMessage
	 * @Description: 拉取上行明细
	 * @param  参数
	 * @return void 返回类型
	 * @throws
	 */
	@Scheduled(cron = "0/30 * * * * ? ")
	public void pullUploadMessage() {
		if(!"1".equals(smsProxySwitch)) return;
		logger.info("=================开始执行定时任务:拉取上行明细==================");
		chuangNanSmsService.pullUploadMessage();
		logger.info("=================拉取上行明细定时任务结束==================");
	}

	/**
	 * 
	 * @Title: smsReset
	 * @Description: 短信数据备份重置
	 * @param  参数
	 * @return void 返回类型
	 * @throws
	 */
	@Scheduled(cron = "0 0 1 * * ? ")
	public void smsReset() {
		if(!"1".equals(smsProxySwitch)) return;
		logger.info("=================开始执行定时任务:短信数据备份重置==================");
		chuangNanSmsService.smsReset();
		logger.info("=================短信数据备份重置定时任务结束==================");
	}
	
	
	/**
	 * 
	 * @Title: pullOutMessageStatus   
	 * @Description: 拉取短信报告状态信息  1分钟执行一次  
	 * @param:     
	 * @return: void  
	 * @author: YueC
	 * @date:   2020年4月14日 下午6:12:34    
	 * @throws
	 */
	@Scheduled(cron = "1 * * * * ?")
	public void pullOutMessageStatus() {
		if(!"1".equals(smsProxySwitch)) return;
		logger.info("=================开始执行定时任务:拉取短信状态报告信息==================");
    	MessageInternalStatus messageInternalStatus = new MessageInternalStatus();
    	messageInternalStatus.setStatus((short) 2);
    	Page page = new Page();
    	page.setPageNo(1);
    	page.setPageSize(20);
    	page.setSidx("SEND_TIME");
    	page.setSord("DESC");
    	List<MessageInternalStatus> statusList = messageInternalStatusService.getDataList(page, messageInternalStatus);
    	
    	
    	PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("N");
		String orgCode = globalSetting.getObject().getOrgCode();
    	
    	 if (statusList.size()>0){
             List<String> statusIdsArray = new ArrayList<>();
             for (MessageInternalStatus status : statusList){
            	 statusIdsArray.add(status.getId());
             }
             if (statusIdsArray.size() > 0){
            	// Result result = messageFeignClient.selectSmsSendList(String.join(",",statusIdsArray));
            	 List<TblSmsSent> list = chuangNanSmsService.selectByApplicationid(statusIdsArray);
            	 
        		// List<LinkedHashMap<String,Object>> resultList = (List<LinkedHashMap<String,Object>>) result.getObject();
                 if (list.size() > 0){
                	 
                	 int failureNumber = 0;
                	 for (TblSmsSent tblSmsSent : list) {
                		 MessageInternalStatus status = new MessageInternalStatus();
                		 status.setId(tblSmsSent.getApplicationid());
                		 String smsstatus = tblSmsSent.getSmsstatus();
                		 status.setReplyContent(tblSmsSent.getStatusDesc());
                		 if("DELIVRD".equalsIgnoreCase(smsstatus) || "cssdeshfly".equals(orgCode)) {//长沙第二福利院直接更新状态成功
                			 status.setStatus((short)7);//成功送达
                		 }else if("提交成功,待对方接收".equalsIgnoreCase(smsstatus)) {
                			 status.setStatus((short)2);//提交成功,待对方接收
                		 }else {
                			 status.setStatus((short)8);//接收失败
                			 status.setReplyContent(smsstatus+":"+tblSmsSent.getStatusDesc());
                			 failureNumber++;
                		 }
                		 status.setReadTime(new Date());
                		 messageInternalStatusService.update(status);
                	 }
                	 
                	 //计算失败率
                	 MessageBasicSetting messageBasicSetting = messageBasicSettingService.selectMessageBasicSetting();
             		 if("1".equals(messageBasicSetting.getFailureSwitch())) {
             			 float failureRate = ((float)failureNumber / (float)list.size()) * 100;
             	   		 if(failureRate >= Long.valueOf(messageBasicSetting.getFailureRate())) {
             	   			messageBasicSetting.setNoticeType("1");//只推送微信消息  不然在欠费的情况下 会一直拉取失败短信重复推送
             	   			messageInternalService.sendMessageWarning(messageBasicSetting,"单次短信发送失败率超过" + messageBasicSetting.getFailureRate() + "%,请知悉！",null);
             			 }
             	   	 }
                 }
             }
             logger.info("=================拉取短信状态报告信息 定时任务执行结束==================");
         }
	}
	
	/**
	 * 
	 * @Title: jobSchudlerRun   
	 * @Description: 系统短信  整点执行
	 * @param:       
	 * @return: void  
	 * @author: YueC
	 * @date:   2020年4月14日 下午6:14:50    
	 * @throws
	 */
	@Scheduled(cron = "0 0 0/1 * * ? ")
	//@Scheduled(cron = "0 0/1 * * * ? ")
	public void messageSysSetting() {
		logger.info("=================开始执行定时任务:系统短信==================");
		
		List<MessageSysSetting> list = messageSysSettingService.selectCurrentTimeData();
		for (MessageSysSetting messageSysSetting : list) {
			List<Employee> emplist = null;
			Employee leaderEmp = null;
			if("生日祝福".equals(messageSysSetting.getMessageType())) {
				PlatformResult<List<Employee>> result = hrmFeignClient.getEmployeeByBirthday();
				emplist = result.getObject();
			}
			
			if("入职纪念日".equals(messageSysSetting.getMessageType())) {
				PlatformResult<List<Employee>> result = hrmFeignClient.getEmployeeByHiredate();
				emplist = result.getObject();
			}
			
			if("值班提醒".equals(messageSysSetting.getMessageType())) {
				 emplist = emailInternalService.getDutyRosterReport();  //查询第二天的行政值班

				//根据配置的值班组查询人员
				List<Employee> dutyRosterReportByLeader = emailInternalService.getDutyRosterReportByLeader();//查询第二天的值班领导
				if(dutyRosterReportByLeader != null && dutyRosterReportByLeader.size() >0){
					leaderEmp = dutyRosterReportByLeader.get(0);
				}
			}
			
			if("值班提醒(自定义)".equals(messageSysSetting.getMessageType())) {
				
				List<Map<String,String>> rosterReportList = new ArrayList<>();
				
				if("-1".equals(messageSysSetting.getSendData())) {
					emplist = emailInternalService.getDutyRosterReportById(messageSysSetting.getDutyRoster(),"1");//查询需要发短信得值班人员
					rosterReportList = emailInternalService.getDutyRosterReportList("1");
				}else {
					emplist = emailInternalService.getDutyRosterReportById(messageSysSetting.getDutyRoster(),messageSysSetting.getSendData());
					rosterReportList = emailInternalService.getDutyRosterReportList(messageSysSetting.getSendData());
				}
				String sendContent = messageSysSetting.getSendContent();
				
				for (Map<String,String>  rosterReport : rosterReportList) {
					String rosterGroup = "（" + rosterReport.get("rosterGroup") + "-姓名）";
					String rosterGroup2 = "（" + rosterReport.get("rosterGroup") + "-电话）";
					if(sendContent.contains(rosterGroup)) {
						sendContent = sendContent.replaceAll(rosterGroup, rosterReport.get("empName"));
					}
					if(sendContent.contains(rosterGroup2)) {
						sendContent = sendContent.replaceAll(rosterGroup2, rosterReport.get("empPhone"));
					}
				}
				messageSysSetting.setSendContent(sendContent);
				
				logger.info("========sendContent========="+ sendContent + "=======sendContent===========");
			}

			if("设置值班".equals(messageSysSetting.getMessageType())) {
				emplist =  emailInternalService.getDutySystemAdmin();//获取所有的值班管理员
			}

			if("志愿者活动".equals(messageSysSetting.getMessageType())) {
				emplist =  emailInternalService.getVoluntarie();  //志愿者活动
				if(!emplist.isEmpty()){
					emplist.forEach(item->{
						Map<String, Object> map = new HashMap<>();
						map.put("志愿者活动信息", item.getRemark()); //名字
						String content = CommonUtils.getTemplateText(map, messageSysSetting.getSendContent());
						MessageInternal record = new MessageInternal();
						record.setMsgType("OA");
						record.setSenderName("admin");
						record.setSenderId("admin");
						record.setCreateDate(new Date());
						record.setCreateUase("admin");
						record.setIsDraft("0");
						record.setTiming("0");
						record.setMobilePhone(item.getEmpPhone());  //设置要发送消息的手机号
						record.setContent(content);
						messageInternalService.save(record);
					});
				}
				break;
			}

			if(null != emplist && emplist.size() > 0) {
				for(int i=0;i<emplist.size();i++) {
					Employee emp = emplist.get(i);
					//emplist.forEach(emp -> {
					if (StringUtils.isNotEmpty(emp.getEmpPhone())) {

						Map<String, Object> map = new HashMap<>();
						map.put("员工姓名", emp.getEmpName()); //名字

						//值班提醒可能用到的值
						if ("值班提醒".equals(messageSysSetting.getMessageType()) || "值班提醒(自定义)".equals(messageSysSetting.getMessageType())) {
							map.put("明天", getLaterDay()); //明天
							if (leaderEmp != null) {
								map.put("值班领导", leaderEmp.getEmpName());
								map.put("领导电话", leaderEmp.getEmpPhone());
							}
						}


						if (StringUtils.isNotBlank(emp.getYearWork())) {
							if ("0".equals(emp.getYearWork())) {
								map.put("工龄", "1"); //工龄
							} else {
								map.put("工龄", emp.getYearWork()); //工龄
							}

						} else {
							map.put("工龄", "1"); //工龄
						}

						if (null != emp.getEmpAge()) {
							map.put("年龄", String.valueOf(emp.getEmpAge())); //年龄
						} else {
							map.put("年龄", "0");
						}
						String content = CommonUtils.getTemplateText(map, messageSysSetting.getSendContent());

						MessageBasicSetting messageBasicSetting = new MessageBasicSetting();
						messageBasicSetting.setNoticeUser(emp.getEmpCode());
						if ("1".equals(smsProxySwitch)) {
							messageBasicSetting.setNoticeType("2");
							messageInternalService.sendMessageWarning(messageBasicSetting, content, messageSysSetting.getMessageType());
						} else {
							messageBasicSetting.setNoticeType("1");
							messageInternalService.sendMessageWarning(messageBasicSetting, content, messageSysSetting.getMessageType());
						}


					}
				}
			//	});
			}
		}
		
		logger.info("=================开始执行定时任务:系统短信结束==================");
	}

	private static String getLaterDay(){
		Calendar calendar = Calendar.getInstance();

		// 增加一天
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		// 获取明天的年、月、日
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1; // 注意月份从0开始，需要加1
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		// 输出明天的日期
		return year + "年" + month + "月" + day+ "日";
	}
	
	/**
	 * 
	 * @Title: sendEmailByTiming
	 * @Description: 定时发送邮箱数据（每分钟执行一次）
	 * @param  参数
	 * @return void 返回类型
	 * 2021年9月7日a
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "1 * * * * ?")
	public void sendEmailByTiming() {
    	 logger.info("=================定时发送邮箱数据 定时任务开始==================");
    	 List<EmailInternal> list =  emailInternalService.selectEmailByTiming();
    	 for (EmailInternal emailInternal : list) {
    		 emailInternal.setIsDraft("0");
    		 emailInternal.setTiming("0");
    		 emailInternal.setSaveToOutbox("on");
    		 emailInternalService.sendEmailInternal(emailInternal);
		 }
    	 logger.info("=================定时发送邮箱数据 定时任务结束==================");
	}
	
	/**
	 * 
	 * @Title: sendMessageByTiming
	 * @Description: 发送短信草稿箱数据 定时任务
	 * @param  参数
	 * @return void 返回类型
	 * 2021年9月23日
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "1 * * * * ?")
	public void sendMessageByTiming() {
    	 logger.info("=================发送短信草稿箱数据 定时任务开始==================");
    	 List<MessageInternal> list =  messageInternalService.selectMessageByTiming();
    	 for (MessageInternal messageInternal : list) {
    		 messageInternal.setIsDraft("0");
    		 messageInternal.setTiming("0");
    		 messageInternalService.save(messageInternal);
		 }
    	 logger.info("==================发送短信草稿箱数据 定时任务结束==================");
	}
	
	/**
	 * 
	 * @Title: pushSysUsage
	 * @Description: 每日零点推送
	 * @param  参数
	 * @return void 返回类型
	 * 2022年1月15日
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "0 0/30 * * * ?")
	public void pushSysUsage() {
    	 logger.info("=================推送系统使用情况 定时任务开始==================");
    	 sysUsageService.pushSysUsage();
    	 logger.info("==================推送系统使用情况 定时任务结束==================");
	}
	
	/**
	 * 获取节假日数据
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	public void getHoliday() {
    	 logger.info("=================获取节假日数据 定时任务开始==================");
    	 
    	 Example example = new Example(CommHolidayYear.class);
 		 Example.Criteria criteria = example.createCriteria();
 		 criteria.andCondition(" DATE_FORMAT(CREATE_DATE,'%Y') = " + DateUtil.year(new Date()));
 		 
    	 List<CommHolidayYear> list = commHolidayYearMapper.selectByExample(example);
    	 
    	 if(list.size() <= 0) {
    		 Map<String,Object> params = new HashMap<>();
        	 params.put("key", "6426106a1a754074725be8f7e71f9a01");
        	 params.put("date", DateUtil.today());
        	 params.put("type", 1);
        	 
        	 String holidayJson = HttpUtil.post("https://apis.tianapi.com/jiejiari/index", params);
        	 
        	 JSONObject holidayObj = JSON.parseObject(holidayJson);
        	 
        	 if("200".equals(holidayObj.getString("code")) && "success".equals(holidayObj.getString("msg"))) {
        		 
        		 JSONObject result = holidayObj.getJSONObject("result");
        		 
        		 JSONArray holidayList = result.getJSONArray("list");
        		 
        		 for (int i = 0; i < holidayList.size(); i++) {
        			 JSONObject holiday = holidayList.getJSONObject(i);
        			 
        			 CommHolidayYear commHolidayYear = new CommHolidayYear();
        			 commHolidayYear.setId(IdGeneraterUtils.nextId());
        			 commHolidayYear.setName(holiday.getString("name"));
        			 commHolidayYear.setVacation(holiday.getString("vacation"));
        			 commHolidayYear.setRemark(holiday.getString("remark"));
        			 
        			 String holidayStr = holiday.getString("holiday").replace("月", "-").replace("号","");
        			 
        			 String[] holidays = holidayStr.split("-");
        			 
        			 String month = StrUtil.padPre(holidays[0], 2, '0');
        			 String day = StrUtil.padPre(holidays[1], 2, '0');
        			 
        			 commHolidayYear.setHoliday(DateUtil.year(new Date()) + "-" + month + "-" + day);
        			 commHolidayYear.setCreateDate(new Date());
        			 
        			 commHolidayYearMapper.insert(commHolidayYear);
    			}
        	 }
    	 }
    	 
    	 Example example2 = new Example(CommHolidayYear.class);
 		 Example.Criteria criteria2 = example2.createCriteria();
 		 criteria2.andCondition(" DATE_FORMAT(CREATE_DATE,'%Y') = " + (DateUtil.year(new Date()) + 1 ));
 		 
    	 List<CommHolidayYear> list2 = commHolidayYearMapper.selectByExample(example2);
    	 
    	 if(list2.size() <= 0) {
    		 Map<String,Object> params = new HashMap<>();
        	 params.put("key", "6426106a1a754074725be8f7e71f9a01");
        	 params.put("date", (DateUtil.year(new Date()) + 1) + "-01" + "-01");
        	 params.put("type", 1);
        	 
        	 String holidayJson = HttpUtil.post("https://apis.tianapi.com/jiejiari/index", params);
        	 
        	 JSONObject holidayObj = JSON.parseObject(holidayJson);
        	 
        	 if("200".equals(holidayObj.getString("code")) && "success".equals(holidayObj.getString("msg"))) {
        		 
        		 JSONObject result = holidayObj.getJSONObject("result");
        		 
        		 JSONArray holidayList = result.getJSONArray("list");
        		 
        		 for (int i = 0; i < holidayList.size(); i++) {
        			 JSONObject holiday = holidayList.getJSONObject(i);
        			 
        			 CommHolidayYear commHolidayYear = new CommHolidayYear();
        			 commHolidayYear.setId(IdGeneraterUtils.nextId());
        			 commHolidayYear.setName(holiday.getString("name"));
        			 commHolidayYear.setVacation(holiday.getString("vacation"));
        			 commHolidayYear.setRemark(holiday.getString("remark"));
        			 
        			 String holidayStr = holiday.getString("holiday").replace("月", "-").replace("号","");
        			 
        			 String[] holidays = holidayStr.split("-");
        			 
        			 String month = StrUtil.padPre(holidays[0], 2, '0');
        			 String day = StrUtil.padPre(holidays[1], 2, '0');
        			 
        			 commHolidayYear.setHoliday((DateUtil.year(new Date()) + 1) + "-" + month + "-" + day);
        			 commHolidayYear.setCreateDate(DateUtil.parse((DateUtil.year(new Date()) + 1) + "-01" + "-01", "yyyy-MM-dd"));
        			 
        			 commHolidayYearMapper.insert(commHolidayYear);
    			}
        	 }
    	 }
    	 
    	 logger.info("==================获取节假日数据 定时任务结束=================");
	}
	
	
	@Scheduled(cron = "0 0 0 18 * ? ")
	public void sendDiskWarning() {
    	 logger.info("=================定时发送磁盘空间不足预警邮件 定时任务开始==================");
    	 
    	 double systemDiskUsage = DiskUtils.getSystemDiskUsage();//系统盘
    	 
    	 double serviceDiskUsage = DiskUtils.getServiceDiskUsage();//系统应用盘
    	 
    	 //double specificDirectoryUsage = DiskUtils.getSpecificDirectoryUsage("");//指定目录
    	 
    	 PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	 GlobalSetting object = globalSetting.getObject();
    	 String diskWarningUserCode = object.getDiskWarningUserCode();
    	 String diskWarningUserName = object.getDiskWarningUserName();
    	 
    	 if(StringUtils.isNotBlank(diskWarningUserCode)) {
    		 
    		 String content = "";
    		 if(systemDiskUsage> 20) {
        		 content = "系统盘磁盘使用占比已达到" + String.format("%.2f", systemDiskUsage) + "%,请知悉！";
        	 }
    		 if(serviceDiskUsage > 20) {
    			 content = "应用盘磁盘使用占比已达到" + String.format("%.2f", serviceDiskUsage) + "%,请知悉！";
    		 }
    		 
    		 if(systemDiskUsage > 20 && serviceDiskUsage > 50) {
    			 content = "系统盘磁盘使用占比已达到" + String.format("%.2f", systemDiskUsage) + "%,应用盘磁盘使用占比已达到 " + String.format("%.2f", serviceDiskUsage)  + "%,请知悉！";
    		 }
    		 
    		 if(StringUtils.isNotBlank(content)) {
    			 emailInternalService.sendEmail("OA磁盘预警提醒","admin","admin",diskWarningUserCode,diskWarningUserName,content);
    		 }
    		
    	 }
    	 
    	 logger.info("=================定时发送磁盘空间不足预警邮件 定时任务结束==================");
	}
	
}
