package cn.trasen.homs.schudler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import com.taobao.api.ApiException;

import cn.trasen.homs.dingtalk.config.AppConfig;
import cn.trasen.homs.dingtalk.service.AccessTokenUtil;
import cn.trasen.homs.utils.DateUtil;

/**
 * 钉钉获取token定时任务
 * 
 * <AUTHOR>
 *
 */
public class DdGetTokenSchudler {


	private Logger logger = LoggerFactory.getLogger(DdGetTokenSchudler.class);

	@Autowired
	AccessTokenUtil accessTokenUtil;
	
    @Autowired
    private AppConfig appConfig;

	@Value("${wxSwitch}")
	private String wxSwitch;
	
	

	/**
	 * 定时获取钉钉AssessToken 每隔1小时获取钉钉的access_token
	 */
	@Scheduled(cron = "0 0 0/1 * * ? ")
	@Async
	public void getDdTokenTask() {
		if ("1".equals(wxSwitch)) {
			// 刷新
			logger.info("--->>>>>-------------获取钉钉token的定时任务开始了：" + DateUtil.getDateTime());
			String accessToken = accessTokenUtil.getAccessToken(appConfig.getAppKey(), appConfig.getAppSecret());
			logger.info("--->>>>>-------------获取钉钉token的定时任务结束了，token：" + accessToken);
		}
	}
}
