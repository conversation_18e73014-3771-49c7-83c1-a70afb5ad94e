package cn.trasen.homs.schudler;


import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.bpm.dao.WfTaskHisMapper;
import cn.trasen.homs.bpm.model.WfInstanceInfo;
import cn.trasen.homs.bpm.model.WfTask;
import cn.trasen.homs.bpm.model.WfTaskHis;
import cn.trasen.homs.bpm.properties.WorkflowAppConfigProperties;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.mapper.ExceptionLogMapper;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.homs.feign.oa.InformationFeignService;

/**
 * @ClassName: TimingInfoJob
 * @Description: 流程定时发送信息Job
 * <AUTHOR>
 * @date 2019年11月6日 下午4:46:16
 */
@Component
public class TimingInfoJob {

	private static final Logger logger = LoggerFactory.getLogger(TimingInfoJob.class);

	@Autowired
	private WorkflowTaskService wfTaskService;

	@Autowired
	private WfTaskHisMapper wfTaskHisMapper;
	
	@Autowired
	private InformationFeignService informationFeignService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Autowired
	private WorkflowAppConfigProperties workflowAppConfigProperties;
	
	@Autowired
	private ExceptionLogMapper exceptionLogMapper;
	
	@Value("${dailyReminder}")
	private String dailyReminder;
	
	@Autowired
	private WorkflowInstanceService workflowInstanceService;
	
	@Autowired
	private GlobalSettingsFeignService globalSettingsService;
	
	// FIXME:工作流日报定时任务临时关掉，后续需移到专有job微服务中
	@Scheduled(cron = "0 0 18 * * ?")
	public void sendDailyReminderData(){
		
		if("1".equals(dailyReminder)){
			List<Map<String, Object>> dailyReminderData = wfTaskService.selectDailyReminderData();
			
			for (Map<String, Object> map : dailyReminderData) {
				StringBuffer content = new StringBuffer();
				content.append("流程日报：尊敬的").append(map.get("username")).append("，您好，今日您收到了").append(map.get("todayDoNum")).append("条待办流程，您已办理");
				content.append(map.get("haveDoneNum")).append("条流程，等待您办理的流程").append(map.get("toDoNum")).append("条，抄送给您的流程");
				content.append(map.get("copyToMeNum")).append("条。");
				
				NoticeReq noticeVo = NoticeReq.builder()
						.content(content.toString())
						.noticeType("3")
						.subject("流程日报")
						.sender("admin")
						.senderName("admin")
						.receiver((String) map.get("usercode"))
						.wxSendType("2")
						.source("流程日报")
						.build();
				 informationFeignService.sendNotice(noticeVo);
			}
		}
	}
	
	/**
	 * 
	 * @MethodName: handleTermRemind
	 * @Description: 办理期限提醒
	 * <AUTHOR> void
	 * @date 2024-03-19 11:41:36
	 */
	@Scheduled(cron = "0 0/1 * * * ? ")
	public void handleTermRemind(){
		
		System.out.println("===========执行办理期限提醒开始===============");
		
		PlatformResult<List<DictItemResp>> dictItemByTypeCode = dictItemFeignService.getDictItemByTypeCode("HANDLE_TERM_REMIND");
		
		if(dictItemByTypeCode.isSuccess()){
			List<DictItemResp> handleTermRemind = dictItemByTypeCode.getObject();
			
			if(CollectionUtils.isNotEmpty(handleTermRemind)){
				for (DictItemResp dictItemResp : handleTermRemind) {
					
					//定义时间
					String itemNameValue = dictItemResp.getItemNameValue();
					
					//当前时间
					String formatNow = DateUtil.format(new Date(), "HH:mm");
					
					if(formatNow.equals(itemNameValue)){
						List<WfInstanceInfo> handleTermRemindList = wfTaskService.selectHandleTermRemind();
						
						Date today = new Date();
						
						for (WfInstanceInfo wfInstanceInfo : handleTermRemindList) {
							
							String _url = workflowAppConfigProperties.getWxLoginUrl() + "pages/workflow/approval-custom-detail?name=approvalToDo&isMobile=true&wfInstId=" + wfInstanceInfo.getWfInstanceId();
							
							Date handleAllottedTime = wfInstanceInfo.getHandleAllottedTime();
							String handleMarkedWords = wfInstanceInfo.getHandleMarkedWords();
							
							StringBuffer content = new StringBuffer();
							
							content.append("您好，").append(wfInstanceInfo.getWorkflowTitle()).append("的流程办理期限为:").append(DateUtil.format(handleAllottedTime, "yyyy-MM-dd"));
							if(today.getTime() > handleAllottedTime.getTime()){
								content.append(",流程已经超期，请及时处理！");
							}else{
								content.append(",流程即将超期，请及时处理！");
							}
							if(StringUtils.isNotBlank(handleMarkedWords)){
								content.append("办理提示：").append(handleMarkedWords);
							}
							 
							 NoticeReq noticeVo = NoticeReq.builder()
										.content(content.toString())
										.noticeType("3")
										.subject("流程办理期限超期提醒")
										.sender("admin")
										.senderName("admin")
										.receiver(wfInstanceInfo.getAssigneeNos())
										.wxSendType("1")
										.url(_url)
										.businessId(wfInstanceInfo.getBusinessId())
										.source("流程管理")
										.toUrl(StringUtils.isNotBlank(workflowAppConfigProperties.getWorkflowMyDealUrl()) ? workflowAppConfigProperties.getWorkflowMyDealUrl() : "/process/index")
										.build();
								NoticeService.sendAsynNotice(noticeVo);
						}
					}
				}
			}
			
		}
		
		System.out.println("===========执行办理期限提醒结束===============");
	}

	
	/**
	 * 是否超过days未审批
	 */
	private boolean isNextDay(Date curDate, Date flowDate, Integer days) {
		if(Objects.isNull(days) || days == 0){
			return false;
		}
		Calendar birth = Calendar.getInstance();
		birth.setTime(flowDate);// 目标时间
		birth.add(Calendar.DAY_OF_MONTH, days);
		flowDate = birth.getTime();
		// 当前时间大于目标时间
		if(compareDate(curDate, flowDate) == 1) {
			return true;
		}
		return false;
	}

	/**
	 * @Title: compareDate
	 * @Description: 比较两个日期的大小
	 * @param DATE1
	 * @param DATE2
	 * @return int   DATE1 > DATE2 = 1; DATE1 < DATE2 = -1; DATE1 =DATE2 = 0  返回类型
	 * @throws
	 */
	private int compareDate(Date DATE1, Date DATE2) {
		//DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm");
		try {
			//Date dt1 = df.parse(DATE1);
			//Date dt2 = df.parse(DATE2);
			if (DATE1.getTime() > DATE2.getTime()) {
				return 1;
			} else if (DATE1.getTime() < DATE2.getTime()) {
				return -1;
			} else {
				return 0;
			}
		} catch (Exception exception) {
			exception.printStackTrace();
		}
		return 0;
	}
	
	@Scheduled(cron = "0/5 * * * * ?")
	public void clearRepeatData(){
		workflowInstanceService.clearRepeatData();
	}
	
	/**
	 * 平江超时提醒
	 */
	@Scheduled(cron = "0 0 0/1 * * ? ")
	public void timeoutRemind(){
		PlatformResult<GlobalSetting> safeGlobalSetting = globalSettingsService.getSafeGlobalSetting("N");
		GlobalSetting globalSetting = safeGlobalSetting.getObject();
		if("pjxdyrmyy".equals(globalSetting.getOrgCode())) {
			List<WfTask> timeoutDataList = workflowInstanceService.timeoutRemind();
			for (WfTask wfTask : timeoutDataList) {
				
				if(wfTask.getTimeout() < 86400) {
					StringBuffer content = new StringBuffer();
					content.append("您有一条"+ wfTask.getWorkflowTitle() + "流程即将办理超时，请尽快审核");
					
					NoticeReq noticeVo = NoticeReq.builder()
							.content(content.toString())
							.noticeType("3")
							.subject("流程办理超期提醒")
							.sender("admin")
							.senderName("admin")
							.receiver(wfTask.getAssigneeNo())
							.wxSendType("2")
							.source("流程办理超期提醒")
							.build();
					informationFeignService.sendNotice(noticeVo);
				}
				
				if(wfTask.getTimeout() >= 86400) {
					workflowInstanceService.insertTaskTimeOut(wfTask);
				}
			}
		}
	}
	
	/**
	 * 计算审批时长 每天凌晨开始执行
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	public void approvalDuration(){
		logger.info("开始计算审批时长-start-approvalDuration");
		List<String> wfInstanceIds = workflowInstanceService.selectAllWfInstanceId();
		
		for (String wfInstanceId : wfInstanceIds) {
			List<WfTaskHis> wfTaskHisList = wfTaskHisMapper.selectByWfInstanceId(wfInstanceId);
			
			boolean isFinish = false;
			
			if(CollectionUtils.isNotEmpty(wfTaskHisList)) {
				int totalDuration = 0;
				for (int i = 0; i < wfTaskHisList.size(); i++) {
					WfTaskHis wfTaskHis = wfTaskHisList.get(i);
					
					if("9999".equals(wfTaskHis.getWfStepId())){
						isFinish = true;
					}
					
					if("1000".equals(wfTaskHis.getWfStepId()) || "9999".equals(wfTaskHis.getWfStepId())) {
						wfTaskHis.setApprovalDuration(0);
						wfTaskHisMapper.updateByPrimaryKeySelective(wfTaskHis);
					}else if(null !=  wfTaskHis.getMultiInstanceType() && (2 == wfTaskHis.getMultiInstanceType() 
							|| 4 == wfTaskHis.getMultiInstanceType() || 5 == wfTaskHis.getMultiInstanceType())){
						 
						Date finishedDateStart = wfTaskHisMapper.selectUpTaskInfo(wfTaskHis.getWfInstanceId(),wfTaskHis.getWfStepId());
						 if(null != finishedDateStart) { 
							 Date finishedDateEnd =wfTaskHis.getFinishedDate();
							long betweenMs = DateUtil.betweenMs(finishedDateStart,finishedDateEnd) / 60000;
							wfTaskHis.setApprovalDuration(Integer.valueOf(String.valueOf(betweenMs)) );
							wfTaskHisMapper.updateByPrimaryKeySelective(wfTaskHis); 
							
							totalDuration += wfTaskHis.getApprovalDuration();
						 }
						 
					} else {
						Date finishedDateStart = wfTaskHisList.get(i-1).getFinishedDate();
						
						if(null != finishedDateStart) {
							Date finishedDateEnd = wfTaskHis.getFinishedDate();
							long betweenMs = DateUtil.betweenMs(finishedDateStart, finishedDateEnd) / 60000;
							wfTaskHis.setApprovalDuration(Integer.valueOf(String.valueOf(betweenMs)) );
							wfTaskHisMapper.updateByPrimaryKeySelective(wfTaskHis);
							
							totalDuration += wfTaskHis.getApprovalDuration();
						}
					}
				}
				
				wfTaskHisMapper.updateTotalDuration(totalDuration,wfInstanceId);
				
			}
			
			if(isFinish) {
				WfInstanceInfo wfInstanceInfo = new WfInstanceInfo();
				wfInstanceInfo.setWfInstanceId(wfInstanceId);
				wfInstanceInfo.setApprovalDuration(1);
				workflowInstanceService.updateByPrimaryKeySelective(wfInstanceInfo);
			}
		}
		
		logger.info("审批时长计算结束-end-approvalDuration");
		
	}

	/**
	 * 清理comm_error_logs日志，每天凌晨0点开始
	 *  0 0 * * *
	 */
	@Scheduled(cron = "0 0 * * * ?")
	public void CleanHistoryErrorLogs() {
		exceptionLogMapper.cleanHistoryErrorLogs();
	}
}
