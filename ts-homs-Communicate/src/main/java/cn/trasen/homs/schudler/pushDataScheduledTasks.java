package cn.trasen.homs.schudler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import cn.trasen.homs.push.service.CommErrorLogsService;
import cn.trasen.homs.push.service.DruidMonitoringRecordsService;
import cn.trasen.homs.push.service.PisZbCollectionService;
import cn.trasen.homs.push.service.PisZbMonitorService;
import cn.trasen.homs.utils.DateUtil;

@Configuration
@EnableScheduling
public class pushDataScheduledTasks {
	
	private Logger logger = LoggerFactory.getLogger(pushDataScheduledTasks.class);
	
	@Autowired
	private CommErrorLogsService commErrorLogsService;
	
	@Autowired
	private PisZbMonitorService pisZbMonitorService;
	
	@Autowired
	private PisZbCollectionService pisZbCollectionService;
	
	@Autowired
	private DruidMonitoringRecordsService  druidMonitoringRecordsService;
	
	
	
	/**
	 * 定时推送错误日志数据，每间隔10分钟推送一次
	 */
	//@Scheduled(cron = "0 0/10 * * * ? ")
	public void pushCommErrorLogs() {
			// 推送错误日志数据
			logger.info("--->>>>>-------------推送OA错误日志数据的定时任务开始了：" + DateUtil.getDateTime());
			commErrorLogsService.pushCommErrorLogs();//推送OA日志数据
			logger.info("--->>>>>-------------推送OA错误日志数据的定时任务结束了："  + DateUtil.getDateTime());
			
			logger.info("--->>>>>-------------推送工单系统错误日志数据的定时任务开始了：" + DateUtil.getDateTime());
			commErrorLogsService.pushWorksheetCommErrorLogs();//推送工单日志数据
			logger.info("--->>>>>-------------推送工单系统错误日志数据的定时任务结束了："  + DateUtil.getDateTime());
	}
	
	
	/**
	 * 定时推送sql监测数据，每间隔10分钟推送一次
	 */
	//@Scheduled(cron = "0 0/10 * * * ? ")
	public void pushDruidMonitoringRecordsData() {
			// 推送sql监测数据
			logger.info("--->>>>>-------------推送sql监测数据的定时任务开始了：" + DateUtil.getDateTime());
			druidMonitoringRecordsService.pushDruidMonitoringRecordsData();//推送OA日志数据
			logger.info("--->>>>>-------------推送sql监测数据的定时任务结束了："  + DateUtil.getDateTime());
	}
	
	
	
	/**
	 * 定时推送决策系统数据，每天凌晨6点推送数据回公司 ,需要添加多数据源(暂时去掉了，再决策系统里推送)
	 */
	/*@Scheduled(cron = "0 0/2 * * * ? ")
	public void pushPisZbData() {
			// 推送错误日志数据
			logger.info("--->>>>>-------------推送JC决策系统数据的定时任务开始了：" + DateUtil.getDateTime());
			pisZbMonitorService.pushInset();//推送同步是否正常完结
			pisZbCollectionService.pushInset();//推送同步数据的指标详情
			logger.info("--->>>>>-------------推送JC决策系统数据的定时任务结束了："  + DateUtil.getDateTime());
	} */
}
