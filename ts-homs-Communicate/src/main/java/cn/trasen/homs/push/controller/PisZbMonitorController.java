package cn.trasen.homs.push.controller;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.push.dao.PisZbMonitorMapper;
import cn.trasen.homs.push.model.CommErrorLogs;
import cn.trasen.homs.push.model.PisZbMonitor;
import cn.trasen.homs.push.service.PisZbMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "PisZbMonitorController")
public class PisZbMonitorController {

	private transient static final Logger logger = LoggerFactory.getLogger(PisZbMonitorController.class);

	@Autowired
	private PisZbMonitorService pisZbMonitorService;
	
	@Autowired
	private PisZbMonitorMapper pisZbMonitorMapper;
	
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/PisZbMonitor/save")
	public PlatformResult<String> savePisZbMonitor(@RequestBody PisZbMonitor record) {
		try {
			pisZbMonitorService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "List新增", notes = "List新增")
	@PostMapping("/PisZbMonitor/pushInset")
	public PlatformResult<String> pushInset(@RequestBody List<PisZbMonitor> records) {
		try {
			if(CollectionUtils.isNotEmpty(records)) {
				for(PisZbMonitor pisZbMonitor : records) {
					PisZbMonitor record =  pisZbMonitorMapper.selectByPrimaryKey(pisZbMonitor.getId());
					if(record !=null) {//已经存在不插入
						pisZbMonitorMapper.updateByPrimaryKeySelective(pisZbMonitor);
					}else{
						pisZbMonitorMapper.insertSelective(pisZbMonitor);
					}
				}
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/PisZbMonitorController/getList")
	public List<PisZbMonitor> getList(PisZbMonitor record) {
		return pisZbMonitorService.getList(record);
	}
	
	/**
	 * @Title selectPisZbCollectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PisZbCollection>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/PisZbMonitorController/getPageList")
	public DataSet<PisZbMonitor> selectPisZbCollectionList(Page page, PisZbMonitor record) {
		return pisZbMonitorService.getDataSetList(page, record);
	}
	
	
	@ApiOperation(value = "pushInsetBase64DataList新增", notes = "pushInsetBase64DataList新增")
	@PostMapping("/PisZbMonitor/pushInsetBase64Data")//pushInsetBase64Data推送数据新增
	public PlatformResult<String> pushInsetBase64Data(@RequestBody String body) {
		try {
			String Base64Data = body.substring(body.indexOf("\"logData\":\"") + 11, body.lastIndexOf("\"}"));
			//System.out.println("接收后解析的字符串: " + Base64Data);
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(Base64Data);
            String jsonData = new String(decodedBytes, StandardCharsets.UTF_8);
            
            ObjectMapper mapper = new ObjectMapper();
            try {
            	//CommErrorLogs obj = mapper.readValue(jsonLogData, CommErrorLogs.class);
            	List<PisZbMonitor> pisZbMonitorList = mapper.readValue(jsonData, new TypeReference<List<PisZbMonitor>>(){});
            	 for (PisZbMonitor record : pisZbMonitorList) {
            		 PisZbMonitor pisZbMonitor =  pisZbMonitorMapper.selectByPrimaryKey(record.getId());
            		 if(pisZbMonitor != null) {//已经存在不新增
            		 }else {
            				pisZbMonitorMapper.insertSelective(record);
            		 }
                 }
            } catch (Exception e) {
                e.printStackTrace();
            }
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
}
