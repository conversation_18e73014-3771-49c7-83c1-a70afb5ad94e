<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.push.dao.PisZbMonitorMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.push.model.PisZbMonitor">
    <result column="id" jdbcType="VARCHAR" property="id" />
     <result column="org_name" jdbcType="VARCHAR" property="orgName" />
      <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
      <result column="content" jdbcType="VARCHAR" property="content" />
       <result column="zb_count" jdbcType="VARCHAR" property="zbCount" />
    <result column="create_date" jdbcType="VARCHAR" property="createDate" />
      <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
      <result column="pdate" jdbcType="VARCHAR" property="pdate" />
  </resultMap>
  
   <select id="getList" parameterType="cn.trasen.homs.push.model.PisZbMonitor" resultType="cn.trasen.homs.push.model.PisZbMonitor">
 	SELECT  *FROM pis_zb_monitor where 1=1 and is_deleted='N'
 		  <if test="orgCode != null and orgCode != ''">
            and  org_code=#{orgCode}
      	  </if> <if test="orgName != null and orgName != ''">
            and  org_name=#{orgName}
      	  </if>  
      	  
      	  order by create_date desc 
  </select>
  
  <select id="selectpushData" parameterType="cn.trasen.homs.push.model.PisZbMonitor" resultType="cn.trasen.homs.push.model.PisZbMonitor">
 	SELECT  *FROM pis_zb_monitor where 1=1 and is_deleted='N' and  pdate  between   DATE_FORMAT(date_add(now(),interval -7 day),'%Y-%m-%d') 
 				AND   DATE_FORMAT(now(),'%Y-%m-%d')  
  </select>
  
</mapper>