<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.push.dao.PisZbCollectionMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.push.model.PisZbCollection">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="zbcode" jdbcType="VARCHAR" property="zbcode" />
    <result column="pname" jdbcType="VARCHAR" property="pname" />
    <result column="pdate" jdbcType="VARCHAR" property="pdate" />
    <result column="pvalue" jdbcType="VARCHAR" property="pvalue" />
    <result column="zb_count" jdbcType="VARCHAR" property="zbCount" />
    <result column="yqid" jdbcType="VARCHAR" property="yqid" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
    <select id="selectZbCollection" parameterType="cn.trasen.homs.push.model.PisZbCollection" resultType="cn.trasen.homs.push.model.PisZbCollection">
 	<!-- SELECT  PDATE,ZBCODE,PNAME,YQID,SUM(PVALUE) PVALUE,count(1) zb_count  FROM CXZB_ALL 
 			WHERE PDATE BETWEEN (select  date_add(wcrq,interval -1 day) from jc_cqrq) 
 					AND   (select  date_add(cqrq,interval -1 day) from  jc_cqrq) 
 					GROUP BY PDATE,ZBCODE,PNAME,YQID -->
		SELECT  PDATE,ZBCODE,PNAME,YQID,SUM(PVALUE) PVALUE,count(1) zb_count  FROM CXZB_ALL 
 				WHERE PDATE  between   DATE_FORMAT(date_add(now(),interval -1 day),'%Y-%m-%d') 
 				AND   DATE_FORMAT(now(),'%Y-%m-%d') GROUP BY PDATE,ZBCODE,PNAME,YQID

  </select>
  
  <select id="selectPushData" parameterType="cn.trasen.homs.push.model.PisZbCollection" resultType="cn.trasen.homs.push.model.PisZbCollection">
 	SELECT  * FROM pis_zb_collection 
 			WHERE   PDATE BETWEEN (select  date_add(wcrq,interval -1 day) from jc_cqrq) 
 					AND   (select  date_add(cqrq,interval -1 day) from  jc_cqrq) 

  </select>
  
</mapper>