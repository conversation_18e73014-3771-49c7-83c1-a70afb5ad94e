package cn.trasen.homs.push.service.impl;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.DynamicDS.TargetDataSource;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.push.Contants.Contants;
import cn.trasen.homs.push.dao.PisZbMonitorMapper;
import cn.trasen.homs.push.model.PisZbMonitor;
import cn.trasen.homs.push.service.PisZbMonitorService;
import tk.mybatis.mapper.entity.Example;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PisZbMonitorServiceImpl implements PisZbMonitorService{

	@Autowired
	private PisZbMonitorMapper pisZbMonitorMapper;
	
	//@Value("${pushDataUrl}")
	private String pushDataUrl = Contants.pushDataUrl;
	
	/*
	 * @Value("${orgName}") private String orgName;
	 */
	/*
	@Value("${pushPisZbMonitorDateUrl}")
	String pushPisZbMonitorDateUrl;
	
	 @Value("${ZbPushSwitch}")
	    String ZbPushSwitch;//1直接推送公司    2前置机服务推送公司
	    */
	
	@Override
	//@TargetDataSource(name = "deanquery")
	public List<PisZbMonitor> getList(PisZbMonitor pisZbMonitor) {
		// TODO Auto-generated method stub
		return pisZbMonitorMapper.getList(pisZbMonitor);
	}

	@Override
	@Transactional(readOnly = false)
	//@TargetDataSource(name = "deanquery")
	public Integer save(PisZbMonitor record) {
		// TODO Auto-generated method stub
		return pisZbMonitorMapper.insertSelective(record);
	}


	@Override
	//@TargetDataSource(name = "deanquery")
	public DataSet<PisZbMonitor> getDataSetList(Page page, PisZbMonitor record) {
			//page.setSidx("pdate");
			//page.setSord("desc");
		// TODO Auto-generated method stub
		Example example = new Example(PisZbMonitor.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getOrgCode())){
			criteria.andEqualTo("orgCode", record.getOrgCode());
		}
		if(StringUtils.isNotBlank(record.getOrgName())){
			criteria.andLike("orgName", "%"+record.getOrgName()+"%");
		}
		if(StringUtils.isNotBlank(record.getContent())){
			criteria.andLike("content", "%"+record.getContent()+"%");
		}
		if(StringUtils.isNotBlank(record.getBeginDate()) && StringUtils.isNotBlank(record.getEndDate())){
			criteria.andBetween("pdate", record.getBeginDate(), record.getEndDate());
		}
		
		List<PisZbMonitor> records = pisZbMonitorMapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@TargetDataSource(name = "deanquery")
	public void pushInset(){
		/* // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化当前日期
        String formattedCurrentDate = currentDate.format(formatter);
        System.out.println("当前日期: " + formattedCurrentDate);
        // 获取前一周的日期
        // Period.between方法计算两个日期之间的时间差，这里我们用当前日期减去7天
        LocalDate oneWeekAgo = currentDate.minus(Period.ofDays(7));
        // 格式化前一周的日期
        String formattedOneWeekAgo = oneWeekAgo.format(formatter);
        System.out.println("前一周的日期: " + formattedOneWeekAgo);
		// TODO Auto-generated method stub
		Example example = new Example(PisZbMonitor.class);
		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		example.createCriteria().andBetween("pdate", formattedCurrentDate , formattedOneWeekAgo);
		List<PisZbMonitor>  records =   pisZbMonitorMapper.selectByExample(example);//查询一周内的数据推送  */
		List<PisZbMonitor>  records =   pisZbMonitorMapper.selectpushData();
		 try {
			 if(CollectionUtils.isNotEmpty(records)) {
					ObjectMapper mapper = new ObjectMapper();
					String jsonString = mapper.writeValueAsString(records);//对象转String
			        // String编码为Base64字符串
			           String Base64Data = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
			           //System.out.println("加密传输的字符串: " + Base64Data);
				        
			           URL url = new URL(pushDataUrl + "/PisZbMonitor/pushInsetBase64Data");
				        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
				        connection.setRequestMethod("POST");
				        connection.setRequestProperty("Content-Type", "application/json");
				        connection.setDoOutput(true);
				        try (OutputStream os = connection.getOutputStream()) {
				            String json = String.format("{\"logData\":\"%s\"}", Base64Data);
				            os.write(json.getBytes(StandardCharsets.UTF_8));
				        }
				        int responseCode = connection.getResponseCode();
				        if (responseCode == HttpURLConnection.HTTP_OK) {
				            System.out.println("数据推送成功");
				        } else {
				            System.out.println("数据推送失败");
				        }
				}
		 } catch (Exception e) {
	            e.printStackTrace();
	        }
		
		
		
	}
	
	/*
	@Override
	@Transactional(readOnly = false)
	@TargetDataSource(name = "deanquery")
	public void pushInset() {
		// TODO Auto-generated method stub
		Example example = new Example(PisZbMonitor.class);
		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		List<PisZbMonitor>  records =   pisZbMonitorMapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(records)) {
			if ("1".equals(ZbPushSwitch)) {
				Object obj = JSONArray.toJSON(records);
				String json = obj.toString();
				String result = HttpRequest.post(pushPisZbMonitorDateUrl + "/PisZbMonitor/pushInset")
						.body(json)
						.execute().body();
			} else if ("2".equals(ZbPushSwitch)) {
				Object obj = JSONArray.toJSON(records);
				String json = obj.toString();
				String result = HttpRequest.post(pushPisZbMonitorDateUrl + "/zbMonitor/push")
						.body(json)
						.execute().body();
			}
		}
		
	}*/

}
