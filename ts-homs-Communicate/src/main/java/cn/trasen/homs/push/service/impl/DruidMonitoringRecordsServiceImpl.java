package cn.trasen.homs.push.service.impl;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.push.Contants.Contants;
import cn.trasen.homs.push.dao.DruidMonitoringRecordsMapper;
import cn.trasen.homs.push.model.DruidMonitoringRecords;
import cn.trasen.homs.push.service.DruidMonitoringRecordsService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName DruidMonitoringRecordsServiceImpl
 * @Description TODO
 * @date 2024��8��20�� ����4:17:39
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DruidMonitoringRecordsServiceImpl implements DruidMonitoringRecordsService {

	private Logger logger = LoggerFactory.getLogger(DruidMonitoringRecordsServiceImpl.class);
	
	@Autowired
	private DruidMonitoringRecordsMapper mapper;
	
	private final  RestTemplate restTemplate;

    public DruidMonitoringRecordsServiceImpl(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }
	
    //@Value("${pushDataUrl}")
	private String pushDataUrl= Contants.pushDataUrl;
    
	@Value("${spring.datasource.druid.stat-view-servlet.login-username}")
	private String druidUserName;
	
	@Value("${spring.datasource.druid.stat-view-servlet.login-password}")
	private String druidPassWord;
	
	@Autowired
	private DiscoveryClient discoveryClient;
    
    @Resource
    private GlobalSettingsFeignService globalSettingsFeignService;
    
	@Transactional(readOnly = false)
	@Override
	public Integer save(DruidMonitoringRecords record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(DruidMonitoringRecords record) {
		record.setUpdateDate(new Date());
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		DruidMonitoringRecords record = new DruidMonitoringRecords();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public DruidMonitoringRecords selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<DruidMonitoringRecords> getDataSetList(Page page, DruidMonitoringRecords record) {
		Example example = new Example(DruidMonitoringRecords.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getOrgCode())){
			criteria.andEqualTo("orgCode", record.getOrgCode());
		}
		if(StringUtils.isNotBlank(record.getOrgName())){
			criteria.andLike("orgName", "%"+record.getOrgName()+"%");
		}
		if(StringUtils.isNotBlank(record.getServiceName())){
			criteria.andLike("serviceName", "%"+record.getServiceName()+"%");
		}
		List<DruidMonitoringRecords> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public void pushDruidMonitoringRecordsData() {
		//String url = "http://**************:9019/ts-oa/druid/sql.json?loginUsername=xtbg&loginPassword=123456@Xtbg"; // 替换为实际URL
		 List<String> services = discoveryClient.getServices();
		// 获取所有服务名称
	        for (String service : services) {
	        	String pushService = service;  //推送服务名称
	        	if("service-center".equals(service) || "ssoservice".equals(service) || "ts-external".equals(service)) {//服务中心不需要
	        	}else {
	        		if("ts-homs-baseservice".equals(service)) {
	        			pushService = "ts-basics-bottom";
	        		}
	        		if("ts-homs-message".equals(service)) {
	        			pushService = "ts-message";
	        		}
		        	String url = "";
		            List<ServiceInstance> instances = discoveryClient.getInstances(service);
			        // 获取特定服务的所有实例
			        for (ServiceInstance instance : instances) {
			        	//druid没有配置allow: IP不能访问,先写死127.0.0.1
			        	//url = instance.getUri().toString();
			        	url = "http://127.0.0.1:" + String.valueOf(instance.getPort());
			        	//url = "http://**************:" + String.valueOf(instance.getPort());
			            //System.out.println("Host: " + instance.getHost() + ", Port: " + instance.getPort() + ", URI: " + instance.getUri());
			        }
			        url  = url + "/" + pushService + "/druid/sql.json?loginUsername="+ druidUserName+ "&loginPassword=" +druidPassWord;
			        logger.info("--->>>>>-------------推送sql数据druid的url："  + url);
			        pushServiceData(url,pushService);
	        	}
	        }
	}

    public void pushServiceData(String url,String pushService) {
        //String url = "http://**************:9019/ts-oa/druid/sql.json?loginUsername=xtbg&loginPassword=123456@Xtbg"; // 替换为你的实际URL
        //String encoding = Base64.getEncoder().encodeToString((username + ":" + password).getBytes(StandardCharsets.UTF_8));
        //HttpHeaders headers = new HttpHeaders();
        //headers.add("Authorization", "Basic " + encoding);
        //headers.setContentType(MediaType.APPLICATION_JSON);
        //return restTemplate.getForObject(url, String.class, headers);
    	PlatformResult<GlobalSetting> resultSetting = globalSettingsFeignService.getSafeGlobalSetting("N");
    	GlobalSetting globalSetting =  new GlobalSetting();
		if(resultSetting.getStatusCode()==200) {
			 globalSetting = resultSetting.getObject();
		}
        try {
		 	List<Map<String,Object>> result = new ArrayList<>();
		 	 HttpHeaders headers = new HttpHeaders();
		 	headers.setContentType(MediaType.APPLICATION_JSON);
		 	 //调用URL获取数据
			String resultJson = restTemplate.getForObject(url, String.class, headers);
			JSONObject jsonObject =	JSONObject.parseObject(resultJson);
			if(null != jsonObject.get("ResultCode")  && "1".equals(String.valueOf(jsonObject.get("ResultCode")))) {
				if(null != jsonObject.get("Content")) {
					JSONArray list = (JSONArray) jsonObject.get("Content");
					for (int i = 0; i < list.size(); i++) {
		       			Map<String,Object> map = new HashMap<>();
		       			//DruidMonitoringRecords druidMonitoringRecords =  new DruidMonitoringRecords(); 
		       			JSONObject druidData = list.getJSONObject(i);
		       			//过滤数据,只推送超过500毫秒数据
		       			if (druidData != null && druidData.get("MaxTimespan") != null && ((Number) druidData.get("MaxTimespan")).intValue() > 500) {
		       				map.put("druidId", druidData.get("ID"));
			       			map.put("name", druidData.get("Name"));
			       			map.put("url", druidData.get("URL"));
			       			map.put("dbType", druidData.get("DbType"));
			       			map.put("dataSource", druidData.get("DataSource"));
			       			map.put("sqlScript", druidData.get("SQL"));
			       			map.put("executeCount", druidData.get("ExecuteCount"));
			       			map.put("totalTime", druidData.get("TotalTime"));
			       			map.put("maxTimespan", druidData.get("MaxTimespan"));
			       			map.put("inTransactionCount", druidData.get("InTransactionCount"));
			       			map.put("errorCount", druidData.get("ErrorCount"));
			       			map.put("effectedRowCount", druidData.get("EffectedRowCount"));
			       			map.put("fetchRowCount", druidData.get("FetchRowCount"));
			       			map.put("runningCount", druidData.get("RunningCount"));
			       			map.put("concurrentMax", druidData.get("ConcurrentMax"));
			       			map.put("histogram", StringUtils.join(druidData.get("Histogram")));
			       			map.put("executeAndResultHoldTimeHistogram", StringUtils.join(druidData.get("ExecuteAndResultHoldTimeHistogram")) );
			       			map.put("fetchRowCountHistogram", StringUtils.join(druidData.get("FetchRowCountHistogram")) );
			       			map.put("effectedRowCountHistogram", StringUtils.join(druidData.get("EffectedRowCountHistogram")) );
			       			map.put("maxTimespanOccurTime", druidData.get("MaxTimespanOccurTime"));
			       			map.put("lastTime", druidData.get("LastTime"));
			       			
			       			map.put("serviceName", pushService);//服务名称
			       			map.put("orgName", globalSetting.getWebTitle());//机构名称
			       			map.put("orgCode", globalSetting.getOrgCode());//机构代码
			       			result.add(map);
		       			}
					}
					
					ObjectMapper mapper = new ObjectMapper();
					String jsonString = mapper.writeValueAsString(result);//对象转String
					//System.out.println("jsonString : " + jsonString);
					
					String Base64Data = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
			        //System.out.println("加密传输的字符串: " + Base64Data);
					
			            URL urlPush = new URL(pushDataUrl + "/api/DruidMonitoringRecords/pushInsetBase64Data");
				        HttpURLConnection connection = (HttpURLConnection) urlPush.openConnection();
				        connection.setRequestMethod("POST");
				        connection.setRequestProperty("Content-Type", "application/json");
				        connection.setDoOutput(true);
				        try (OutputStream os = connection.getOutputStream()) {
				            String json = String.format("{\"logData\":\"%s\"}", Base64Data);
				            os.write(json.getBytes(StandardCharsets.UTF_8));
				        }
				        int responseCode = connection.getResponseCode();
				        if (responseCode == HttpURLConnection.HTTP_OK) {
				            System.out.println("数据推送成功");
				        } else {
				            System.out.println("数据推送失败");
				        }
				}
			}
		  } catch (Exception e) {
              e.printStackTrace();
          }
    }
}
