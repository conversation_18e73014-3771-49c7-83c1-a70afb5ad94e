package cn.trasen.homs.push.service;

import java.util.List;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.push.model.PisZbMonitor;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

public interface PisZbMonitorService {

	Integer save(PisZbMonitor record);
	
	List<PisZbMonitor>  getList(PisZbMonitor record);
	
	void  pushInset();
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PisZbCollection>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	DataSet<PisZbMonitor> getDataSetList(Page page, PisZbMonitor record);

}
