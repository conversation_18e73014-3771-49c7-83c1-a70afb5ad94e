package cn.trasen.homs.push.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import lombok.Getter;
import lombok.Setter;

@Table(name = "toa_pis_zb_monitor")
@Setter
@Getter
public class PisZbMonitor {

	@Id
    private String id;

	@Column(name = "org_code")
    private String orgCode;

	@Column(name = "org_name")
    private String orgName;
    
    private String content;

    @Column(name = "zb_count")
    private String zbCount;
    
    @Column(name = "create_date")
    private String createDate;
    
    @Column(name = "is_deleted")
    private String isDeleted;
    
    @Column(name = "pdate")
    private String pdate;
    
    @Transient
    private String beginDate;//查询条件
    
    @Transient
    private String endDate;//查询条件
}
