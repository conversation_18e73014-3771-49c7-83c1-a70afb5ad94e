package cn.trasen.homs.push.controller;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.push.dao.DruidMonitoringRecordsMapper;
import cn.trasen.homs.push.model.DruidMonitoringRecords;
import cn.trasen.homs.push.model.PisZbCollection;
import cn.trasen.homs.push.service.DruidMonitoringRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName DruidMonitoringRecordsController
 * @Description TODO
 * @date 2024��8��20�� ����4:17:39
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "DruidMonitoringRecordsController")
public class DruidMonitoringRecordsController {

	private transient static final Logger logger = LoggerFactory.getLogger(DruidMonitoringRecordsController.class);

	@Autowired
	private DruidMonitoringRecordsService druidMonitoringRecordsService;
	
	@Autowired
	private DruidMonitoringRecordsMapper druidMonitoringRecordsMapper;
	

	/**
	 * @Title saveDruidMonitoringRecords
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/DruidMonitoringRecords/save")
	public PlatformResult<String> saveDruidMonitoringRecords(@RequestBody DruidMonitoringRecords record) {
		try {
			druidMonitoringRecordsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateDruidMonitoringRecords
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/DruidMonitoringRecords/update")
	public PlatformResult<String> updateDruidMonitoringRecords(@RequestBody DruidMonitoringRecords record) {
		try {
			druidMonitoringRecordsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectDruidMonitoringRecordsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<DruidMonitoringRecords>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/DruidMonitoringRecords/{id}")
	public PlatformResult<DruidMonitoringRecords> selectDruidMonitoringRecordsById(@PathVariable String id) {
		try {
			DruidMonitoringRecords record = druidMonitoringRecordsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteDruidMonitoringRecordsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/DruidMonitoringRecords/delete/{id}")
	public PlatformResult<String> deleteDruidMonitoringRecordsById(@PathVariable String id) {
		try {
			druidMonitoringRecordsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectDruidMonitoringRecordsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<DruidMonitoringRecords>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/DruidMonitoringRecords/list")
	public DataSet<DruidMonitoringRecords> selectDruidMonitoringRecordsList(Page page, DruidMonitoringRecords record) {
		return druidMonitoringRecordsService.getDataSetList(page, record);
	}
	
	
	@ApiOperation(value = "推送新增", notes = "推送新增")
	@PostMapping("/api/DruidMonitoringRecords/pushInsetBase64Data")
	public PlatformResult<String> pushSave(@RequestBody String body) {
		try {
			String encodedLogData = body.substring(body.indexOf("\"logData\":\"") + 11, body.lastIndexOf("\"}"));
			//System.out.println("接收后解析的字符串: " + encodedLogData);
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(encodedLogData);
            String jsonLogData = new String(decodedBytes, StandardCharsets.UTF_8);
            
            ObjectMapper mapper = new ObjectMapper();
            try {
            	//CommErrorLogs obj = mapper.readValue(jsonLogData, CommErrorLogs.class);
            	List<DruidMonitoringRecords> druidMonitoringRecordsList = mapper.readValue(jsonLogData, new TypeReference<List<DruidMonitoringRecords>>(){});
            	 for (DruidMonitoringRecords record : druidMonitoringRecordsList) {
            		 Example example = new Example(DruidMonitoringRecords.class);
            		 Example.Criteria criteria = example.createCriteria();
            		 criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            		 criteria.andEqualTo("orgCode", record.getOrgCode());//机构代码
            		 criteria.andEqualTo("druidId", record.getDruidId());//druidId
            		 criteria.andEqualTo("serviceName", record.getServiceName());//服务名称
            		 List<DruidMonitoringRecords>  records =   druidMonitoringRecordsMapper.selectByExample(example);
            		 //验证数据是否已经同步过
            		 if(CollectionUtils.isNotEmpty(records)) {//已经存在新增
            			 record.setUpdateDate(new Date());//设置更新时间
            			 druidMonitoringRecordsMapper.updateByExampleSelective(record, example);
            		 }else {
            			 druidMonitoringRecordsService.save(record);
            		 }
                 }
            } catch (Exception e) {
                e.printStackTrace();
            }
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
