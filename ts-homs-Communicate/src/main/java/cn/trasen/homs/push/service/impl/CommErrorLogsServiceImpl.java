package cn.trasen.homs.push.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.push.Contants.Contants;
import cn.trasen.homs.push.dao.CommErrorLogsMapper;
import cn.trasen.homs.push.model.CommErrorLogs;
import cn.trasen.homs.push.service.CommErrorLogsService;

/**
 * @ClassName CommErrorLogsServiceImpl
 * @Description TODO
 * @date 2024��5��15�� ����2:15:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommErrorLogsServiceImpl implements CommErrorLogsService {

	@Autowired
	private CommErrorLogsMapper mapper;
	
	//@Value("${pushDataUrl}")
	private String pushDataUrl = Contants.pushDataUrl;
	
    @Resource
    private GlobalSettingsFeignService globalSettingsFeignService;
	

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommErrorLogs record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommErrorLogs record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CommErrorLogs record = new CommErrorLogs();
		record.setId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CommErrorLogs selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommErrorLogs> getDataSetList(Page page, CommErrorLogs record) {
		/*Example example = new Example(CommErrorLogs.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CommErrorLogs> records = mapper.selectByExampleAndRowBounds(example, page);*/
		List<CommErrorLogs> records = mapper.selectPageList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public void pushCommErrorLogs() {
		List<CommErrorLogs> records  =  mapper.selectAllPushData();
		ObjectMapper mapper = new ObjectMapper();
        try {
        	//将对象转换成Json字符串
        	if(CollectionUtils.isNotEmpty(records)) {
        		PlatformResult<GlobalSetting> result = globalSettingsFeignService.getSafeGlobalSetting("N");
        		if(result.getStatusCode()==200) {
        			GlobalSetting globalSetting = result.getObject();
        			if(globalSetting != null) {
        				for(CommErrorLogs commErrorLogs :records) {
        					commErrorLogs.setOrgCode(globalSetting.getWebTitle());
        				}
        			}
        		}
        		
        		   String jsonString = mapper.writeValueAsString(records);
        	         // 将错误日志数据编码为Base64字符串
        	            String encodedLogData = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
        	            //发送请求
        	            sendLogData(encodedLogData);
        				/*
        				 * String result = HttpRequest.post(pushCommErrorLogsUrl +
        				 * "/ts-message/api/CommErrorLogs/pushSave") .body(encodedLogData)
        				 * .execute().body();
        				 */
        	}
        } catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	@Override
	public void pushWorksheetCommErrorLogs() {
		List<CommErrorLogs> records  =  mapper.selectWorksheetAllPushData();
		ObjectMapper mapper = new ObjectMapper();
        try {
        	//将对象转换成Json字符串
        	if(CollectionUtils.isNotEmpty(records)) {
				/*
				 * for(CommErrorLogs commErrorLogs :records) {//添加机构名称
				 * commErrorLogs.setOrgCode(orgName); }
				 */
        		PlatformResult<GlobalSetting> result = globalSettingsFeignService.getSafeGlobalSetting("N");
        		if(result.getStatusCode()==200) {
        			GlobalSetting globalSetting = result.getObject();
        			if(globalSetting != null) {
        				for(CommErrorLogs commErrorLogs :records) {
        					commErrorLogs.setOrgCode(globalSetting.getWebTitle());
        				}
        			}
        		}
        		   String jsonString = mapper.writeValueAsString(records);
        	         // 将错误日志数据编码为Base64字符串
        	            String encodedLogData = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
        	            //发送请求
        	            sendLogData(encodedLogData);
        	}
        } catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	 private void sendLogData(String encodedLogData) throws IOException {
		 	//System.out.println("加密传输的字符串: " + encodedLogData);
	        URL url = new URL(pushDataUrl + "/api/CommErrorLogs/pushSave");
	        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
	        connection.setRequestMethod("POST");
	        connection.setRequestProperty("Content-Type", "application/json");
	        connection.setDoOutput(true);

	        try (OutputStream os = connection.getOutputStream()) {
	            String json = String.format("{\"logData\":\"%s\"}", encodedLogData);
	            os.write(json.getBytes(StandardCharsets.UTF_8));
	        }

	        int responseCode = connection.getResponseCode();
	        if (responseCode == HttpURLConnection.HTTP_OK) {
	            System.out.println("数据推送成功");
	        } else {
	            System.out.println("数据推送失败");
	        }
	    }
}
