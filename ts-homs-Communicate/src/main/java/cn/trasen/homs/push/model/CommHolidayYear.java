package cn.trasen.homs.push.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "comm_holiday_year")
@Setter
@Getter
public class CommHolidayYear {
	
	@Id
	private String id;
	
    @ApiModelProperty(value = "节假日名称")
	private String name;
	
    @ApiModelProperty(value = "节假日数组")
	private String vacation;
	
    @ApiModelProperty(value = "调休日数组")
	private String remark;
    
    @ApiModelProperty(value = "节日日期")
   	private String holiday;
    
    @Column(name = "CREATE_DATE")
	private Date createDate;
    

}
