package cn.trasen.homs.push.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.push.model.DruidMonitoringRecords;

/**
 * @ClassName DruidMonitoringRecordsService
 * @Description TODO
 * @date 2024��8��20�� ����4:17:39
 * <AUTHOR>
 * @version 1.0
 */
public interface DruidMonitoringRecordsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	Integer save(DruidMonitoringRecords record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	Integer update(DruidMonitoringRecords record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return DruidMonitoringRecords
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	DruidMonitoringRecords selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<DruidMonitoringRecords>
	 * @date 2024��8��20�� ����4:17:39
	 * <AUTHOR>
	 */
	DataSet<DruidMonitoringRecords> getDataSetList(Page page, DruidMonitoringRecords record);
	
	void pushDruidMonitoringRecordsData();
}
