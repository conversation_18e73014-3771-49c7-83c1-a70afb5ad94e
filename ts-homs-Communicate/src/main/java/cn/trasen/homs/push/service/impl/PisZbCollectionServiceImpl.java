package cn.trasen.homs.push.service.impl;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.DynamicDS.TargetDataSource;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.push.Contants.Contants;
import cn.trasen.homs.push.dao.PisZbCollectionMapper;
import cn.trasen.homs.push.model.PisZbCollection;
import cn.trasen.homs.push.service.PisZbCollectionService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PisZbCollectionServiceImpl
 * @Description TODO
 * @date 2024��3��12�� ����10:40:49
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PisZbCollectionServiceImpl implements PisZbCollectionService {

	@Autowired
	private PisZbCollectionMapper mapper;
	
	@Autowired
	private PisZbCollectionService pisZbCollectionService;

	//@Value("${pushDataUrl}")
	private String pushDataUrl = Contants.pushDataUrl;
	
	@Resource
	private GlobalSettingsFeignService globalSettingsFeignService;
	
	/*
	@Value("${pushPisZbMonitorDateUrl}")
	String pushPisZbMonitorDateUrl;
	
	 @Value("${ZbPushSwitch}")
	String ZbPushSwitch;//1直接推送公司    2前置机服务推送公司
	 
	 @Value("${PIS_ORG_CODE}")
	 String PIS_ORG_CODE;//机构编码
	 
	 @Value("${PIS_ORG_NAME}")
	 String PIS_ORG_NAME;//机构名称*/
	 
	@Transactional(readOnly = false)
	@Override
	//@TargetDataSource(name = "deanquery")
	public Integer save(PisZbCollection record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		//record.setOrgCode(orgName);
		//record.setOrgName(orgName);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	//@TargetDataSource(name = "deanquery")
	public Integer update(PisZbCollection record) {
		record.setUpdateDate(new Date());
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	//@TargetDataSource(name = "deanquery")
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PisZbCollection record = new PisZbCollection();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	//@TargetDataSource(name = "deanquery")
	public PisZbCollection selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	//@TargetDataSource(name = "deanquery")
	public DataSet<PisZbCollection> getDataSetList(Page page, PisZbCollection record) {
			//page.setSidx("pdate");
			//page.setSord("desc");
		Example example = new Example(PisZbCollection.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		if(StringUtils.isNotBlank(record.getYqid())){
			criteria.andEqualTo("yqid", record.getYqid());
		}
		if(StringUtils.isNotBlank(record.getOrgCode())){
			criteria.andEqualTo("orgCode", record.getOrgCode());
		}
		if(StringUtils.isNotBlank(record.getOrgName())){
			criteria.andLike("orgName", "%"+record.getOrgName()+"%");
		}
		if(StringUtils.isNotBlank(record.getZbcode())){
			criteria.andEqualTo("zbcode", record.getZbcode());
		}
		if(StringUtils.isNotBlank(record.getPname())){
			criteria.andLike("pname", "%"+record.getPname()+"%");
		}
		if(StringUtils.isNotBlank(record.getBeginDate()) && StringUtils.isNotBlank(record.getEndDate())){
			criteria.andBetween("pdate", record.getBeginDate(), record.getEndDate());
		}
		List<PisZbCollection> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@TargetDataSource(name = "deanquery")
	public void pushInset() {
		// TODO Auto-generated method stub
		PisZbCollection record = new PisZbCollection();
		List<PisZbCollection>  records =   mapper.selectZbCollection(record);//查询指标库数据，汇总指标同步前一天数据
		 try {
			 if(CollectionUtils.isNotEmpty(records)) {
					/*
					 * for(PisZbCollection pisZbCollection :records) {//添加机构信息
					 * pisZbCollection.setOrgCode(orgName); pisZbCollection.setOrgName(orgName); }
					 */
				 PlatformResult<GlobalSetting> result = globalSettingsFeignService.getSafeGlobalSetting("N");
	        		if(result.getStatusCode()==200) {
	        			GlobalSetting globalSetting = result.getObject();
	        			if(globalSetting != null) {
	        				for(PisZbCollection pisZbCollection :records) {
	        					pisZbCollection.setOrgCode(globalSetting.getOrgCode());
	        					pisZbCollection.setOrgName(globalSetting.getWebTitle());
	        				}
	        			}
	        		}
					ObjectMapper mapper = new ObjectMapper();
					String jsonString = mapper.writeValueAsString(records);//对象转String
			        // String编码为Base64字符串
			           String Base64Data = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
			           //System.out.println("加密传输的字符串: " + Base64Data);
				        
			           URL url = new URL(pushDataUrl + "/PisZbCollection/pushInsetBase64Data");
				        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
				        connection.setRequestMethod("POST");
				        connection.setRequestProperty("Content-Type", "application/json");
				        connection.setDoOutput(true);
				        try (OutputStream os = connection.getOutputStream()) {
				            String json = String.format("{\"logData\":\"%s\"}", Base64Data);
				            os.write(json.getBytes(StandardCharsets.UTF_8));
				        }
				        int responseCode = connection.getResponseCode();
				        if (responseCode == HttpURLConnection.HTTP_OK) {
				            System.out.println("数据推送成功");
				        } else {
				            System.out.println("数据推送失败");
				        }
				}
		 } catch (Exception e) {
	            e.printStackTrace();
	        }
	}

	/*
	@Override
	@Transactional(readOnly = false)
	@TargetDataSource(name = "deanquery")
	public void pushInset() {
		// TODO Auto-generated method stub
		PisZbCollection record = new PisZbCollection();
		List<PisZbCollection>  pisZbCollectionList =   mapper.selectZbCollection(record);//查询指标库数据，汇总指标同步情况
		if(CollectionUtils.isNotEmpty(pisZbCollectionList)) {
			for(PisZbCollection pisZbCollection : pisZbCollectionList) {
				pisZbCollectionService.save(pisZbCollection);
			}
		}
				List<PisZbCollection>  records =   mapper.selectPushData(record);//查询表数据推送公司
				if(CollectionUtils.isNotEmpty(records)) {
					if ("1".equals(ZbPushSwitch)) {
						Object obj = JSONArray.toJSON(records);//直接调用公司服务
						String json = obj.toString();
						String result = HttpRequest.post(pushPisZbMonitorDateUrl + "/PisZbCollection/pushInset")
								.body(json)
								.execute().body();
					} else if ("2".equals(ZbPushSwitch)) {//痛过前置机服务推送到公司
						Object obj = JSONArray.toJSON(records);
						String json = obj.toString();
						String result = HttpRequest.post(pushPisZbMonitorDateUrl + "/zbCollection/push")
								.body(json)
								.execute().body();
					}
				}
				
		
	}
	*/
}
