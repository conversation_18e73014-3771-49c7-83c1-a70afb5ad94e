package cn.trasen.homs.push.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "toa_pis_zb_collection")
@Setter
@Getter
public class PisZbCollection {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 机构编码
     */
    @Column(name = "org_code")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 指标CODE
     */
    @ApiModelProperty(value = "指标CODE")
    private String zbcode;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String pname;

    /**
     * 指标产生日期
     */
    @ApiModelProperty(value = "指标产生日期")
    private String pdate;

    /**
     * 指标汇总值
     */
    @ApiModelProperty(value = "指标汇总值")
    private String pvalue;

    /**
     * 指标产生数据条数
     */
    @Column(name = "zb_count")
    @ApiModelProperty(value = "指标产生数据条数")
    private String zbCount;

    /**
     * 院区ID
     */
    @ApiModelProperty(value = "院区ID")
    private String yqid;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 删除标志
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标志")
    private String isDeleted;
    
    @Transient
    private String beginDate;//查询条件
    
    @Transient
    private String endDate;//查询条件
}