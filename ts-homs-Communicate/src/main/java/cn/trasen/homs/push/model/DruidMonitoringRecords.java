package cn.trasen.homs.push.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_druid_monitoring_records")
@Setter
@Getter
public class DruidMonitoringRecords {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * druidID
     */
    @Column(name = "druid_id")
    @ApiModelProperty(value = "druidID")
    private String druidId;

    /**
     * 数据源标识
     */
    @ApiModelProperty(value = "数据源标识")
    private String name;

    /**
     * url
     */
    @ApiModelProperty(value = "url")
    private String url;

    /**
     * 数据库类型
     */
    @Column(name = "db_type")
    @ApiModelProperty(value = "数据库类型")
    private String dbType;

    /**
     * 数据源
     */
    @Column(name = "data_source")
    @ApiModelProperty(value = "数据源")
    private String dataSource;

    /**
     * 执行数
     */
    @Column(name = "execute_count")
    @ApiModelProperty(value = "执行数")
    private Integer executeCount;

    /**
     * 事务运行次数
     */
    @Column(name = "in_transaction_count")
    @ApiModelProperty(value = "事务运行次数")
    private Integer inTransactionCount;

    /**
     * 错误数
     */
    @Column(name = "error_count")
    @ApiModelProperty(value = "错误数")
    private Integer errorCount;

    /**
     * 更新行数
     */
    @Column(name = "effected_row_count")
    @ApiModelProperty(value = "更新行数")
    private Integer effectedRowCount;

    /**
     * 读取行数
     */
    @Column(name = "fetch_row_count")
    @ApiModelProperty(value = "读取行数")
    private Integer fetchRowCount;

    /**
     * 执行中
     */
    @Column(name = "running_count")
    @ApiModelProperty(value = "执行中")
    private Integer runningCount;

    /**
     * 最大并发
     */
    @Column(name = "concurrent_max")
    @ApiModelProperty(value = "最大并发")
    private Integer concurrentMax;

    /**
     * 执行时间分布
     */
    @ApiModelProperty(value = "执行时间分布")
    private String histogram;

    /**
     * 执行+RS时分布
     */
    @Column(name = "execute_and_result_hold_time_histogram")
    @ApiModelProperty(value = "执行+RS时分布")
    private String executeAndResultHoldTimeHistogram;

    /**
     * 读取行分布
     */
    @Column(name = "fetch_row_count_histogram")
    @ApiModelProperty(value = "读取行分布")
    private String fetchRowCountHistogram;

    /**
     * 更新行分布
     */
    @Column(name = "effected_row_count_histogram")
    @ApiModelProperty(value = "更新行分布")
    private String effectedRowCountHistogram;

    /**
     * 最后更新时间
     */
    @Column(name = "max_timespan_occur_time")
    @ApiModelProperty(value = "最后更新时间")
    private String maxTimespanOccurTime;

    /**
     * 最后使用时间
     */
    @Column(name = "last_time")
    @ApiModelProperty(value = "最后使用时间")
    private String lastTime;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 服务名称
     */
    @Column(name = "service_name")
    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    /**
     * 机构编码
     */
    @Column(name = "org_code")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * sql脚本
     */
    @Column(name = "sql_script")
    @ApiModelProperty(value = "sql脚本")
    private String sqlScript;

    /**
     * 执行时间毫秒
     */
    @Column(name = "total_time")
    @ApiModelProperty(value = "执行时间毫秒")
    private Long totalTime;

    /**
     * 最慢耗时
     */
    @Column(name = "max_timespan")
    @ApiModelProperty(value = "最慢耗时")
    private Long maxTimespan;
}