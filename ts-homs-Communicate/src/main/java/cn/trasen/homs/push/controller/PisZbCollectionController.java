package cn.trasen.homs.push.controller;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.push.dao.PisZbCollectionMapper;
import cn.trasen.homs.push.model.PisZbCollection;
import cn.trasen.homs.push.service.PisZbCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PisZbCollectionController
 * @Description TODO
 * @date 2024��3��12�� ����10:40:49
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PisZbCollectionController")
public class PisZbCollectionController {

	private transient static final Logger logger = LoggerFactory.getLogger(PisZbCollectionController.class);

	@Autowired
	private PisZbCollectionService pisZbCollectionService;
	
	@Autowired
	private PisZbCollectionMapper pisZbCollectionMapper;

	/**
	 * @Title savePisZbCollection
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/PisZbCollection/save")
	public PlatformResult<String> savePisZbCollection(@RequestBody PisZbCollection record) {
		try {
			pisZbCollectionService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePisZbCollection
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/PisZbCollection/update")
	public PlatformResult<String> updatePisZbCollection(@RequestBody PisZbCollection record) {
		try {
			pisZbCollectionService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPisZbCollectionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PisZbCollection>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/PisZbCollection/{id}")
	public PlatformResult<PisZbCollection> selectPisZbCollectionById(@PathVariable String id) {
		try {
			PisZbCollection record = pisZbCollectionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePisZbCollectionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/PisZbCollection/delete/{id}")
	public PlatformResult<String> deletePisZbCollectionById(@PathVariable String id) {
		try {
			pisZbCollectionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPisZbCollectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PisZbCollection>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/PisZbCollection/list")
	public DataSet<PisZbCollection> selectPisZbCollectionList(Page page, PisZbCollection record) {
		return pisZbCollectionService.getDataSetList(page, record);
	}
	
	
	
	@ApiOperation(value = "List新增", notes = "List新增")
	@PostMapping("/PisZbCollection/pushInset")//推送数据新增
	public PlatformResult<String> pushInset(@RequestBody List<PisZbCollection> records) {
		try {
			if(CollectionUtils.isNotEmpty(records)) {
				for(PisZbCollection pisZbCollection : records) {
					PisZbCollection record =  pisZbCollectionMapper.selectByPrimaryKey(pisZbCollection.getId());
					if(record !=null) {//已经存在--更新
						pisZbCollectionMapper.updateByPrimaryKeySelective(pisZbCollection);
					}else{
						pisZbCollectionMapper.insertSelective(pisZbCollection);
					}
				}
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "pushInsetBase64DataList新增", notes = "pushInsetBase64DataList新增")
	@PostMapping("/PisZbCollection/pushInsetBase64Data")//pushInsetBase64Data推送数据新增
	public PlatformResult<String> pushInsetBase64Data(@RequestBody String body) {
		try {
			String Base64Data = body.substring(body.indexOf("\"logData\":\"") + 11, body.lastIndexOf("\"}"));
			//System.out.println("接收后解析的字符串: " + Base64Data);
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(Base64Data);
            String jsonData = new String(decodedBytes, StandardCharsets.UTF_8);
            
            ObjectMapper mapper = new ObjectMapper();
            try {
            	//CommErrorLogs obj = mapper.readValue(jsonLogData, CommErrorLogs.class);
            	List<PisZbCollection> pisZbCollectionList = mapper.readValue(jsonData, new TypeReference<List<PisZbCollection>>(){});
            	 for (PisZbCollection record : pisZbCollectionList) {
            		 //PisZbCollection pisZbCollection =  pisZbCollectionMapper.selectByPrimaryKey(record.getId());//定时任务产生了,数据可能会重复产生;直接汇总的无ID
            		 Example example = new Example(PisZbCollection.class);
            		 Example.Criteria criteria = example.createCriteria();
            		 criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            		 criteria.andEqualTo("orgCode", record.getOrgCode());
            		 criteria.andEqualTo("zbcode", record.getZbcode());
            		 criteria.andEqualTo("pdate", record.getPdate());
            		 criteria.andEqualTo("yqid", record.getYqid());
            		 List<PisZbCollection>  records =   pisZbCollectionMapper.selectByExample(example);
            		 if(CollectionUtils.isNotEmpty(records)) {//已经存在不新增
            		 }else {
            			 pisZbCollectionService.save(record);
            		 }
                 }
            } catch (Exception e) {
                e.printStackTrace();
            }
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
