package cn.trasen.homs.push.controller;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.push.dao.CommErrorLogsMapper;
import cn.trasen.homs.push.model.CommErrorLogs;
import cn.trasen.homs.push.service.CommErrorLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommErrorLogsController
 * @Description TODO
 * @date 2024��5��15�� ����2:15:31
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CommErrorLogsController")
public class CommErrorLogsController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommErrorLogsController.class);

	@Autowired
	private CommErrorLogsService commErrorLogsService;
	
	@Autowired
	private CommErrorLogsMapper commErrorLogsMapper;

	@ApiOperation(value = "推送新增", notes = "推送新增")
	@PostMapping("/api/CommErrorLogs/pushSave")
	public PlatformResult<String> pushSave(@RequestBody String body) {
		try {
			String encodedLogData = body.substring(body.indexOf("\"logData\":\"") + 11, body.lastIndexOf("\"}"));
			//System.out.println("接收后解析的字符串: " + encodedLogData);
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(encodedLogData);
            String jsonLogData = new String(decodedBytes, StandardCharsets.UTF_8);
            
            ObjectMapper mapper = new ObjectMapper();
            try {
            	//CommErrorLogs obj = mapper.readValue(jsonLogData, CommErrorLogs.class);
            	List<CommErrorLogs> commErrorLogsList = mapper.readValue(jsonLogData, new TypeReference<List<CommErrorLogs>>(){});
            	 for (CommErrorLogs record : commErrorLogsList) {
            		 CommErrorLogs commErrorLogs = commErrorLogsService.selectById(record.getId());
            		 if(commErrorLogs != null) {//已经存在不新增
            		 }else {
            			 record.setIsSync("Y");
            			 commErrorLogsMapper.insertSelective(record);
            		 }
                 }
            } catch (Exception e) {
                e.printStackTrace();
            }
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title saveCommErrorLogs
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CommErrorLogs/save")
	public PlatformResult<String> saveCommErrorLogs(@RequestBody CommErrorLogs record) {
		try {
			commErrorLogsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	
	/**
	 * @Title updateCommErrorLogs
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CommErrorLogs/update")
	public PlatformResult<String> updateCommErrorLogs(@RequestBody CommErrorLogs record) {
		try {
			commErrorLogsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommErrorLogsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommErrorLogs>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CommErrorLogs/{id}")
	public PlatformResult<CommErrorLogs> selectCommErrorLogsById(@PathVariable String id) {
		try {
			CommErrorLogs record = commErrorLogsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommErrorLogsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CommErrorLogs/delete/{id}")
	public PlatformResult<String> deleteCommErrorLogsById(@PathVariable String id) {
		try {
			commErrorLogsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommErrorLogsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommErrorLogs>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CommErrorLogs/list")
	public DataSet<CommErrorLogs> selectCommErrorLogsList(Page page, CommErrorLogs record) {
		return commErrorLogsService.getDataSetList(page, record);
	}
}
