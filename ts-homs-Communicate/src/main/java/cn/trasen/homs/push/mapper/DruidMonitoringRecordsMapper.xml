<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.push.dao.DruidMonitoringRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.push.model.DruidMonitoringRecords">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="druid_id" jdbcType="VARCHAR" property="druidId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="db_type" jdbcType="VARCHAR" property="dbType" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="execute_count" jdbcType="INTEGER" property="executeCount" />
    <result column="in_transaction_count" jdbcType="INTEGER" property="inTransactionCount" />
    <result column="error_count" jdbcType="INTEGER" property="errorCount" />
    <result column="effected_row_count" jdbcType="INTEGER" property="effectedRowCount" />
    <result column="fetch_row_count" jdbcType="INTEGER" property="fetchRowCount" />
    <result column="running_count" jdbcType="INTEGER" property="runningCount" />
    <result column="concurrent_max" jdbcType="INTEGER" property="concurrentMax" />
    <result column="histogram" jdbcType="VARCHAR" property="histogram" />
    <result column="execute_and_result_hold_time_histogram" jdbcType="VARCHAR" property="executeAndResultHoldTimeHistogram" />
    <result column="fetch_row_count_histogram" jdbcType="VARCHAR" property="fetchRowCountHistogram" />
    <result column="effected_row_count_histogram" jdbcType="VARCHAR" property="effectedRowCountHistogram" />
    <result column="max_timespan_occur_time" jdbcType="VARCHAR" property="maxTimespanOccurTime" />
    <result column="last_time" jdbcType="VARCHAR" property="lastTime" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="sql_script" jdbcType="LONGVARCHAR" property="sqlScript" />
    <result column="total_time" jdbcType="BIGINT" property="totalTime" />
    <result column="max_timespan" jdbcType="BIGINT" property="maxTimespan" />
  </resultMap>
</mapper>