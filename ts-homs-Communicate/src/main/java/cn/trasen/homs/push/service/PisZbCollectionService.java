package cn.trasen.homs.push.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.push.model.PisZbCollection;

/**
 * @ClassName PisZbCollectionService
 * @Description TODO
 * @date 2024��3��12�� ����10:40:49
 * <AUTHOR>
 * @version 1.0
 */
public interface PisZbCollectionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	Integer save(PisZbCollection record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	Integer update(PisZbCollection record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PisZbCollection
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	PisZbCollection selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PisZbCollection>
	 * @date 2024��3��12�� ����10:40:49
	 * <AUTHOR>
	 */
	DataSet<PisZbCollection> getDataSetList(Page page, PisZbCollection record);
	
	void  pushInset();
}
