package cn.trasen.homs.SQLToAPI.permission.interceptor;

import java.sql.Connection;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;

import cn.trasen.homs.SQLToAPI.permission.DataScopeContants;
import cn.trasen.homs.SQLToAPI.permission.DataScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.ITableFieldConditionDecision;
import cn.trasen.homs.SQLToAPI.permission.MyPermissionConfig;
import cn.trasen.homs.SQLToAPI.permission.PermissionUtil;
import cn.trasen.homs.SQLToAPI.permission.SpringUtil;
import cn.trasen.homs.SQLToAPI.permission.SqlConditionHelper;
import cn.trasen.homs.SQLToAPI.permission.model.DataScopeModel;

/**
 * <AUTHOR>
 * @ClassName DataScopeInterceptor
 * @Description DataScopeInterceptor
 * @date 2020年4月26日 下午10:04:12
 */
@Intercepts(value = {
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class DataScopeInterceptor implements Interceptor {

    private Logger logger = LoggerFactory.getLogger(DataScopeInterceptor.class);

    private SqlConditionHelper conditionHelper;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        DataScopeModel dataScopeModel = DataScopeContextHolder.get();
        if (null == dataScopeModel || dataScopeModel.isHasAllData()) {
            return invocation.proceed();
        }

        Map<String, String> tableMap = ((MyPermissionConfig) SpringUtil.getBean("myPermissionConfig")).getTableMap();
        Map<String, String> tablePatternMap = ((MyPermissionConfig) SpringUtil.getBean("myPermissionConfig")).getTablePatternMap();
        if (tableMap.size() < 1 && tablePatternMap.size() < 1) {
            return invocation.proceed();
        }

        final Object[] queryArgs = invocation.getArgs();
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaStatementHandler = SystemMetaObject.forObject(statementHandler);
        Object object = null;
        // 分离代理对象链
        while (metaStatementHandler.hasGetter("h")) {
            object = metaStatementHandler.getValue("h");
            metaStatementHandler = SystemMetaObject.forObject(object);
        }
        // 分离最后一个代理对象的目标类
        while (metaStatementHandler.hasGetter("target")) {
            object = metaStatementHandler.getValue("target");
            metaStatementHandler = SystemMetaObject.forObject(object);
        }
        if (object != null) {
            statementHandler = (StatementHandler) object;
        }

        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();
        if (sql.contains("NEXTVAL")) {
            return invocation.proceed();
        }

        // 初步判读是否要权限处理。。 过滤掉不必要的处理
        boolean flag = true;
        for (Map.Entry<String, String> entry : tableMap.entrySet()) {
            if (sql.toUpperCase().contains(entry.getKey().toUpperCase())) {
                flag = false;
            }
        }

        Pattern p;
        Matcher m;
        String regex;
        for (Map.Entry<String, String> entry : tablePatternMap.entrySet()) {
            regex = entry.getValue();
            if (regex.startsWith("^")) {
                regex = regex.substring(1);
            }
            if (regex.endsWith("$")) {
                regex = regex.substring(0, regex.length() - 1);
            }
            p = Pattern.compile(regex);
            m = p.matcher(sql);
            if (m.find()) {
                flag = false;
            }
        }

        if (flag) {
            return invocation.proceed();
        }

        Connection connection = (Connection) queryArgs[0];
        String dialect = connection.getMetaData().getDatabaseProductName();
        // 处理SQL权限对象
        sql = handleSqlDataScope(sql);

        String newSql = "";
        if (!dataScopeModel.isHasVisitData()) {
            logger.debug("===Interceptor== no Visit Data  没有数据访问权限");
            newSql = "select * from (" + sql + ") ttt where 1=2";
        } else {
            newSql = addDataScopeCondition(sql, dialect.toLowerCase());
        }
        logger.debug("数据权限拼接sql:{}" , newSql);
        metaStatementHandler.setValue("delegate.boundSql.sql", newSql);

        return invocation.proceed();
    }

    /**
     * @param customSql
     * @Title handleSqlDataScope
     * @Description 处理SQL数据权限
     * @date 2020年5月16日 上午10:04:47
     * <AUTHOR>
     */
    public String handleSqlDataScope(final String customSql) {
        DataScopeModel dataScopeModel = DataScopeContextHolder.get();
        if (dataScopeModel != null && StringUtils.isNotBlank(customSql)) {
            // 判斷SQL是否包含权限对象
            boolean isMatch = Pattern.matches(DataScopeContants.REGEX, customSql.replaceAll("(\r\n|\n)", "<br/>"));
            if (isMatch) {
                Pattern pat = Pattern.compile(DataScopeContants.REGEX);
                Matcher mat = pat.matcher(customSql);
                // 获取当前人数据权限范围
                // 这个与sysCode是一样的
                String dataScope = dataScopeModel.getDataScope();
                if (StringUtils.isNotBlank(dataScope)) {
                    Set<String> set = new HashSet<>(Arrays.asList(dataScope.split(",")));
                    // 匹配配置的权限是否包含当前人员权限范围中
                    // 如果包含权限范围表示全部数据
                    while (mat.find()) {
                        String customDataScope = mat.group(0);
                        if (StringUtils.isNotBlank(customDataScope)) {
                            // 所有数据权限
                            if (customDataScope.contains(DataScopeContants.DATA_ALL)) {
                                dataScopeModel.setHasAllData(Boolean.TRUE);
                                break;
                            } else {
                                for (String temp : set) {
                                    if (StringUtils.isNotBlank(temp) && customDataScope.contains(temp)) {
                                        dataScopeModel.setHasAllData(Boolean.TRUE);
                                        break;
                                    }
                                }
                            }
                        }
                        break;
                    }
                } else {
                    // 判断没有角色处理
                    while (mat.find()) {
                        String customDataScope = mat.group(0);
                        if (StringUtils.isNotBlank(customDataScope)) {
                            // 所有数据权限
                            if (customDataScope.contains(DataScopeContants.DATA_ALL)) {
                                dataScopeModel.setHasAllData(Boolean.TRUE);
                                break;
                            }
                        }
                        break;
                    }
                }
                // return customSql.replace(DataScopeContants.REGEX, "");
                return customSql;
            }
        }
        return customSql;
    }

    private String addDataScopeCondition(String sql, String dialect) {
        List<SQLStatement> statementList = null;
        try {
            statementList = SQLUtils.parseStatements(sql, dialect);
        } catch (Exception e) {
            logger.error("SQL解析异常, 原始SQL:{}", sql);
        }

        if (statementList == null || statementList.size() == 0) {
            return sql;
        }
        SQLStatement sqlStatement = statementList.get(0);
        conditionHelper.addStatementCondition(sqlStatement);
        return SQLUtils.toSQLString(sqlStatement, dialect);
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

        conditionHelper = new SqlConditionHelper(new ITableFieldConditionDecision() {
            @Override
            public Map<String, String> adjudge(String tableName) {
                return PermissionUtil.adjudge(tableName);
            }
        });
    }

    public SqlConditionHelper getConditionHelper() {
        return conditionHelper;
    }

    public void setConditionHelper(SqlConditionHelper conditionHelper) {
        this.conditionHelper = conditionHelper;
    }

}
