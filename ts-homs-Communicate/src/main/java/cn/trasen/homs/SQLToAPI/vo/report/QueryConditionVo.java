package cn.trasen.homs.SQLToAPI.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
**********************************************   
* @Description: 自定义查询的查询条件
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Setter
@Getter
public class QueryConditionVo extends ProcedureParamVo{
	
	@ApiModelProperty("比较类型")
	private CompareType compareType;
	
	@ApiModelProperty("是否为模糊匹配-限于STRING")
	private boolean like = false;
		
	@ApiModelProperty("是否为必需条件")
	private boolean must = false;
	
	@ApiModelProperty("是否为列表参数")
	private boolean list = false;
	
}
