package cn.trasen.homs.SQLToAPI.service;

import java.util.List;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
**********************************************   
* @Description: 用户自定义查询-服务定义
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public interface UserDefineServiceService {

	/**
	* @Title: list
	* @Description: 自定义查询列表
	* @Author:huangkui
	* @Date: 2019年6月4日 下午4:06:42
	* @param condition
	* @param page
	* @return
	 */
	List<UserDefineQueryVo> list(String categoryCode,String topicCateCode,String condition, String systemService, Page page, String[] excludeColumns);


	/**
	 * 功能描述:查询详情
	  * @param id
	 * @Return: UserDefineQueryVo
	 * @Author: duanxinrun
	 * @Date: 2021/2/7 15:01
	 */
	UserDefineQueryVo selectById(String id);
	
	/**
	 * @Title selectByIds
	 * @Description 根据ID 
	 * @param ids
	 * @return List<UserDefineService>
	 * @date 2021年8月22日 下午2:19:32
	 * <AUTHOR>
	 */
	List<UserDefineService> selectByIds(List<String> ids);
	
	/**
	 * @Title doBatchSave
	 * @Description 批量保存
	 * @param records
	 * @return Integer
	 * @date 2021年8月22日 下午2:27:17
	 * <AUTHOR>
	 */
	Integer doBatchSave(List<UserDefineService> records);


	void importWithDesign(UserDefineService userDefineService);
	
}
