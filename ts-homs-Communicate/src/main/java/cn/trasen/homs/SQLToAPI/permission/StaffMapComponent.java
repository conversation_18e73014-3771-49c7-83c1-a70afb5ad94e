package cn.trasen.homs.SQLToAPI.permission;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import cn.trasen.homs.SQLToAPI.permission.model.MapVo;
import cn.trasen.homs.SQLToAPI.permission.model.StaffMapProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName StaffMapComponent
 * @Description StaffMapComponent
 * @date 2020年4月29日 上午11:26:48
 */
@Slf4j
@Component
public class StaffMapComponent implements CommandLineRunner {

    @Autowired
    private StaffMapProperties staffMapProperties;

    /**
     * @Title initStaffMapData
     * @Description 初始化加载映射表
     * @date 2020年4月29日 下午12:15:34
     * <AUTHOR>
     */
    public void initStaffMapData() {
        log.debug("staff map enabled :{}", staffMapProperties.isEnabled());
        log.debug("staff map laod url:{}", staffMapProperties.getUrl());
        if (staffMapProperties.isEnabled() && StringUtils.isNotBlank(staffMapProperties.getUrl())) {
            List<MapVo> staffMapList = loadStaffMapData(staffMapProperties.getUrl());
            if (staffMapList != null && staffMapList.size() > 0) {
                staffMapList.stream().forEach(item -> {
                    StaffMapHolder.getInstance().set(item.getSourceCode(), item.getTargetCode());
                });
            }
        }
    }

    private List<MapVo> loadStaffMapData(String url) {
        // 获取用户信息
        InputStream is = null;
        BufferedReader bf = null;
        String body = "";
        try {
            CloseableHttpClient client = HttpClients.custom().build();
            HttpGet httpGet = new HttpGet(url);
            is = client.execute(httpGet).getEntity().getContent();
            bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = bf.readLine()) != null) {
                buffer.append(line);
            }
            body = buffer.toString();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
            if (bf != null) {
                try {
                    bf.close();
                } catch (IOException e) {
                }
            }
        }
        log.debug("loadStaffMapData==body:{}", body);
        E<MapVo> staffMapList = JSONObject.parseObject(body, new TypeReference<E<MapVo>>() {
        });

        if (staffMapList != null) {
            return staffMapList.getRows();
        }
        return null;
    }

    public StaffMapProperties getStaffMapProperties() {
        return staffMapProperties;
    }

    public void setStaffMapProperties(StaffMapProperties staffMapProperties) {
        this.staffMapProperties = staffMapProperties;
    }

    @Override
    public void run(String... args) throws Exception {
        this.initStaffMapData();
    }
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class E<T> {
    private List<T> rows;
}
