package cn.trasen.homs.SQLToAPI.vo.report;
/**
**********************************************   
* @Description: 字段类型
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public enum ColumnType {
	STRING(0, "文字"), NUMBER(1, "数字"), DATE(2, "日期"),
	//用于客户端显示页签，必须设置默认值，客户端根据默认值显示页签
	PAGE(4,"页签"),
	DATEUNIT(5,"时间组件"),SELECT(6,"下拉框"),
	//文本，用于直接替换SQL，只做空校验
	TEXT(7,"文本");

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public Integer getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(Integer typeCode) {
		this.typeCode = typeCode;
	}

	private String typeName;
	private Integer typeCode;

	private ColumnType(Integer code, String name) {
		this.typeName = name;
		this.typeCode = code;
	}
	
	public static ColumnType fromCode(Integer code) {
		if(code == 0) {
			return STRING;
		}else if(code == 1) {
			return NUMBER;
		}else if(code == 2) {
			return DATE;
		}else if(code == 4) {
			return PAGE;
		}else if(code == 5) {
			return DATEUNIT;
		}
		return null;
	}
}
