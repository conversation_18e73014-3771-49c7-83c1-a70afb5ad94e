package cn.trasen.homs.SQLToAPI.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import cn.trasen.homs.SQLToAPI.dao.UserDefineServiceMapper;
import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.permission.ThpUserScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.model.ThpsUser;
import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.service.UserDefineServiceService;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnsVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 ********************************************** 
 * @Description: 用户自定义服务实现
 * @Author:huangkui
 * @Date: 2019年9月18日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class UserDefineServiceServiceImpl implements UserDefineServiceService {
	
	private static final String PLAT_ROLE = "R_ROLE_PLAT"; //平台管理员

	@Autowired
	private UserDefineServiceMapper mapper;

	@Autowired
	private UserDataSourceService userDataSourceService;

	@Override
	public List<UserDefineQueryVo> list(String categoryCode, String topicCateCode, String condition, String systemService, Page page, String[] excludeColumns) {
		long begin = System.currentTimeMillis();

        //判断是否是超级管理员角色，是则可以查看系统服务，否则不能查看系统服务
        ThpsUser userInfo = ThpUserScopeContextHolder.get();
        if(null != userInfo){
        	String sysRoleCode = userInfo.getSysRoleCode();
        	if(ObjectUtils.isEmpty(sysRoleCode) || (!sysRoleCode.contains(Contants.IS_SUPER_ADMIN) && !sysRoleCode.contains(PLAT_ROLE))) {
            	systemService = "02";
            }
        }
        //排除自定义服务表中大字段数据返回
        //1、先获取表所有字段
        List<TableColumnsVo> columnsList = mapper.getTableColumns("COMM_CUST_SERVICE");
        //排除的字段名集合
//        String[] excludeColumns = {"COLUMN_DEFINE", "CONDITION_DEFINE", "FORM_DATA","QUERY_SQL"};
        StringBuilder queryColumn = new StringBuilder();
        if(null !=columnsList && columnsList.size() > 0){
        	String columnsString = columnsList.get(0).getColumns();
        	String[] columns = columnsString.split(",");
        	for(String col : columns){
        		boolean exclude = false;
        		if(null != excludeColumns && excludeColumns.length > 0){
        			for(String exCol : excludeColumns){
        				if(exCol.equals(col)){
        					exclude = true;
        					break;
        				}
        			}
        		}
        		if(!exclude){
        			queryColumn.append(col).append(",");
        		}
        	}
        	
        	List<UserDefineQueryVo> result = mapper.listAll(page, categoryCode, topicCateCode, condition, systemService, queryColumn.toString().substring(0, queryColumn.toString().length() - 1));
    		log.info("list查询耗时:{}", (System.currentTimeMillis() - begin));
    		long begin2 = System.currentTimeMillis();
    		Map<String, String> map = userDataSourceService.names();
    		log.info("list调用接口耗时:{}", (System.currentTimeMillis() - begin2));
    		if (map != null) {
    			for (UserDefineQueryVo vo : result) {
    				vo.setDsName(map.get(vo.getDsId()));
    			}
    		}

    		result.stream().forEach(t -> {
    			t.setQuerySql(null);
    		});

    		return result;
        }

		return null;
	}

	public UserDefineQueryVo selectById(String id) {
		UserDefineService userDefineService = mapper.selectByPrimaryKey(id);
		Map<String, String> map = userDataSourceService.names();
		UserDefineQueryVo vo = new UserDefineQueryVo();
		BeanUtils.copyProperties(userDefineService, vo);
		vo.setDsName(map.get(vo.getDsId()));
		return vo;
	}

	@Override
	public List<UserDefineService> selectByIds(List<String> ids) {
		if (!ObjectUtils.isEmpty(ids)) {
			Example example = new Example(UserDefineService.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andIn("id", ids);
			return mapper.selectByExample(example);
		}
		return null;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer doBatchSave(List<UserDefineService> records) {
		// 批量导入
		if (!ObjectUtils.isEmpty(records)) {
			records.stream().forEach(item -> {
				UserDefineService record = mapper.selectByPrimaryKey(item.getId());
				ThpsUser userInfo = ThpUserScopeContextHolder.get();
		        if(null != userInfo){
		        	item.setCreateUser(userInfo.getUsercode());
		        	item.setCreateUserName(userInfo.getUsername());
		        	item.setUpdateUser(userInfo.getUsercode());
		        	item.setUpdateUserName(userInfo.getUsername());
		        }
				if(record ==null || ObjectUtils.isEmpty(record.getId())) {
					item.setCreateDate(new Date());
					item.setUpdateDate(new Date());
					mapper.insertSelective(item);
				} else {
					item.setUpdateDate(new Date());
					mapper.updateByPrimaryKey(item);
				}
			});
		}
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public void importWithDesign(UserDefineService userDefineService) {
		userDefineService.setIsDeleted(Contants.IS_DELETED_FALSE);
		userDefineService.setCreateDate(new Date());
		ThpsUser userInfo = ThpUserScopeContextHolder.get();
        if(null != userInfo){
        	userDefineService.setCreateUser(userInfo.getUsercode());
        	userDefineService.setCreateUserName(userInfo.getUsername());
        }

		String id = userDefineService.getId();
		UserDefineService svc = mapper.selectByPrimaryKey(id);
		if(svc != null) {
			if(StringUtils.hasText(svc.getDsId())) {
				userDefineService.setDsId(svc.getDsId());
			}
			mapper.updateByPrimaryKeySelective(userDefineService);
		}else {
			mapper.insertSelective(userDefineService);
		}
	}
}
