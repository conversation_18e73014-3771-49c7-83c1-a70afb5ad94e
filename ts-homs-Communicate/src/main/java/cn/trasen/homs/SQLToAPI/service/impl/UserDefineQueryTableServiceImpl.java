package cn.trasen.homs.SQLToAPI.service.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.oracle.visitor.OracleSchemaStatVisitor;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

//import cn.trasen.bi.bo.zbk.OfBusiCodeBo;
import cn.trasen.homs.SQLToAPI.QueryConstants;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg.DateCfg;
import cn.trasen.homs.SQLToAPI.dao.UserDefineServiceMapper;
import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.permission.ThpUserScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.model.ThpsUser;
import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.service.UserDefineQueryTableService;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil.ProcFunIO;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil;
import cn.trasen.homs.SQLToAPI.vo.DataSetVo;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnAlign;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.CompareType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcFunResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryConditionVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.bean.sso.UserDataSource;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户自定义查询-服务实现
 * @Author:huangkui
 * @Date: 2019年5月28日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class UserDefineQueryTableServiceImpl implements UserDefineQueryTableService {

	@Autowired
	private UserDefineServiceMapper mapper;
	

	@Autowired
	private UserDataSourceService userDataSourceService;

	@Autowired
	private DateDefaultCfg dateCfg;

	private static Pattern keyPt = Pattern
			.compile("(\\s+|\\()((update)|(insert)|(delete)|(create)|(replace)|(trunc)|(truncate))\\s+");
	private static Pattern dotSpacePt = Pattern.compile("\\.\\s+");
	private static final Pattern columnPt = Pattern.compile("(['\"]?([^'\"\\s]+)['\"]?)\\s?\\,$");
	private static final Pattern dbColPt = Pattern.compile("\\w+\\.\\w+");
	
	public static void main(String[] args) {
		Matcher mc = columnPt.matcher(" 'Ak\r\n中文■◆●☀1d\",");
		while(mc.find()) {
			System.out.println(mc.group(2));
		}
		
		String sql = "select a. delete from aa";
		sql = dotSpacePt.matcher(sql).replaceAll(".");
		System.out.println(sql);
		mc = keyPt.matcher(sql);
		System.out.println(mc.find());
		System.out.println(keyPt.matcher("lalsld (insert lalsd  ").find());
		System.out.println(keyPt.matcher("lalsld (create lalsd  ").find());
		System.out.println(keyPt.matcher("lalsld (trunc lalsd  ").find());
		System.out.println(keyPt.matcher("lalsld (truncate lalsd  ").find());
		System.out.println(keyPt.matcher("lalsld  delete lalsd  ").find());
		System.out.println(keyPt.matcher("lalsld  update lalsd  ").find());

		List<SQLStatement> stmtList = SQLUtils.parseStatements("SELECT SUBSTR(t.STATISTICAL_DATE, 1, 7),d.KSMC,\r\n"
				+ "sum(t.IN_NUM),\r\n"
				+ "sum(case when t.STATISTICAL_DATE = to_char(sysdate-1,'yyyy-mm-dd') then t.BED_NUM else 0 end) BED_NUM,\r\n"
				+ "round(sum(t.IN_NUM)/sum(t.BED_NUM),2)\r\n"
				+ "from OF_ZY_DEPT_BED_USE_RATE t join OD_ORG_DEPT_N d\r\n"
				+ "on t.YQDM = d.YQBM and t.KSDM = d.KSID\r\n" + "where t.STATISTICAL_DATE > '2019-06-01'",
				JdbcConstants.ORACLE);
		for (int i = 0; i < stmtList.size(); i++) {

			SQLStatement stmt = stmtList.get(i);
			OracleSchemaStatVisitor visitor = new OracleSchemaStatVisitor();
			stmt.accept(visitor);

			// 获取操作方法名称,依赖于表名称
			System.out.println("Manipulation : " + visitor.getTables());
			// 获取字段名称
			System.out.println("fields : " + visitor.getColumns());
		}

		System.out.println("2019-06-01~2019-06-10".split("\\~")[0]);

		sql = "select aaaa as abc \r\n, t.fff, t.ddd as 'abcd'  , fffff as \"ddd\",";
		mc = columnPt.matcher(QueryConstants.spacePt.matcher(sql).replaceAll(" "));
		while (mc.find()) {
			System.out.println(mc.group(1));
		}
		mc = dbColPt.matcher(sql);
		while (mc.find()) {
			System.out.println(mc.group());
		}

		System.out.println(DecimalFormat.getInstance()
				.format(new BigDecimal(23123.0023).setScale(2, RoundingMode.HALF_UP).doubleValue()));

	}

//	@Override
//	public UserDefineQueryVo parseBusicodeCondition(String sql, Integer dsType, OfBusiCodeBo ofBusiCodeBo) {
//		UserDefineQueryVo userDefineQueryVo = new UserDefineQueryVo();
//		userDefineQueryVo.setQueryConditions(ofBusiCodeBo.getQueryConditions());
//		userDefineQueryVo.setTableColumns(ofBusiCodeBo.getTableColumns());
//		UserDefineQueryVo vo = parseCondition(sql, null, userDefineQueryVo);
//		return vo;
//	}

	@Override
	public UserDefineQueryVo parseCondition(String sql, Integer dsType,UserDefineQueryVo userDefineQueryVo) {
		List<QueryConditionVo> queryConditions = parseNamedConditions(sql, userDefineQueryVo);
		boolean namedCondition = queryConditions.size() > 0;

		Assert.isTrue(checkSqlType(sql), "只允许select语句");
		// 去掉多余空格、回车换行等
		String sqlStr = QueryConstants.spacePt.matcher(sql).replaceAll(" ");
		// 第一步，解析出select于from中间的字符串，并且只允许出现一次
		String lowSql = sqlStr.toLowerCase();
		int begin = lowSql.indexOf("select");
		int end = lowSql.indexOf("from", begin + 1);
		Assert.isTrue(begin != -1 && end > begin + 6, "SQL语句不符合要求，找不到字段列表");
		// 只取第一个select 到 from的字符串作为列
		String colStr = sqlStr.substring(begin + 6, end).trim();
		// 加上一个逗号便于后面解析
		colStr = colStr + ",";
		Assert.isTrue(!checkStar(colStr), "SQL语句不符合要求，不能使用*字符获取所有行");
		if (!namedCondition) {
			// 非命名参数形式，需要校验是否多个select、where
			int si = lowSql.indexOf("select", end + 4);
			Assert.isTrue(si == -1, "SQL语句不符合要求，不允许出现多个select");
			// 检查where出现的次数
			int wc = 0;
			lowSql = sql.toLowerCase();
			int wi = lowSql.indexOf("where");
			while (wi != -1 && wc < 2) {
				wc++;
				wi = lowSql.indexOf("where", wi + 5);
			}
			Assert.isTrue(wc == 1, String.format("SQL语句不符合要求，where%s出现一次", wc == 0 ? "必须" : "只允许"));
		}

		// 第二步解析字段列表
		List<String> columns = parseColumns(colStr);

		log.info("解析出字段：{}，个数：{}", columns, columns.size());
		List<TableColumnVo> tableColumns = new ArrayList<>();
		Map<String, TableColumnVo> tableColumnsOldMap =  new HashMap<String, TableColumnVo>();
		if(userDefineQueryVo!=null && userDefineQueryVo.getTableColumns()!=null){
			List<TableColumnVo> tableColumnsOld = userDefineQueryVo.getTableColumns();
			tableColumnsOldMap = tableColumnsOld.stream().collect(Collectors.toMap(TableColumnVo::getName, t -> t));
		}

		int c = 1;
		//查询字段列表，可以通过先预执行后获取字段列表，再加入
		for (String column : columns) {
			Matcher mc = columnPt.matcher(column);
			String colName = null;
			if (mc.find()) {
				colName = mc.group(1);
				char cc = colName.charAt(0);
				if (cc == '\"' || cc == '\'') {
					// 使用了别名
					colName = mc.group(2);
				} else {
					if(colName.indexOf('.') != -1) {
						colName = colName.substring(colName.lastIndexOf('.')+1);
					}
					colName = transColumnName(colName, dsType);
				}
			} else {
				//
				if (dsType != Contants.DATA_SOURCE_TYPE_ORACLE) {
					throw new IllegalArgumentException(String.format("SQL语句不符合要求，字段[%s]需要设置别名！", column));
				}

				// 字段没有取别名
				if (queryConditions.stream().filter(q -> column.indexOf(":" + q.getName()) != -1).findFirst()
						.isPresent()) {
					throw new IllegalArgumentException(String.format("SQL语句不符合要求，包含条件的列[%s]必须使用别名", column));
				}
				// 列名：转成大写，并且去掉空格
				colName = QueryConstants.spacePt.matcher(column).replaceAll("").toUpperCase();
				// 去掉最后的逗号
				colName = colName.substring(0, colName.length() - 1);
			}
			// 有可能一个列包含多个字段
			TableColumnVo cvo = null;
			if(tableColumnsOldMap.containsKey(colName)){
				cvo = tableColumnsOldMap.get(colName);
			}else{
				cvo = new TableColumnVo();
				cvo.setName(colName);
				cvo.setAlign(ColumnAlign.right);
				cvo.setTitle(colName);
				cvo.setType(ColumnType.STRING);
			}
			cvo.setIndex(c++);
			tableColumns.add(cvo);

			if (!namedCondition) {
				Set<String> colNameSet = new HashSet<>();
				List<String> dbName = findDbName(column);
				Assert.notEmpty(dbName, String.format("SQL语句不符合要求，字段[%s]必须指定对应表格别名", column));
				// 如果不是命名参数形式
				for (String name : dbName) {
					if (colNameSet.contains(name)) {
						continue;
					}
					colNameSet.add(name);
					QueryConditionVo qvo = new QueryConditionVo();
					qvo.setColumnType(ColumnType.STRING);
					qvo.setCompareType(CompareType.EQ);
					qvo.setName(name);
					qvo.setTitle(name);
					qvo.setId(IdGeneraterUtils.nextId());
					queryConditions.add(qvo);
				}
			}
		}

		UserDefineQueryVo vo = new UserDefineQueryVo();
		vo.setQueryConditions(queryConditions);
		vo.setTableColumns(tableColumns);
		vo.setQuerySql(sql);
		return vo;
	}

	private String transColumnName(String colName, Integer dsType) {
		if (Contants.DATA_SOURCE_TYPE_DB2.equals(dsType) || Contants.DATA_SOURCE_TYPE_ORACLE.equals(dsType)) {
			// db2 oracle列名转成大写
			return colName.toUpperCase();
		} else {
			// sqlserver mysql 或者未知类型直接返回列名
			return colName;
		}
	}

	private List<String> parseColumns(String colStr) {
		int count = 0;
		int begin = 0;
		int idx = 1;
		List<String> columns = new ArrayList<>();
		while (idx < colStr.length()) {
			char c = colStr.charAt(idx);
			if (c == '(') {
				count++;
			} else if (c == ')') {
				count--;
			} else if (colStr.charAt(idx) == ',' && count == 0) {
				// 说明找到了一个字段界定符号
				String column = colStr.substring(begin, idx + 1);
				columns.add(column);
				begin = idx + 1;
				idx = begin + 1;
			}
			idx++;
		}
		return columns;
	}

	private static final Pattern namedCondtion = Pattern.compile("\\:(\\w+)");

	/**
	 * @Title: parseNamedConditions
	 * @Description: 按参数方式解析条件
	 * @Author:huangkui
	 * @Date: 2019年6月17日 下午5:40:52
	 * @param sql
	 * @return
	 */
	private List<QueryConditionVo> parseNamedConditions(String sql) {
		List<QueryConditionVo> list = new ArrayList<>();
		Matcher mc = namedCondtion.matcher(sql);
		Set<String> names = new HashSet<>();
		while (mc.find()) {
			String name = mc.group(1);
			if (names.contains(name) || QueryConstants.NUMBER_PT.matcher(name).find()) {
				// 已经有了该命名参数，或者是纯数字将不加入
				continue;
			}
			if (!checkNamedCondition(sql, mc.start(), mc.end())) {
				continue;
			}
			names.add(name);

			QueryConditionVo qvo = new QueryConditionVo();
			qvo.setColumnType(ColumnType.STRING);
			qvo.setCompareType(CompareType.EQ);
			qvo.setName(name);
			qvo.setTitle(name);
			qvo.setId(IdGeneraterUtils.nextId());
			list.add(qvo);
		}
		return list;
	}

	/**
	 * @Title: parseNamedConditions
	 * @Description: 按参数方式解析条件
	 * @Author:huangkui
	 * @Date: 2019年6月17日 下午5:40:52
	 * @param sql
	 * @return
	 */
	private List<QueryConditionVo> parseNamedConditions(String sql,UserDefineQueryVo userDefineQueryVo) {
		List<QueryConditionVo> list = new ArrayList<>();
		Matcher mc = namedCondtion.matcher(sql);
		Set<String> names = new HashSet<>();
		Map<String, QueryConditionVo> queryConditionMap = new HashMap<String, QueryConditionVo>();
		if(userDefineQueryVo!=null && userDefineQueryVo.getQueryConditions()!=null){
			List<QueryConditionVo> queryConditions = userDefineQueryVo.getQueryConditions();
			queryConditionMap = queryConditions.stream().collect(Collectors.toMap(QueryConditionVo::getName, t -> t));
		}

		while (mc.find()) {
			String name = mc.group(1);
			if (names.contains(name) || QueryConstants.NUMBER_PT.matcher(name).find()) {
				// 已经有了该命名参数，或者是纯数字将不加入
				continue;
			}
			if (!checkNamedCondition(sql, mc.start(), mc.end())) {
				continue;
			}
			names.add(name);
			if(queryConditionMap.containsKey(name)){
				list.add(queryConditionMap.get(name));
			}else{
				QueryConditionVo qvo = new QueryConditionVo();
				qvo.setColumnType(ColumnType.STRING);
				qvo.setCompareType(CompareType.EQ);
				qvo.setName(name);
				qvo.setTitle(name);
				qvo.setId(IdGeneraterUtils.nextId());
				list.add(qvo);
			}
		}
		return list;
	}

	private boolean checkNamedCondition(String sql, int start, int end) {
		// 检查是否在字符串内
		int count = 0;
		for (int i = start; i >= 0; i--) {
			if(sql.charAt(i) == '\'') {
				count ++;
			}
		}
		if(count % 2 == 1) {
			count = 0;
			for(int i = end; i< sql.length();i++) {
				if(sql.charAt(i) == '\'') {
					count ++;
				}
			}
			return count % 2 == 0;
		}
		return true;
	}

	private boolean checkStar(String colStr) {
		if (colStr.indexOf('*') != -1) {
			String noSpace = QueryConstants.spacePt.matcher(colStr).replaceAll("");
			int idx = noSpace.indexOf('*');
			while (idx != -1) {
				if (idx > 0) {
					if (noSpace.charAt(idx - 1) == '.') {
						return true;
					}
				} else if (idx == 0) {
					// 第一个字符就是*号
					return true;
				}
				idx = noSpace.indexOf('*', idx + 1);
			}
		}
		return false;
	}

	private List<String> findDbName(String column) {
		Matcher mc = dbColPt.matcher(column);
		List<String> list = new ArrayList<>();
		while (mc.find()) {
			list.add(mc.group());
		}
		return list;
	}

	/**
	 * 暂时废弃不用
	 */
	@Override
	public UserDefineQueryVo preQuery(String sql, String dsId, List<QueryConditionVo> conditions) {
		Assert.isTrue(checkSqlType(sql), "只允许select语句");
		UserDataSource ds = userDataSourceService.selectDecrypt(dsId);
		Assert.notNull(ds, "数据源不存在");

		// 先设置参数
		String querySql = setQueryParam(sql, conditions, null, true);

		// 预解析返回字段列
		StringBuffer colSb = new StringBuffer();
		String sqlUpperCase = sql.toUpperCase();
		int begin = sqlUpperCase.indexOf("SELECT");
		int end = sqlUpperCase.indexOf("FROM", begin + 1);
		while (begin != -1 && end != -1) {
			colSb.append(sqlUpperCase.substring(begin + 6, end));
			colSb.append(",");
			begin = sqlUpperCase.indexOf("SELECT", end + 1);
			end = sqlUpperCase.indexOf("FROM", begin + 1);
		}
		// 将字段中的所有空格都去掉
//		String colStr = QueryConstants.spacePt.matcher(colSb.toString().replace('\"', ' ')).replaceAll("");

		log.info("开始查询：{}", querySql);
		List<Map<String, Object>> rows = CommonSqlUtil.queryFirstPageUseMybatis(ds, querySql);
		log.info("查询结果：{}", rows == null ? "null" : rows.size());
		Assert.notEmpty(rows, "查询结果为空");
		Map<String, Object> row = rows.get(0);
		Assert.notNull(row, "查询结果为空");
		UserDefineQueryVo vo = new UserDefineQueryVo();
		List<TableColumnVo> tableColumns = new ArrayList<>();
		for (String col : row.keySet()) {
			Object value = row.get(col);
			TableColumnVo column = new TableColumnVo();
			column.setName(col);
			column.setTitle(col);
			column.setAlign(ColumnAlign.right);
			if (value instanceof String) {
				column.setType(ColumnType.STRING);
			} else if (value instanceof Date) {
				column.setType(ColumnType.DATE);
				column.setDateFormat("yyyy-MM-dd");
			} else {
				column.setType(ColumnType.NUMBER);
			}
			column.setNumPercent(false);
			column.setDateFormat("");
			tableColumns.add(column);
		}

		vo.setDsId(dsId);
		vo.setQuerySql(sql);
		vo.setQueryResult(CommonSqlUtil.transResult(tableColumns, rows));
		vo.setTableColumns(tableColumns);
		vo.setQueryConditions(conditions);

		return vo;
	}

	private String setQueryParam(String sql, List<QueryConditionVo> conditions, List<QueryParamVo> queryParamList,
			boolean isTest) {
		List<QueryConditionVo> namedConditions = parseNamedConditions(sql);
		if (namedConditions.size() > 0) {
			if (!isTest) {
				// 真实查询就取保存的参数，不取解析的
				namedConditions = conditions;
			}
			// 一、命名参数形式
			String lastSql = sql;
			for (QueryConditionVo condition : namedConditions) {
				String name = condition.getName();
				QueryParamVo param = getParamWithCondition(condition, queryParamList);
				Assert.notNull(param, String.format("命名参数[%s]缺少值并且未指定默认值", name));
				lastSql = lastSql.replace(":" + name, transParamValue(condition, param, true));
			}
			return lastSql;
		}

		// 二、非命名参数形式
		String lowSql = sql.toLowerCase();
		int index = lowSql.indexOf("where");
		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer.append(sql.substring(0, index));
		sqlBuffer.append(" where 1=1");
		// 开始添加条件
		for (QueryConditionVo condition : conditions) {
			String name = condition.getName();
			QueryParamVo param = getParamWithCondition(condition, queryParamList);
			if (param == null) {
				continue;
			}

			sqlBuffer.append(" and ");
			sqlBuffer.append(name);

			sqlBuffer.append(transParamValue(condition, param, false));
		}

		int ii = lowSql.indexOf("group", index);
		if (ii != -1) {
			sqlBuffer.append(" ");
			sqlBuffer.append(sql.substring(ii));
		} else {
			ii = lowSql.indexOf("order", index);
			if (ii != -1) {
				sqlBuffer.append(" ");
				sqlBuffer.append(sql.substring(ii));
			}
		}
		return sqlBuffer.toString();
	}

	private String transParamValue(QueryConditionVo condition, QueryParamVo param, boolean namedParam) {
		boolean isNum = condition.getColumnType().equals(ColumnType.NUMBER);
		if (!isNum && condition.getColumnType().equals(ColumnType.PAGE)) {
			String defVal = condition.getDefaultValue();
			defVal = defVal.substring(defVal.indexOf(',') + 1, defVal.indexOf(';'));
			if (defVal.charAt(0) != '\'') {
				// PAGE类型根据默认值来判断是否为数字
				isNum = true;
			}
		}
		StringBuffer sqlBuffer = new StringBuffer();
		boolean isRange = condition.isRange();
		boolean isList = condition.isList();
		if (namedParam) {
			// 命名参数形式忽略 范围配置
			isRange = false;
			if (!isList && param.getList() != null && param.getList().size() > 0) {
				// 范围值转成列表值
				isList = true;
			}
		}
		List<String> list = param.getList();
		if (isRange) {
			sqlBuffer.append(" between ");
			if (!isNum) {
				sqlBuffer.append("'");
			}
			sqlBuffer.append(list.get(0));
			if (!isNum) {
				sqlBuffer.append("'");
			}
			sqlBuffer.append(" and ");
			if (!isNum) {
				sqlBuffer.append("'");
			}
			sqlBuffer.append(list.get(1));
			if (!isNum) {
				sqlBuffer.append("'");
			}
		} else if (isList) {
			if (!namedParam) {
				sqlBuffer.append(" in ");
			}
			sqlBuffer.append("(");
			for (String v : list) {
				if (!isNum) {
					sqlBuffer.append("'");
				}
				sqlBuffer.append(v);
				if (!isNum) {
					sqlBuffer.append("'");
				}
				sqlBuffer.append(",");
			}
			sqlBuffer.setLength(sqlBuffer.length() - 1);
			sqlBuffer.append(")");
		} else if (condition.isLike()) {
			if (!namedParam) {
				sqlBuffer.append(" like ");
			}
			sqlBuffer.append("'%");
			sqlBuffer.append(param.getValue());
			sqlBuffer.append("%'");
		} else if(condition.getColumnType() == ColumnType.TEXT) {
			if (!namedParam) {
				// 命名参数形式忽略操作
				sqlBuffer.append(" ");
				sqlBuffer.append(condition.getCompareType().getOp());
				sqlBuffer.append(" ").append(param.getValue());
			}else {
				sqlBuffer.append(param.getValue());
			}
		}else{
			sqlBuffer.append(" ");
			if (!namedParam) {
				// 命名参数形式忽略操作
				sqlBuffer.append(condition.getCompareType().getOp());
			}
			sqlBuffer.append(" ");
			if (!isNum) {
				sqlBuffer.append("'");
			}
			sqlBuffer.append(param.getValue());
			if (!isNum) {
				sqlBuffer.append("'");
			}
		}
		return sqlBuffer.toString();
	}

	private QueryParamVo getParamWithCondition(QueryConditionVo condition, List<QueryParamVo> queryParamList) {
		QueryParamVo param = null;
		String name = condition.getName();
		boolean presend = false;
		Optional<QueryParamVo> op = null;
		if (queryParamList != null && queryParamList.size() > 0) {
			op = queryParamList.stream().filter(q -> q.getName().equals(name)).findFirst();
			presend = op.isPresent();
		}
		if (!presend) {
			if (condition.isMust()) {
				// 命名参数形式
				Assert.hasText(condition.getDefaultValue(), String.format("参数%s为必填参数", name));
				String value = condition.getDefaultValue();
				param = new QueryParamVo();
				param.setName(name);
				if (condition.getColumnType().equals(ColumnType.PAGE)) {
					int begin = value.indexOf(',');
					int end = value.indexOf(';');
					value = value.substring(begin + 1, end);
					if (value.charAt(0) == '\'') {
						value = value.substring(1);
					}
					if (value.charAt(value.length() - 1) == '\'') {
						value = value.substring(0, value.length() - 1);
					}
					param.setValue(value);
				}
				if (condition.isList() || condition.isRange()) {
					param.setList(transValueToList(condition.getColumnType(), value));
				} else {
					param.setValue(value);
				}
			} else {
				// 非必需参数
				return null;
			}
		} else {
			param = op.get();
			if (condition.isList() || condition.isRange()) {
				if (CollectionUtils.isEmpty(param.getList())) {
					if (condition.isMust()) {
						String value = param.getValue();
						if (!StringUtils.hasText(value)) {
							value = condition.getDefaultValue();
						}
						Assert.hasText(value, String.format("参数%s为必填参数", name));
						param.setList(transValueToList(condition.getColumnType(), value));
					} else {
						// 非必需参数
						return null;
					}
				}
			} else if (!StringUtils.hasText(param.getValue())) {
				if (condition.isMust()) {
					Assert.hasText(condition.getDefaultValue(), String.format("参数%s为必填参数", name));
					String value = condition.getDefaultValue();
					if (condition.getColumnType().equals(ColumnType.PAGE)) {
						int begin = value.indexOf(',');
						int end = value.indexOf(';');
						value = value.substring(begin + 1, end);
						if (value.charAt(0) == '\'') {
							value = value.substring(1);
						}
						if (value.charAt(value.length() - 1) == '\'') {
							value = value.substring(0, value.length() - 1);
						}
					}
					param.setValue(value);
				} else {
					// 非必需参数
					return null;
				}
			} else {
				if (condition.getColumnType().equals(ColumnType.PAGE)) {
					String value = param.getValue();
					if (value.charAt(0) == '\'') {
						value = value.substring(1);
					}
					if (value.charAt(value.length() - 1) == '\'') {
						value = value.substring(0, value.length() - 1);
					}
					param.setValue(value);
				}
			}
		}
		return param;
	}

	@Override
	@Transactional(readOnly = false)
	public UserDefineQueryVo saveOrUpdate(UserDefineService record) {
		ThpsUser user = ThpUserScopeContextHolder.get();
		//处理自定义下拉列表
		List<QueryConditionVo> list = JSONObject.parseArray(record.getConditionDefine(), QueryConditionVo.class);
		boolean change[] = new boolean[] {false};
		list.stream().forEach(q -> {
			if(StringUtils.hasText(q.getListDataUrl())) {
				String dataUrl = q.getListDataUrl();
				int idx = dataUrl.indexOf('?');
				if(idx > 0) {
					String queryString = dataUrl.substring(idx);
					StringBuffer buffer = new StringBuffer(dataUrl.substring(0, idx));
					Pattern pt = Pattern.compile("(\\?|&)\\w+=");
					Matcher mc = pt.matcher(queryString);
					int begin = 0;
					int end = 0;
					try {
						while(mc.find()) {
							begin = mc.start();
							if(end > 0) {
								buffer.append(URLEncoder.encode(queryString.substring(end, begin-1),"UTF-8"));
							}
							buffer.append(mc.group());
							end = mc.end();
						}
						if(end > 0) {
							buffer.append(URLEncoder.encode(queryString.substring(end),"UTF-8"));
						}
					} catch (UnsupportedEncodingException e) {
						e.printStackTrace();
					}
					q.setListDataUrl(buffer.toString());
					change[0] = true;
				}
			}
		});
		if(change[0]) {
			//有设置下拉列表
			record.setConditionDefine(JSONArray.toJSONString(list));
		}
		if (StringUtils.hasText(record.getId())) {
			
			UserDefineService db = mapper.selectByPrimaryKey(record.getId());
			Assert.notNull(db, "自定义服务不存在！");
			Assert.isTrue(db.getModifyFlag() != null && db.getModifyFlag() == 1, "该自定义服务是历史数据，不允许修改！");
			
			// 更新
			record.setUpdateDate(new Date());
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			mapper.updateByPrimaryKeySelective(record);
		} else {
			// 新增
			//类型固定为SQL
			record.setServiceType(1);
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			//新增加的允许修改
			record.setModifyFlag(1);
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			mapper.insertSelective(record);
		}
		return copyFrom(record);
	}

	@Override
	@Transactional(readOnly = false)
	public Integer delete(String id) {
		Assert.hasText(id, "ID不能为空.");
		UserDefineService record = new UserDefineService();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = ThpUserScopeContextHolder.get();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public UserDefineQueryVo selectById(String id) {
		UserDefineService query = mapper.selectByPrimaryKey(id);
		if (query != null) {
			UserDataSource ds = userDataSourceService.selectDecrypt(query.getDsId());
			UserDefineQueryVo vo = copyFrom(query);
			if (ds != null) {
				vo.setDsName(ds.getDsName());
				vo.setDsType(ds.getDsType());
			}
			return vo;
		}
		return null;
	}

	@Override
	public UserDefineQueryVo copyFrom(UserDefineService query) {
		UserDefineQueryVo vo = new UserDefineQueryVo();
		BeanUtils.copyProperties(query, vo);
		try {
			vo.setTableColumns(JSONObject.parseArray(query.getColumnDefine(), TableColumnVo.class));
			vo.setQueryConditions(JSONObject.parseArray(query.getConditionDefine(), QueryConditionVo.class));
		} catch (Exception e) {
			e.printStackTrace();
			if (vo.getTableColumns() == null) {
				throw new IllegalArgumentException("字段定义json格式错误");
			} else {
				throw new IllegalArgumentException("条件定义json格式错误");
			}
		}
		return vo;
	}

	@Override
	public List<QueryParamVo> transParam(String paramStr) {
		return JSONObject.parseArray(paramStr, QueryParamVo.class);
	}

	@Override
	public String check(UserDefineQueryVo vo, List<QueryParamVo> queryParamList) {
		if (!checkSqlType(vo.getQuerySql())) {
			return "SQL语句不规范，只允许出现select";
		}
		Integer dsType = null;
		if (StringUtils.hasText(vo.getDsId())) {
			UserDataSource dataSource = userDataSourceService.selectDecrypt(vo.getDsId());
			if (dataSource == null) {
				return "找不到对应数据源";
			}
			dsType = dataSource.getDsType();
		}
		// 检查sql语句
		UserDefineQueryVo pvo = null;
		try {
			pvo = parseCondition(vo.getQuerySql(), dsType,vo);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return queryParamList == null ? "SQL语句错误，请使用解析后的语句保存" : "SQL语句错误，请重新配置";
		}

		// 检查字段
		List<TableColumnVo> cols = new ArrayList<>();
		// 先将树形结构转成列表
		if(null != vo.getTableColumns()){
			CommonSqlUtil.treeToList(vo.getTableColumns(), cols);
		} else {
			throw new IllegalArgumentException("至少设置一个结果对象！");
		}
		List<TableColumnVo> pcols = pvo.getTableColumns();
		for (TableColumnVo col : cols) {
			if (!StringUtils.hasText(col.getName())) {
				return "字段必须指定名称";
			}
			if (!StringUtils.hasText(col.getTitle())) {
				return String.format("字段[%s]没有定义标题", col.getName());
			}
			if (!col.isVirtual()) {
				// 检查是否与SQL匹配
				Optional<TableColumnVo> op = pcols.stream().filter(pc -> pc.getName().equalsIgnoreCase(col.getName()))
						.findFirst();
				if (!op.isPresent()) {
					return String.format("SQL语句中不包含%s数据列", col.getName());
				} else {
					// 为了防止解析时没有确定数据源而不确定字段大小写，此处将准确的字段名设置回去
					col.setName(op.get().getName());
				}
				String result = checkSubQuery(col,mapper);
				if(result != null) {
					return result;
				}
			}
		}
		// 检查条件
		List<QueryConditionVo> conditions = vo.getQueryConditions();
		if (conditions == null) {
			return "条件定义错误";
		}

		List<QueryConditionVo> pcs = pvo.getQueryConditions();
		// 以下检查条件与参数
		for (QueryConditionVo condition : conditions) {
			String name = condition.getName();
			if ((condition.getColumnType().equals(ColumnType.DATE)
					|| condition.getColumnType().equals(ColumnType.DATEUNIT))&& name.equals("begin")) {
				// !!!###@@@特殊处理
				condition.setRange(false);
			}
			String title = condition.getTitle();
			if (!StringUtils.hasText(title)) {
				title = name;
			}
			// 检查是否有操作类型
			if (!condition.isLike() && !condition.isRange() && !condition.isList()
					&& condition.getCompareType() == null) {
				return String.format("“%s”比较类型不能为空", title);
			}

			if (queryParamList != null) {
				Optional<QueryParamVo> op = queryParamList.stream()
						.filter(q -> q.getName() != null && q.getName().equals(name)).findFirst();
				if (op.isPresent()) {
					ColumnType type = condition.getColumnType();
					QueryParamVo param = op.get();
					if(type == ColumnType.DATE || type == ColumnType.DATEUNIT) {
						//检查日期 按钮设置
						if(StringUtils.hasText(condition.getDateButtonValue())) {
							if (!ReportUtil.checkDefaultValue(type, condition.getDateButtonValue(), dateCfg)) {
								return String.format("“%s”时间按钮值配置(%s)错误", title, condition.getDateButtonValue());
							}
						}
						int idx = param.getValue().indexOf(" ~ ");
						if (idx != -1) {
							param.setValue(param.getValue().substring(0,idx));
						}else {
							//日期去掉空格
							param.setValue(param.getValue().trim());
						}
						DateCfg cfg = dateCfg.getByCode(param.getValue());
						if(cfg != null) {
							Date date = dateCfg.getDateByConfig(cfg);
							param.setValue(QueryConstants.DATE_FORMAT.format(date));
						}
					}
					if (condition.isRange()) {
						if (param.getList() == null) {
							if (param.getValue() != null) {
								param.setList(transValueToList(type, param.getValue()));
							} else if (condition.getDefaultValue() != null) {
								param.setList(transValueToList(type, condition.getDefaultValue()));
							}
						}
						if (param.getList() == null || param.getList().size() != 2) {
							if (condition.isMust()) {
								// 如果是必需参数
								return String.format("参数[%s]需要提供前后两个范围值", title);
							}
						} else {
							for (String value : param.getList()) {
								// 校验数据格式
								if (!ReportUtil.checkDefaultValue(type, value, dateCfg)) {
									return String.format("参数[%s]需要[%s]类型值", title, type.getTypeName());
								}
							}
						}
					} else if (condition.isList()) {
						if (param.getList() == null) {
							if (param.getValue() != null) {
								param.setList(transValueToList(type, param.getValue()));
							} else if (condition.getDefaultValue() != null) {
								param.setList(transValueToList(type, condition.getDefaultValue()));
							}
						}
						if (param.getList() == null || param.getList().size() == 0) {
							if (condition.isMust()) {
								// 如果是必需参数
								return String.format("参数[%s]需要提供列表值", title);
							}
						} else {
							for (String value : param.getList()) {
								// 校验数据格式
								if (!ReportUtil.checkDefaultValue(type, value, dateCfg)) {
									return String.format("参数[%s]需要[%s]类型值", title, type.getTypeName());
								}
							}
						}
					} else {
						String value = param.getValue();
						if (!StringUtils.hasText(value) && condition.getDefaultValue() != null) {
							value = condition.getDefaultValue();
						}
						if (value == null && condition.isMust()) {
							return String.format("参数[%s]为必填参数", title);
						}
						if (!StringUtils.hasText(value)) {
							if (type != ColumnType.STRING && condition.isMust()) {
								// 文字类型参数允许参数为空字符串
								return String.format("参数[%s]为必填参数", title);
							}
						} else {
							// 校验数据格式
							if (!ReportUtil.checkDefaultValue(type, value, dateCfg)) {
								return String.format("参数[%s]需要[%s]类型值", title, type.getTypeName());
							}
						}
					}
				} else {
					if (condition.isMust()) {
						if (condition.getColumnType() == ColumnType.STRING && condition.getDefaultValue() == null) {
							// 字符串类型可以设置为空字符串
							return String.format("缺少参数[%s]", title);
						} else if (!StringUtils.hasText(condition.getDefaultValue())) {
							return String.format("缺少参数[%s]", title);
						}
					}
				}
			}
			// 避免修改SQL字段
			Optional<QueryConditionVo> op = pcs.stream().filter(pc -> pc.getName().equalsIgnoreCase(name)).findFirst();
			if (!op.isPresent()) {
				return String.format("SQL语句中不包含%s字段", name);
			} else {
				// 为了防止解析时没有确定数据源而不确定字段大小写，此处将准确的字段名设置回去
				condition.setName(op.get().getName());
			}
			// 检查默认值是否符合要求
			if (StringUtils.hasText(condition.getDefaultValue())) {
				if (condition.getColumnType().equals(ColumnType.PAGE)) {
					String[] vs = condition.getDefaultValue().split(";");
					if (vs.length < 2) {
						return String.format("“%s”默认值格式错误（范例1：“收入,1;人次,2”，范例2：“收入,'1';人次,'2'”。）", title);
					}
					for (String v : vs) {
						String[] vvs = v.split(",");
						if (vvs.length != 2) {
							return String.format("“%s”默认值格式错误（范例1：“收入,1;人次,2”，范例2：“收入,'1';人次,'2'”。）", title);
						}
					}
				} else {
					String[] vs = condition.getDefaultValue().split(",");
					if (condition.isRange() && vs.length != 2) {
						return String.format("“%s”默认值必须提供前后两个范围值，以逗号隔开", title);
					}
					for (String v : vs) {
						if (!ReportUtil.checkDefaultValue(condition.getColumnType(), v, dateCfg)) {
							return String.format("“%s”默认值不符合规范%s", title,
									condition.getColumnType().equals(ColumnType.STRING) ? "，字符串不能出现单引号" : "");
						}
					}
				}
			} else {
				if (condition.getColumnType().equals(ColumnType.PAGE)) {
					return "页签(PAGE)类型参数必须设置默认值，格式：页签名1,页签值1;页签名2,页签值2 ....";
				}
			}
		}
		return null;
	}

	protected String checkSubQuery(TableColumnVo col, UserDefineServiceMapper mapper) {
		//检查子查询设置
		if(StringUtils.hasText(col.getSubQueryId())) {
			if(!StringUtils.hasText(col.getSubQueryParam())) {
				return "必须设置子查询参数";
			}
			
			UserDefineService service = mapper.selectByPrimaryKey(col.getSubQueryId());
			
			int type = col.getSubQueryType();
			if(type == 0) {
				if(service == null || service.getServiceType() != 1) {
					return "子查询不存在或已被删除";
				}
				//查询
//				UserDefineQuery q = mapper1.selectByPrimaryKey(col.getSubQueryId());
//				if(q != null && q.getIsDeleted().equals(Contants.IS_DELETED_FALSE)) {
//					col.setSubQueryName(q.getQueryName());
//				}else {
//					return "子查询不存在或已被删除";
//				}
			}else {
				if(service == null || service.getServiceType() != 2) {
					return "存储过程不存在或已被删除";
				}
				//存储过程
//				UserDefineProcFun procFun = procFunMapper1.selectByPrimaryKey(col.getSubQueryId());
//				if(procFun != null && procFun.getIsDeleted().equals(Contants.IS_DELETED_FALSE)) {
//					col.setSubQueryName(procFun.getQueryName());
//				}else {
//					return "存储过程不存在或被删除";
//				}
			}
		}
		return null;
	}

	private List<String> transValueToList(ColumnType type, String value) {
		String split = ",";
		switch (type) {
		case STRING:
			break;
		case NUMBER:
			split = "[^\\d\\.\\-]+";
			break;
		case DATE:
			if (value.indexOf('~') != -1) {
				split = "\\~";
			}
			break;
		default:
			break;
		}
		String[] vs = value.split(split);
		if (vs != null && vs.length > 0) {
			List<String> list = new ArrayList<>();
			for (String v : vs) {
				list.add(v.trim());
			}
			return list;
		}
		return null;
	}

	private boolean checkSqlType(String querySql) {
		String q = querySql.toLowerCase();
		q = QueryConstants.spacePt.matcher(q).replaceAll(" ");
		q = dotSpacePt.matcher(q).replaceAll(".");
		if (keyPt.matcher(q).find()) {
			// 不允许出现非select关键字
			log.info("检查SQL语句未通过：{}", querySql);
			return false;
		}
		// 并且以select开头
		log.info("将判断是否select开始：{}", querySql);
		return q.trim().startsWith("select");
	}

	@Override
	public List<Map<String, Object>> query(UserDefineQueryVo query, List<QueryParamVo> queryParamVo, Page page) {
		List<QueryConditionVo> conditions = query.getQueryConditions();
		String querySql = setQueryParam(query.getQuerySql(), conditions, queryParamVo, false);
		UserDataSource ds = userDataSourceService.selectDecrypt(query.getDsId());
		Assert.notNull(ds, "数据源不存在");

		List<TableColumnVo> cols = new ArrayList<>();
		CommonSqlUtil.treeToList(query.getTableColumns(), cols);
		cols.sort((c1, c2) -> c1.getIndex().compareTo(c2.getIndex()));
		// 检查翻页查询的排序是否在条件列表
		if (StringUtils.hasText(page.sidx)) {
			Optional<TableColumnVo> op = cols.stream().filter(c -> c.getName().equalsIgnoreCase(page.sidx)).findFirst();
			if (op.isPresent()) {
				page.sidx = op.get().getName();
				if (query.getQuerySql().toUpperCase().indexOf("\"" + page.sidx.toUpperCase() + "\"") != -1) {
					page.sidx = "\"" + page.sidx + "\"";
				}
			} else {
				page.sidx = "";
			}
		}
		log.info("开始查询：{}", querySql);
		List<Map<String, Object>> list = query.getPage()==null||query.getPage()==1?
				CommonSqlUtil.queryPageUseMybatis(ds, querySql, page):
					CommonSqlUtil.queryNoPageUseMybatis(ds, querySql, page);
		log.info("查询结果行数：{},总行数{}，总页数{}", list.size(), page.getTotalCount(), page.getTotalPages());
		// 对数据进行格式化
		List<Map<String, Object>> result = CommonSqlUtil.transResult(cols, list);
		return result;
	}

	@Override
	public List<UserDefineQueryVo> list(String condition, Page page) {
		List<UserDefineQueryVo> result = mapper.listQuery(page, condition);
		Map<String, String> map = userDataSourceService.names();
		if(map != null) {
			for(UserDefineQueryVo vo:result) {
				vo.setDsName(map.get(vo.getDsId()));
			}
		}
		return result;
	}

	@Override
	public DataSetVo<Map<String, Object>> tableQuery(String id, Page page, String parameters, HttpServletRequest request) {
		UserDefineQueryVo query = selectById(id);
		if (query == null) {
			throw new IllegalArgumentException("没找到ID为：【" + id + "】的【自定义服务】");
		}
		String paraStr = CommonSqlUtil.paraseParameter(request,parameters,query.getQueryConditions(),QueryConditionVo::getName);	
		if(query.getQuerySql().toLowerCase().indexOf("select")==-1) {
			String querySql = query.getQuerySql();
			List<QueryConditionVo> qcs = query.getQueryConditions();
			List<TableColumnVo> tcl = query.getTableColumns();	
			paraStr = CommonSqlUtil.loadDefaultParas(paraStr,qcs, dateCfg);
			if(paraStr == null) {
				// 客户端初始化没有参数，返回空的数据
				page.setTotalCount(0);
				page.setTotalPages(0);
				return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
						new ArrayList<>());
			}
//			if (!StringUtils.hasText(paraStr)) {
//				paraStr = CommonSqlUtil.loadDefaultParas("",qcs, dateCfg);
//				if(paraStr == null) {
//					// 客户端初始化没有参数，返回空的数据
//					page.setTotalCount(0);
//					page.setTotalPages(0);
//					return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
//							new ArrayList<>());
//				}
//			} 
			List<QueryParamVo> queryParamVo = transParam(paraStr);
			List<ProcFunIO> ios = new ArrayList<>();
			for(QueryParamVo vo:queryParamVo) {
				QueryConditionVo qvo = qcs.stream().filter(q -> q.getName().equals(vo.getName())).findFirst().orElse(null);

				Integer type = CommonSqlUtil.ProcFun_Type_String;
				if(qvo.getColumnType() == ColumnType.NUMBER) {
					type = CommonSqlUtil.ProcFun_Type_Number;
				}
				ProcFunIO io = new ProcFunIO(CommonSqlUtil.ProcFun_Input, vo.getName(), type, vo.getValue());
				ios.add(io );
			}
			
			// 固定游标参数
			String nn = "CURSOR";
			int cursorIndex = querySql.indexOf(":CURSOR");
			if(cursorIndex > 0) {
				ios.add(new ProcFunIO(CommonSqlUtil.ProcFun_Output, "CURSOR", CommonSqlUtil.ProcFun_Type_Cursor, null));
			}
			
			if(cursorIndex == -1) {
				// 出参数
				int idx = querySql.indexOf('=');
				nn = "aa";
				if(idx != -1) {
					nn = querySql.substring(0,idx);
					nn = nn.trim();
					nn = nn.substring(1);
					ios.add(new ProcFunIO(CommonSqlUtil.ProcFun_Output, nn, CommonSqlUtil.ProcFun_Type_DataSet, null));
				}
			}
			List<ProcFunResultVo> list = CommonSqlUtil.callProcFun(userDataSourceService.selectDecrypt(query.getDsId()), querySql, ios );
			String nnn = nn;
			ProcFunResultVo vo = list.stream().filter(p -> p.getName().equals(nnn)).findFirst().orElse(null);
			List<Map<String, Object>> rows = new ArrayList<>();
			if(vo != null) {
				rows = vo.getResult();
				List<TableColumnVo> colList = new ArrayList<>();
				CommonSqlUtil.treeToList(tcl, colList );
				rows =  CommonSqlUtil.transResult(colList, rows);;
			}
			DataSetVo<Map<String, Object>> dataSet = new DataSetVo<>(page.getPageNo(), rows.size(), 1, rows.size(), rows);
			return dataSet;
		}else {
			return queryPageResult(query, paraStr, page);
		}
	}

	@Override
	public DataSetVo<Map<String, Object>> queryPageResult(UserDefineQueryVo query, String parameters, Page page) {
		String paraStr = null;
		log.info("queryPageResult query:{},parameters:{}",JSON.toJSONString(query),parameters );
		
		if (!StringUtils.hasText(parameters) || "[]".equals(parameters)) {
			List<QueryConditionVo> conditions = query.getQueryConditions();
			paraStr = CommonSqlUtil.loadDefaultParas(parameters,conditions, dateCfg);
			log.info("queryPageResult paraStr:{},dateCfg:{}",paraStr,JSON.toJSONString(dateCfg) );
			if(paraStr == null) {
				// 客户端初始化没有参数，返回空的数据
				page.setTotalCount(0);
				page.setTotalPages(0);
				return new DataSetVo<>(page.getPageNo(), page.getPageSize(), 0, 0,
						new ArrayList<>());
			}
		} else {
			paraStr = parameters;
		}

		List<QueryParamVo> queryParamVo = transParam(paraStr);
		//转换带逗号的数字的参数
		CommonSqlUtil.transNumberParam(queryParamVo);
		String checkResult = check(query, queryParamVo);
		if (checkResult != null) {
			throw new IllegalArgumentException(checkResult);
		}
		List<Map<String, Object>> rows = query(query, queryParamVo, page);
		//设置时间取值		
		DataSetVo<Map<String, Object>> dataSetVo = new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
				rows);
		return dataSetVo;
	}
}
