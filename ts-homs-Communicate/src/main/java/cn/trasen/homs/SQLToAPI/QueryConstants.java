package cn.trasen.homs.SQLToAPI;

import java.text.SimpleDateFormat;
import java.util.regex.Pattern;

public class QueryConstants {

	// 是否显示;1是;0:否
	public static final Integer YES = 1;
	public static final Integer NO = 0;

	// 条件类型1：行条件 2：列条件
	public static final Integer CONDITION_TYPE_ROW = 1;
	public static final Integer CONDITION_TYPE_COL = 2;
	
	public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
	public static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	public static final SimpleDateFormat MONTH_FORMAT = new SimpleDateFormat("yyyy-MM");
	public static final Integer DATE_FORMAT_DATE = 1,DATE_FORMAT_MONTH = 2,DATE_FORMAT_YEAR = 3;
	
	public static final Pattern spacePt = Pattern.compile("\\s{1,}");
	public static final Pattern NUMBER_PT = Pattern.compile("^\\-?\\d*\\.?\\d+$");
	public static final Pattern dateSplitPt = Pattern.compile(",|:");
	
	public static final String PAGE_TYPE_QUERY = "QUERY",PAGE_TYPE_PROCFUN = "PROCFUN";
	
}
