package cn.trasen.homs.SQLToAPI.permission.interceptor;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.SQLToAPI.permission.DataScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.MyPermissionConfig;
import cn.trasen.homs.SQLToAPI.permission.OrgMapComponent;
import cn.trasen.homs.SQLToAPI.permission.OrgMapHolder;
import cn.trasen.homs.SQLToAPI.permission.SpringUtil;
import cn.trasen.homs.SQLToAPI.permission.StaffMapComponent;
import cn.trasen.homs.SQLToAPI.permission.StaffMapHolder;
import cn.trasen.homs.SQLToAPI.permission.ThpUserScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.model.DataScopeModel;
import cn.trasen.homs.SQLToAPI.permission.model.ThpsUser;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName DataScopeInterceptor
 * @Description 权限拦截器
 * @date 2020年4月26日 下午7:35:07
 */
@Slf4j
public class TokenInterceptor extends HandlerInterceptorAdapter {

    private String verifyUrl;

    // 是否开启userId
    private Boolean userIdEnabled = Boolean.FALSE;
    
    private OrgMapComponent orgMapComponent;

    private StaffMapComponent staffMapComponent;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        super.preHandle(request, response, handler);
        String requestUri = request.getRequestURI();
        String contextPath = request.getContextPath();
        String url = requestUri.substring(contextPath.length());
        log.debug("TokenInterceptor url:{} start", url);
        ThpsUser userInfo = getCurrentInfo(request);
        
        if(userInfo != null) {
        	ThpUserScopeContextHolder.set(userInfo);
        }
        // check null
        MyPermissionConfig myPermissionConfig = (MyPermissionConfig) SpringUtil.getBean("myPermissionConfig");
        
        
        // 无数据权限控制
        if (myPermissionConfig.getTableMap().size() < 1 && myPermissionConfig.getTablePatternMap().size() < 1) {
            return true;
        }

        DataScopeModel dataScopeModel = new DataScopeModel();
        if (userInfo != null) {
           
            log.debug("======userInfo====SysRoleCode===>{}", userInfo.getSysRoleCode());
            log.debug("======userInfo====OrgRang===>{}", userInfo.getOrgRang());
            log.debug("===myPermissionConfig===>{}", JSONObject.toJSONString(myPermissionConfig));
            log.debug("===dataScopeModel===>{}", JSONObject.toJSONString(dataScopeModel));
            if (userInfo.getSysRoleCode().contains(myPermissionConfig.getRoleAll())) {
                log.debug("======u SysRoleCode= ==RoleAll===>{}  {}", userInfo.getSysRoleCode(), myPermissionConfig.getRoleAll());
            }

            dataScopeModel.setDataScope(userInfo.getSysRoleCode());
            // 管理员 走所有数据
            if (StringUtils.isNotBlank(userInfo.getSysRoleCode()) && userInfo.getSysRoleCode().contains(myPermissionConfig.getRoleAll())) {
                dataScopeModel.setHasAllData(Boolean.TRUE);
                DataScopeContextHolder.set(dataScopeModel);
                log.debug("TokenInterceptor url:{} finisehd==>走RoleAll所有数据", url);
                return true;
            }

            // 走科室数据权限
            if (StringUtils.isNotBlank(userInfo.getSysRoleCode()) && userInfo.getSysRoleCode().contains(myPermissionConfig.getRoleOrg())) {
                String orgRang = userInfo.getOrgRang();
                if (StringUtils.isNotBlank(orgRang)) {
                    // 获取机构映射缓存
                    String[] orgRangList = orgRang.substring(1, orgRang.length() - 1).split(",");
                    Set<String> deptList = new HashSet<>();
                    for (String dept : orgRangList) {
                        if (StringUtils.isNotBlank(dept)) {
                            deptList.add(dept.replace("'", ""));
                        }
                    }
                    // 是否启用映射表
                    boolean isEnabledOrgMap = (orgMapComponent == null) ? false : orgMapComponent.getOrgMapProperties().isEnabled();
                    log.debug("科室映射是否开启 isEnabledOrgMap:{}", isEnabledOrgMap);
                    if (!isEnabledOrgMap) {
                        dataScopeModel.setDeptList(deptList);
                        log.debug("科室权限列表：{}",  StringUtils.join(deptList.toArray(), ","));
                    } else {
                        // 加载映射表
                        Set<String> orgMapList = new HashSet<>();
                        if (deptList != null && !deptList.isEmpty()) {
                            deptList.stream().forEach(item -> {
                                String targetCode = OrgMapHolder.getInstance().get(item);
                                log.debug("科室映射sourceCode:{},targetCode:{}", item, targetCode);
                                if (StringUtils.isNotBlank(targetCode)) {
                                    orgMapList.add(targetCode);
                                }
                            });
                        }
                        log.debug("科室权限映射列表：{}",  StringUtils.join(orgMapList.toArray(), ","));
                        dataScopeModel.setDeptList(orgMapList);
                    }
                    
                    
                    DataScopeContextHolder.set(dataScopeModel);
                }
                // 如果 没有科室
                if (null == dataScopeModel.getDeptList() || dataScopeModel.getDeptList().size() < 1) {
                    log.debug("TokenInterceptor url:{} finisehd==>走科室数据时科室为空，没访问权限", url);
                    dataScopeModel.setHasVisitData(Boolean.FALSE);
                    return true;
                }
                log.debug("TokenInterceptor url:{} finisehd-->走科室数据权限。。", url);
                return true;
            }

            // 默认只走个人数据
            dataScopeModel.setHasOnlySelfData(Boolean.TRUE);
            DataScopeContextHolder.set(dataScopeModel);
            boolean isEnabledStaffMap = (staffMapComponent == null) ? false : staffMapComponent.getStaffMapProperties().isEnabled();
            if (!isEnabledStaffMap) {
            	if(!userIdEnabled) {
            		dataScopeModel.setStaffCode(userInfo.getUsercode());
            	} else {
            		dataScopeModel.setStaffCode(userInfo.getId());
            	}
            } else {
            	if(!userIdEnabled) {
            		dataScopeModel.setStaffCode(StaffMapHolder.getInstance().get(userInfo.getUsercode()));
            	} else {
            		dataScopeModel.setStaffCode(StaffMapHolder.getInstance().get(userInfo.getId()));
            	}
            }

            //如果 没有员工id
            if (org.springframework.util.StringUtils.isEmpty(dataScopeModel.getStaffCode())) {
                log.debug("TokenInterceptor url:{} finisehd==>只走个人数据时员工id为空，没访问权限", url);
                dataScopeModel.setHasVisitData(Boolean.FALSE);
                return true;
            }

            log.debug("TokenInterceptor url:{} finisehd--只走个人数据访问权限", url);
            return true;

        }

        //没认证走所有数据
        if (myPermissionConfig.isNoLoginUserCanVisitAllData()) {
            log.debug("TokenInterceptor url:{} finisehd-->没认证走所有数据访问权限", url);
            dataScopeModel.setHasAllData(Boolean.TRUE);
        } else {
            log.debug("TokenInterceptor url:{} finisehd-->没认证 没有数据访问权限", url);
            dataScopeModel.setHasVisitData(Boolean.FALSE);
        }
        DataScopeContextHolder.set(dataScopeModel);

        return true;
    }

    public void falseResult(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        Map<String, Object> resultBody = new HashMap<>();
        resultBody.put("statusCode", 200);
        resultBody.put("success", false);
        resultBody.put("message", "无数据权限");
        resultBody.put("object", null);
        ObjectMapper objectMapper = new ObjectMapper();
        response.getWriter().println(objectMapper.writeValueAsString(resultBody));
        return;
    }

    /**
     * @param request
     * @return ThpsUser
     * @Title getCurrentInfo
     * @Description 获取当前人
     * @date 2020年4月27日 下午10:01:58
     * <AUTHOR>
     */
    private ThpsUser getCurrentInfo(HttpServletRequest request) {
        String token = request.getHeader("token");
        if(StringUtils.isEmpty(token)) {
        	token = request.getParameter("token");
        }
        if (StringUtils.isEmpty(token)) {
            token = getCookie(request);
        }
        log.debug("token:{}", token);
        ThpsUser userInfo = null;
        if (StringUtils.isNotBlank(token)) {
            // 获取用户信息
            InputStream is = null;
            BufferedReader bf = null;
            String body = "";
            try {
                CloseableHttpClient client = HttpClients.custom().build();
                log.debug("verifyUrl :{}", verifyUrl);
                String tokenUrl = verifyUrl + "?token=" + URLEncoder.encode(token, StandardCharsets.UTF_8.name());
                HttpGet httpGet = new HttpGet(tokenUrl);
                // 请求头设置 token
                httpGet.setHeader("token", token);
                is = client.execute(httpGet).getEntity().getContent();
                bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                StringBuffer buffer = new StringBuffer();
                String line = "";
                while ((line = bf.readLine()) != null) {
                    buffer.append(line);
                }
                body = buffer.toString();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                    }
                }
                if (bf != null) {
                    try {
                        bf.close();
                    } catch (IOException e) {
                    }
                }
            }
            log.debug("body:{}", body);
            JSONObject verify = null;
            try {
                verify = new ObjectMapper().readValue(body, JSONObject.class);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            if (verify != null && "0".equals(verify.getString("code"))) {
                userInfo = verify.getObject("uid", ThpsUser.class);
            }
        }
        return userInfo;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        DataScopeContextHolder.remove();
        ThpUserScopeContextHolder.remove();
        super.afterCompletion(request, response, handler, ex);
    }

    /**
     * @param request
     * @return String
     * @Title getCookie
     * @Description 获取token
     * @date 2020年4月26日 下午7:58:14
     * <AUTHOR>
     */
    private String getCookie(HttpServletRequest request) {
        String token = null;
        Cookie[] cookies = ((HttpServletRequest) request).getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                if ("THPMSCookie".equals(cookie.getName())) {
                    token = cookie.getValue();
                    break;
                }
            }
        }
        return token;
    }

    public String getVerifyUrl() {
        return verifyUrl;
    }

    public void setVerifyUrl(String verifyUrl) {
        this.verifyUrl = verifyUrl;
    }

    public OrgMapComponent getOrgMapComponent() {
        return orgMapComponent;
    }

    public void setOrgMapComponent(OrgMapComponent orgMapComponent) {
        this.orgMapComponent = orgMapComponent;
    }

    public StaffMapComponent getStaffMapComponent() {
        return staffMapComponent;
    }

    public void setStaffMapComponent(StaffMapComponent staffMapComponent) {
        this.staffMapComponent = staffMapComponent;
    }

	public Boolean getUserIdEnabled() {
		return userIdEnabled;
	}

	public void setUserIdEnabled(Boolean userIdEnabled) {
		this.userIdEnabled = userIdEnabled;
	}
}
