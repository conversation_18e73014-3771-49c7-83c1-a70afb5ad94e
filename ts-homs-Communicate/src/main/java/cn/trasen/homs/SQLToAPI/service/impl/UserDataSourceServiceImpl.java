package cn.trasen.homs.SQLToAPI.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil;
import cn.trasen.homs.SQLToAPI.vo.DataSourceTestVo;
import cn.trasen.homs.bean.sso.UserDataSource;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.HISPassword;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.sso.DatasourceFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户定义数据源-服务实现
 * @Author:huangkui
 * @Date: 2019年5月28日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@CacheConfig(cacheNames = "UserDataSource")
@Slf4j
@RequiredArgsConstructor
public class UserDataSourceServiceImpl implements UserDataSourceService {
	
//	@Value("${trasen.dataSource.load}")
//	private String dataSourceLoadUrl;
//	@Value("${trasen.dataSource.list}")
//	private String dataSourceListUrl;
	
	@Resource
	DatasourceFeignClient client;


	@Override
//	@Cacheable(value = "UserDataSource", key = "#id")
	public UserDataSource selectDecrypt(String id) {
		PlatformResult<UserDataSource> resultVo = client.getDatasourceInfo(id);
		UserDataSource ds = resultVo.getObject();
		try {
			String decPwd = HISPassword.HISDecrypt(ds.getPassword());
			if(!StringUtils.hasText(decPwd)) {
				decPwd = ds.getPassword();
			}
			ds.setPassword(decPwd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
//		log.info("dataSourceLoadUrl :{}",dataSourceLoadUrl+"/"+id);
//		String dsString = new RestTemplate().getForObject(dataSourceLoadUrl+"/"+id, String.class);
//		JSONObject object = JSONObject.parseObject(dsString);
//		UserDataSource ds = null;
//		if(object != null && object.getBooleanValue("success")) {
//			ds = object.getObject("object", UserDataSource.class);
//			try {
//				String decPwd = HISPassword.HISDecrypt(ds.getPassword());
//				if(!StringUtils.hasText(decPwd)) {
//					decPwd = ds.getPassword();
//				}
//				ds.setPassword(decPwd);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}		
		return ds;
	}
	
	@Override
	public Map<String, String> names(){
		Page page = new Page();
		page.setPageNo(1);
		page.setPageSize(10000);
		DataSet<UserDataSource> datasourceList = client.getDatasourceList(page, null, null, null);
		Map<String, String> map = new HashMap<String, String>();
		if(null != datasourceList){
			List<UserDataSource> list = datasourceList.getRows();
			if(null != datasourceList){
				for(UserDataSource vo : list){
					String dsName = vo.getDsName();
					String id = vo.getId();
					if(dsName != null && id != null) {
						map.put(id, dsName);
					}
				}
			}
		}
		
//		HttpHeaders headers = new HttpHeaders();
//		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//		MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
//		multiValueMap.add("pageNo", "1");
//		multiValueMap.add("pageSize", "10000");		
//		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(multiValueMap, headers);		
//		String jsonStr = new RestTemplate().postForObject(dataSourceListUrl, requestEntity, String.class);
//		Map<String, String> map = new HashMap<String, String>();
//		if(StringUtils.hasText(jsonStr)) {
//			JSONObject obj = JSONObject.parseObject(jsonStr);
//			if(obj.containsKey("rows")) {
//				JSONArray arrays = obj.getJSONArray("rows");
//				for(int i=0;i<arrays.size();i++) {
//					obj = arrays.getJSONObject(i);
//					String dsName = obj.getString("dsName");
//					String id = obj.getString("id");
//					if(dsName != null && id != null) {
//						map.put(id, dsName);
//					}
//				}
//			}
//		}
		return map ;
	}

	@Override
	public List<Object> testConnection(DataSourceTestVo testVo) {
		List<Map<String, Object>> rows = CommonSqlUtil.queryFirstPageUseMybatis(testVo.getDataSource(), testVo.getSql());
		List<Object> rs = new ArrayList<Object>();
		rs.addAll(rows);
		return rs ;
	}

	@Override
	@CacheEvict(value = "UserDataSource", key = "#id")	
	public Boolean change(String id, String dsName) {
		CommonSqlUtil.removeSessionFactory(dsName);
		return true;
	}

	

}
