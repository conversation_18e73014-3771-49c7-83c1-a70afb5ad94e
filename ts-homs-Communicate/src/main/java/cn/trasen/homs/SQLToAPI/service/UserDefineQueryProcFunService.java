package cn.trasen.homs.SQLToAPI.service;

import java.util.List;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.vo.report.ProcFunResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineProcFunVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
**********************************************   
* @Description: 用户自定义存储过程、函数查询-服务定义
* @Author:huangkui  
* @Date: 2019年7月24日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public interface UserDefineQueryProcFunService {

	/**
	* @Title: preQuery
	* @Description: 预查询
	* @Author:huangkui
	* @Date: 2019年5月27日 下午3:44:23
	* @param sql
	* @param dsId
	 * @param conditions 
	* @return
	 */
	UserDefineProcFunVo preQuery(UserDefineProcFunVo query, List<QueryParamVo> queryParamList);

	/**
	* @Title: selectById
	* @Description: 根据ID查询配置，并返回定义视图对象
	* @Author:huangkui
	* @Date: 2019年5月27日 下午5:32:12
	* @param id
	* @return
	 */
	UserDefineProcFunVo selectById(String id);
	
	/**
	* @Title: copyFrom
	* @Description: 将数据库查询对象转换成查询视图对象
	* @Author:huangkui
	* @Date: 2019年6月12日 下午2:02:50
	* @param query
	* @return
	 */
	UserDefineProcFunVo copyFrom(UserDefineService query);
	
	/**
	* @Title: transParam
	* @Description: 解析参数
	* @Author:huangkui
	* @Date: 2019年5月27日 下午5:45:52
	* @param paramStr 参数json，例：[{name:\"n1\",value:123},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]
	* @return
	 */
	List<QueryParamVo> transParam(String paramStr);

	/**
	* @Title: check
	* @Description: 检查自定义存储过程、函数，如果有问题返回友好提示信息，没有问题则返回null。
	* 		如果传入了参数列表，还会将配置了默认值但未提交参数的加入到参数列表
	* @Author:huangkui
	* @Date: 2019年5月27日 下午5:50:36
	* @param query
	* @param queryParamVo
	* @return
	 */
	String check(UserDefineProcFunVo query, List<QueryParamVo> queryParamList, boolean checkColumn);

	/**
	 * @Title: query
	* @Description: 用户自定义存储过程、函数查询数据
	* @Author:huangkui
	* @Date: 2019年5月27日 下午5:53:30
	* @param query
	* @param queryParamVo
	 * @param page 
	* @return
	 */
	List<ProcFunResultVo> query(UserDefineProcFunVo query, List<QueryParamVo> queryParamVo);
	
	/**
	* @Title: saveOrUpdate
	* @Description: 增加或者修改
	* @Author:huangkui
	* @Date: 2019年5月28日 上午11:46:43
	* @param id
	* @param query
	* @return
	 */
	UserDefineProcFunVo saveOrUpdate(UserDefineService query);

	/**
	* @Title: delete
	* @Description: 删除自定义存储过程、函数
	* @Author:huangkui
	* @Date: 2019年5月28日 上午11:50:50
	* @param id
	* @return
	 */
	Integer delete(String id);

	/**
	* @Title: parseCondition
	* @Description: 解析SQL，获取到条件列表
	* @Author:huangkui
	* @Date: 2019年5月29日 下午4:14:31
	* @param sql SQL语句，条件使用?
	* @return
	 */
	UserDefineProcFunVo parseCondition(String sql);
	
	/**
	* @Title: parseCondition
	* @Description: 解析SQL，获取到条件列表
	* @Author:huangkui
	* @Date: 2019年5月29日 下午4:14:31
	* @param sql SQL语句，条件使用?
	* @return
	 */
	UserDefineProcFunVo parseConditionSimple(String sql);

	/**
	* @Title: list
	* @Description: 自定义存储过程、函数列表
	* @Author:huangkui
	* @Date: 2019年6月4日 下午4:06:42
	* @param condition
	* @param page
	* @return
	 */
	List<UserDefineProcFunVo> list(String condition, Page page);

}
