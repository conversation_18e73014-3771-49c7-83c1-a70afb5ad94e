package cn.trasen.homs.SQLToAPI.utils;

import java.io.IOException;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.apache.commons.dbutils.QueryRunner;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.apache.shiro.util.Assert;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.trasen.BootComm.utils.DruidDataSourceUtils;
import cn.trasen.homs.SQLToAPI.QueryConstants;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg.DateCfg;
import cn.trasen.homs.SQLToAPI.permission.ITableFieldConditionDecision;
import cn.trasen.homs.SQLToAPI.permission.PermissionUtil;
import cn.trasen.homs.SQLToAPI.permission.SqlConditionHelper;
import cn.trasen.homs.SQLToAPI.permission.interceptor.DataScopeInterceptor;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcFunResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryConditionVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnVo;
import cn.trasen.homs.bean.sso.UserDataSource;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import oracle.sql.CLOB;

/**
 **********************************************
 * @Description: sql查询帮助类
 * @Author:huangkui
 * @Date: 2019年5月9日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Slf4j
public class CommonSqlUtil {

	private static Map<String, SqlSessionFactory> factoryMap = new HashMap<>();
	private static Pattern spacePt = Pattern.compile("\\s{1,}");
	private static Pattern numPt = Pattern.compile("^\\-?\\d*(\\d+\\,)*(\\d+\\.)*\\d+$");

	public static void parseIdAndParasFromJson(JSONObject object,BiConsumer<String, String> idAndParaConsumer) {
		String id = object.get("id").toString();
		if(object.containsKey("parameters")) {
			Object obj = object.get("parameters");
			String paraString = "";
			if(obj instanceof JSON) {
				paraString = ((JSON)obj).toJSONString();
			}else {
				paraString = obj.toString();
			}
			idAndParaConsumer.accept(id, paraString);
		}else {
			Set<String> keys = object.keySet();
			if(keys.size() == 1) {
				//说明只有一个ID，没有参数
				idAndParaConsumer.accept(id,"");
			}else {
				JSONArray jsonArray = new JSONArray(keys.size()-1);
				//说明使用字段传的
				for(String key:keys) {
					if(key.equals("id")) {
						//跳过ID和分页查询参数
						continue;
					}
					JSONObject obj = new JSONObject();
					obj.put("name", key);
					Object pobj = object.get(key);
					String paraString = "";
					if(pobj instanceof JSON) {
						paraString = ((JSON)pobj).toJSONString();
					}else {
						paraString = pobj.toString();
					}
					obj.put("value", paraString);
					jsonArray.add(obj);
				}
				idAndParaConsumer.accept(id,jsonArray.toJSONString());
			}
		}
	}

	public static String loadDefaultParas(List<? extends ProcedureParamVo> conditions, DateDefaultCfg dateCfg) {
		return loadDefaultParas("",conditions,dateCfg);
	}

	public static void transNumberParam(List<QueryParamVo> qvos) {
		//转换带逗号的数字的参数
		for(QueryParamVo vo:qvos) {
			if(StringUtils.hasText(vo.getValue())) {
				if(numPt.matcher(vo.getValue()).find()
						&& vo.getValue().contains(",")) {
					log.info("需要转换数字参数：{}",vo.getValue());
					vo.setValue(vo.getValue().replace(",", ""));
					log.info("转换后：{}",vo.getValue());
				}
			}else if(vo.getList() != null) {
				List<String> newList = new ArrayList<String>();
				for(String v:vo.getList()) {
					if(v != null && numPt.matcher(v).find()
								&& v.contains(",")) {
						log.info("需要转换数字参数：{}",v);
						v = v.replace(",", "");
						newList.add(v);
						log.info("转换后：{}",v);
					}else {
						newList.add(v);
					}
				}
				vo.setList(newList);
			}
		}
	}

	/**
	* @param parameters
	 * @Title: loadDefaultParas
	* @Description: 根据参数默认值获取参数值
	* @Author:huangkui
	* @Date: 2019年7月30日 上午8:38:07
	* @param conditions
	* @return
	 */
	public static String loadDefaultParas(String parameters, List<? extends ProcedureParamVo> conditions, DateDefaultCfg dateCfg) {

		Set<String> qnames = conditions.stream().map(ProcedureParamVo::getName).collect(Collectors.toSet());
		Map<String,String> nvMap = new HashMap<String,String>();
		if(StringUtils.hasText(parameters)) {
			//这里也有可能是无效的参数
			JSONArray array1= JSONObject.parseArray(parameters);
			if(array1 != null && array1.size() > 0) {
				for(int i=0;i<array1.size();i++) {
					JSONObject obj = array1.getJSONObject(i);
					String name = obj.getString("name");
					if(qnames.contains(name)) {
						nvMap.put(name, obj.getString("value"));
					}
				}
			}
		}

		boolean canQuery = true;
		StringBuffer paraSb = new StringBuffer("[");
		if (conditions != null) {
			for (ProcedureParamVo obj : conditions) {
				if(obj instanceof QueryConditionVo) {
					QueryConditionVo vo = (QueryConditionVo)obj;
					if (!vo.isMust()) {
						continue;
					}
				}
				if(nvMap.containsKey(obj.getName())) {
					//说明已经有值了！直接加上 ！
					paraSb.append("{\"name\":\"").append(obj.getName()).append("\",\"value\":\"")
					.append(nvMap.get(obj.getName())).append("\"},");
					continue;
				}

				ProcedureParamVo vo = (ProcedureParamVo) obj;
				String name = vo.getName();
				String value = vo.getDefaultValue();
				boolean isDate = vo.getColumnType() == ColumnType.DATE;
				boolean isDateUnit = vo.getColumnType() == ColumnType.DATEUNIT;
				boolean isPage = vo.getColumnType() == ColumnType.PAGE;
				boolean isRange = vo.isRange();
				if (vo.getColumnType() != ColumnType.STRING
						&& !StringUtils.hasText(value)) {
					//非文字类型不允许默认值为空
					if (isDate || isDateUnit) {
						// 如果是日期类型设置成昨日
						value = QueryConstants.DATE_FORMAT
								.format(new Date(System.currentTimeMillis() - 24 * 3600 * 1000L));
						if(vo.isDateTime()) {
							value = QueryConstants.DATETIME_FORMAT
									.format(new Date(System.currentTimeMillis() - 24 * 3600 * 1000L));
						}else {
							value = QueryConstants.DATE_FORMAT
									.format(new Date(System.currentTimeMillis() - 24 * 3600 * 1000L));
							value = transDateFormat(vo.getDateFormat(),value);
						}
						if(name.equals("begin")) {
							//@@@!!!特殊处理
							vo.setRange(false);
							isRange = false;
						}
						if(isRange) {
							value = value + "~" + value;
						}
					}
					/*else if(isMonth){
						value = QueryConstants.MONTH_FORMAT.format(new Date(System.currentTimeMillis()));
					}
					*/else {
						// 必需，但是没有设置默认值
						canQuery = false;
						break;
					}
				}else {
					//进入此处说明要么是文字类型，要么默认值不为空
					if(isDate || isDateUnit) {
						String[] vs = QueryConstants.dateSplitPt.split(value);
						DateCfg cfg = dateCfg.getByCode(vs[0]);
						if(cfg != null) {
							Date date = dateCfg.getDateByConfig(cfg);
							if(vo.isDateTime()) {
								value = QueryConstants.DATETIME_FORMAT.format(date);
							}else {
								value = QueryConstants.DATE_FORMAT
										.format(date);
								value = transDateFormat(vo.getDateFormat(),value);
							}
							if(name.equals("begin")) {
								//@@@!!!特殊处理
								vo.setRange(false);
								isRange = false;
							}
							if(isRange) {
								value = value + "~" + value;
							}
						}
					}
					/*else if(isMonth) {
						DateCfg cfg = dateCfg.getByCode(value);
						if(cfg != null) {
							Date date = dateCfg.getDateByConfig(cfg);
							value = QueryConstants.MONTH_FORMAT.format(date);
						}
					}
					*/else if(isPage) {
						//页签，不能直接使用默认值
						int begin = value.indexOf(',');
						int end = value.indexOf(';');
						value = value.substring(begin+1,end);
						if(value.charAt(0) == '\'') {
							value = value.substring(1);
						}
						if(value.charAt(value.length()-1) == '\'') {
							value = value.substring(0,value.length()-1);
						}
					}
				}
				paraSb.append("{\"name\":\"").append(name).append("\",\"value\":\"").append(value).append("\"},");
			}
			if (paraSb.length() > 1) {
				paraSb.setLength(paraSb.length() - 1);
			}
			paraSb.append("]");
		}
		if (canQuery) {
			// 都设置了默认值，按照默认值查询
			return paraSb.toString();
		}
		return null;
	}

	private static String transDateFormat(Integer dateFormat, String value) {
		if(value == null || dateFormat == null) {
			return null;
		}
		if(dateFormat == QueryConstants.DATE_FORMAT_MONTH ) {
			return value.substring(0,7);
		}else if(dateFormat == QueryConstants.DATE_FORMAT_YEAR) {
			return value.substring(0,4);
		}
		return value;
	}

	/**
	 * @Title: transferMap
	 * @Description: 将查询结果转换成对应的对象列表。注意：对象类型属性必须只有String 和 BigDicamal 两种类型
	 * @Author:huangkui
	 * @Date: 2019年5月9日 下午4:08:18
	 * @param rows
	 * @param clazz
	 * @return
	 */
	public static <T> List<T> transferMap(List<Map<String, Object>> rows, Class<T> clazz) {
		List<T> result = new ArrayList<>();
		if (rows != null) {
			for (Map<String, Object> row : rows) {
				if (row == null) {
					continue;
				}
				int count = 0;
				T obj = null;
				try {
					obj = clazz.newInstance();
				} catch (InstantiationException | IllegalAccessException e) {
					e.printStackTrace();
					throw new IllegalArgumentException("类不能初始化：" + clazz.getName());
				}
				for (String key : row.keySet()) {
					Object value = row.get(key);
					if (value != null && key.length() > 0) {
						Class<?> paramType = null;
						if (value instanceof String) {
							paramType = String.class;
						} else {
							paramType = BigDecimal.class;
						}
						Method method = BeanUtils.findMethod(clazz,
								"set" + key.substring(0, 1).toUpperCase() + key.substring(1), paramType);
						if (method != null) {
							try {
								if (paramType.equals(String.class)) {
									method.invoke(obj, value.toString());
								} else {
									method.invoke(obj, new BigDecimal(value.toString()));
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
						count++;
					}
				}

				if (count > 0) {
					// 如果返回一行全null，则不加入对象
					result.add(obj);
				}
			}
		}
		return result;
	}

	public static <T> T transferMapOne(List<Map<String, Object>> rows, Class<T> clazz) {
		List<T> list = transferMap(rows, clazz);
		return list.size() > 0 ? list.get(0) : null;
	}

	private static SqlSessionFactory getSessionFactory(String dsName) {
		synchronized (factoryMap) {
			if (DruidDataSourceUtils.getInstance().getDataSource(dsName) != null) {
				return factoryMap.get(dsName);
			} else {
				return null;
			}
		}
	}

	/**
	 * @Title: removeSessionFactory
	 * @Description: 删除指定数据源名称的sessionFactory
	 * @Author:huangkui
	 * @Date: 2019年6月3日 上午10:56:22
	 * @param dsName
	 */
	public static void removeSessionFactory(String dsName) {
		synchronized (factoryMap) {
			factoryMap.remove(dsName);
		}
		DruidDataSourceUtils.getInstance().removeDataSource(dsName);
	}

	public static final int ProcFun_Type_String = 0, ProcFun_Type_Number = 1, ProcFun_Type_Cursor = 2,
			ProcFun_Type_DataSet = 3,
			ProcFun_Input = 0, ProcFun_Output = 1, ProcFun_InOut = 2;
	private static final Pattern namePattern = Pattern.compile(":(\\w+)[\\s\\r\\n]*[,\\)]", Pattern.MULTILINE|Pattern.CASE_INSENSITIVE);

	@Data
	public static class ProcFunIO {
		private Integer type;
		private Integer ioType;
		private String name;
		private String value;

		public ProcFunIO(Integer ioType, String name, Integer type, String value) {
			this.ioType = ioType;
			this.type = type;
			this.name = name;
			this.value = value;
		}
	}



	/**
	 * @Title: callProcFun
	 * @Description: 执行存储过程或函数
	 * @Author:huangkui
	 * @Date: 2019年7月25日 下午3:09:33
	 * @param userDs
	 * @param sql
	 * @param ios
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static List<ProcFunResultVo> callProcFun(UserDataSource userDs, String sql, List<ProcFunIO> ios) {
		Map<String, Object> map = new HashMap<>();
		String rsName = "CURSOR";
		log.info("参数：{}", ios.toString());
		Map<String, String> vMap = new HashMap<String, String>();
		for (ProcFunIO pio : ios) {
			String replaceStr = replaceInOut(pio, map);
			if(replaceStr.indexOf("jdbcType=DATASET")!=-1) {
				//
				rsName = pio.name;
				sql = sql.replace(":" + pio.name, "").trim();
				if(sql.startsWith("=")) {
					sql = sql.substring(1);
				}
				map.remove(rsName);
			}else {
				vMap.put(pio.name, replaceStr);
			}
		}
		//替换
		Matcher mc = namePattern.matcher(sql);
		StringBuffer sqlBuffer = new StringBuffer();
		int lastEnd = 0;
		while(mc.find()) {
			String pname = mc.group(1);
			String value = vMap.get(pname);
			int start = mc.start();
			sqlBuffer.append(sql.substring(lastEnd, start));
			sqlBuffer.append(value);
			lastEnd = mc.end(1);
		}
		sqlBuffer.append(sql.substring(lastEnd));
		sql = sqlBuffer.toString();

		map.put("procedure", "{" + sql + "}");
		log.info("将执行SQL：{}->{}",sql,map);
		SqlSession session = null;
		List<ProcFunResultVo> list = new ArrayList<>();
		try {
			// 自动提交
			session = getSessionFactory(userDs).openSession(true);
			switch (userDs.getDsType()) {
			case 1:
				// oracle 只有Oracle支持游标返回，其他数据库不能用游标
				log.info("执行callProcedure...");
				session.select("cn.trasen.query.report.dao.UserDefineSqlMapper.callProcedure", map, null);
				break;
			default:
				log.info("执行callReturnDataSet...");
				List<?> rs = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.callReturnDataSet", map);
				map.put(rsName, rs);
				break;
			}

			for (ProcFunIO pio : ios) {
				if (pio.ioType == ProcFun_Input) {
					// 输入参数
					continue;
				}
				String resultName = pio.name;
				if (pio.type == ProcFun_Type_Cursor || pio.type == ProcFun_Type_DataSet) {
					ProcFunResultVo vo = new ProcFunResultVo();
					vo.setName(resultName);
					vo.setResult((List) map.get(resultName));
					list.add(vo);
				} else {
					ProcFunResultVo vo = new ProcFunResultVo();
					vo.setName(resultName);
					Object obj = (Object) map.get(resultName);
					HashMap<String, Object> rt = new HashMap<>();
					rt.put(resultName, obj);
					vo.setResult(Arrays.asList(rt));
					list.add(vo);
				}
			}
			log.debug(String.format("执行函数成功：size=%d", list.size()));
		} catch (Exception e) {
			log.error("执行函数错误："+sql, e);
			throw new QueryException(e.getMessage(), e);
		} finally {
			if (session != null) {
				session.close();
			}
		}
		return list;
	}

	private static Pattern allNumPt = Pattern.compile("^\\d+$");

	public static void treeToList(List<TableColumnVo> cols, List<TableColumnVo> colList) {
		for (TableColumnVo vo : cols) {
			if (!CollectionUtils.isEmpty(vo.getChildren())) {
				treeToList(vo.getChildren(), colList);
			} else if (!vo.isVirtual()) {
				colList.add(vo);
			}
		}
	}

	@SuppressWarnings("rawtypes")
	public static List<Map<String, Object>> transResult(List<TableColumnVo> cols, List<Map<String, Object>> list) {
		// 判断游标列
		TableColumnVo cursorCol = cols.stream().filter(c -> "CURSOR".equalsIgnoreCase(c.getName())).findFirst().orElse(null);
		if(cursorCol != null) {
			return list;
		}
		List<Map<String, Object>> result = new LinkedList<>();

		list = list.stream().filter(t->!CollectionUtils.isEmpty(t)).collect(Collectors.toList());

		if (list != null) {
			Object[] formatArray = new Object[cols.size()];
			for (Map<String, Object> map : list) {
				Map<String, Object> rowMap = new LinkedHashMap<>();
				for (int i = 0; i < cols.size(); i++) {
					TableColumnVo col = cols.get(i);
					if (col.isVirtual()) {
						// 虚节点不需要获取对应数据
						continue;
					}
					String name = col.getName();
					Object obj = null;
					int idx = name.indexOf('.');
					if (idx != -1) {
						// 列名含有.时，mybatis会一级一级创建map存入数据
						int begin = 0;
						Map data = map;
						String n = null;
						do {
							n = name.substring(begin, idx);
							data = (Map) data.get(n);
							begin = idx + 1;
							idx = name.indexOf('.', begin);
						} while (idx != -1 && data != null);
						if (data != null) {
							obj = data.get(name.substring(begin));
						} else {
							log.warn(String.format("没有找到列%s", name));
						}
					} else {
						obj = map.get(name);
					}
					if (obj == null) {
						rowMap.put(name, null);
					} else {
						ColumnType type = col.getType();
						String value = null;
						switch (type) {
						case STRING:
							value = obj.toString();
							if(obj instanceof oracle.sql.CLOB) {
								//读取oracle的CLOB字段
								value = readOracleClob((oracle.sql.CLOB)obj);								
							}
							break;
						case DATE:
							if (formatArray[i] == null) {
								String format = col.getDateFormat();
								if (!StringUtils.hasText(format)) {
									// 默认只到日期
									format = "yyyy-MM-dd";
								}
								formatArray[i] = new SimpleDateFormat(format);
							}
							SimpleDateFormat df = (SimpleDateFormat) formatArray[i];
							if (obj instanceof String) {
								// 字符 日期
								value = (String) obj;
								Date date = DateUtils.stringToDate(value);
								if (date != null) {
									value = df.format(date);
								}
							} else if (obj instanceof Date) {
								// sql.Date、Time、Timestamp都继承于util.Date
								value = df.format((Date) obj);
							} else {
								value = obj.toString();
								if (allNumPt.matcher(value).find()) {
									// 如果是纯数字，表示是时间戳
									value = df.format(new Date(Long.parseLong(value)));
								}
							}
							break;
						case NUMBER:
							if (formatArray[i] == null) {
								if (col.isNumPercent()) {
									formatArray[i] = DecimalFormat.getPercentInstance();
								} else {
									formatArray[i] = DecimalFormat.getInstance();
								}
							}
							NumberFormat nf = (NumberFormat) formatArray[i];
							nf.setMaximumFractionDigits(2);
							BigDecimal num = new BigDecimal(obj.toString());
							value = nf.format(num.doubleValue());
							break;
						default:
							break;
						}
						rowMap.put(name, value);
					}
				}
				result.add(rowMap);
			}
		}
		return result;
	}

	private static String readOracleClob(CLOB clob) {
		try {
			StringBuffer sb = new StringBuffer();
			Reader reader = clob.characterStreamValue();
			char[] cbuf = new char[10240];
			int size = 0;
			do{
				size = reader.read(cbuf);	
				if(size < 0) {
					break;
				}else {
					sb.append(new String(cbuf,0,size));
				}
			}while(true);
			return sb.toString();
		} catch (UnsupportedEncodingException | SQLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	private static String replaceInOut(ProcFunIO out, Map<String, Object> map) {
		int ioType = out.ioType;
		if (ioType == ProcFun_Input) {
			StringBuffer value = new StringBuffer("#{");
			String inName = out.name;
			value.append(inName);
			switch (out.type) {
			case ProcFun_Type_String:
				value.append(",mode=IN,jdbcType=VARCHAR");
				map.put(inName, out.value);
				break;
			case ProcFun_Type_Number:
				value.append(",mode=IN,jdbcType=DECIMAL");
				map.put(inName, new BigDecimal(out.value));
				break;
			default:
				throw new IllegalArgumentException("输入参数类型错误");
			}
			value.append("}");
			return value.toString();
		} else {
			StringBuffer outBuf = new StringBuffer("#{");
			String outName = out.name;
			outBuf.append(outName);
			Object value = null;
			if (out.ioType == ProcFun_InOut) {
				if (out.type == ProcFun_Type_Cursor) {
					// 输入输出参数不能为游标
					throw new IllegalArgumentException("输入输出参数不能为游标类型");
				}
				outBuf.append(",mode=INOUT,jdbcType=");
				//输入输出参数需要将值放入map
				if(out.type == ProcFun_Type_String) {
					value = out.value;
				}else if(out.type == ProcFun_Type_Number) {
					value = new BigDecimal(out.value);
				}
			} else {
				outBuf.append(",mode=OUT,jdbcType=");
			}
			if("CURSOR".equalsIgnoreCase(outName)) {
				// 游标
				outBuf.append("CURSOR,resultMap=cursorMap");
			} else {
				switch (out.type) {
				case ProcFun_Type_String:
					outBuf.append("VARCHAR");
					if(value == null) {
						value = "";
					}
					break;
				case ProcFun_Type_Number:
					outBuf.append("DECIMAL");
					if(value == null) {
						value = new BigDecimal(0);
					}
					break;
				case ProcFun_Type_Cursor:
					outBuf.append("CURSOR,resultMap=cursorMap");
					break;
				case ProcFun_Type_DataSet:
					outBuf.append("DATASET");
					break;
				default:
					throw new IllegalArgumentException("返回值类型错误");
				}
			}
			
			outBuf.append("}");
			map.put(outName, value);
			return outBuf.toString();
		}
	}

	/**
	 * 非分页查询
	 * @param userDs
	 * @param sql
	 * @param page
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2019年12月30日 下午5:59:37
	 */
	public static List<Map<String, Object>> queryNoPageUseMybatis(UserDataSource userDs, String sql, Page page){
		return queryUseMybatis(userDs,sql,page,false);
	}

	private static List<Map<String, Object>> queryUseMybatis(UserDataSource userDs, String sql, Page page, boolean pageing) {
		if (page.sord == null) {
			page.sord = "asc";
		}

		boolean haveOrder = false;
		// 处理排序
		if (StringUtils.hasText(page.getSidx())) {
			int idx = sql.toLowerCase().lastIndexOf("order");
			int byIdx = sql.toLowerCase().indexOf("by", idx + 5);
			if (idx != -1 && byIdx > idx) {
				int count = 0;
				for (int i = byIdx + 2; i < sql.length(); i++) {
					char c = sql.charAt(i);
					if (c == '(') {
						count++;
					} else if (c == ')') {
						count--;
					}
				}
				if (count != 0) {
					// 说明order by 在子查询中
				} else {
					haveOrder = true;
				}
			}
			String orderStr = "";
			if (haveOrder) {
				orderStr = sql.substring(byIdx + 2).trim();
				if (orderStr.length() > 0) {
					orderStr = "," + orderStr;
				}
			}else {
				idx = sql.length();
			}
			if (page.sidx.indexOf('.') != -1) {
				// 说明是复杂列名的字段
				String col = findColumnInSql(sql, page.sidx);
				if (col != null) {
					sql = sql.substring(0, idx) + " order by " + col + " " + page.sord + orderStr;
				} else {
					log.warn("没有找到排序字段，将忽略排序！{}-{}", sql, page.sidx);
				}
			} else {
				sql = sql.substring(0, idx) + " order by " + page.sidx + " " + page.sord + orderStr;
			}
		}

		//需要分页
		StringBuffer countSql = new StringBuffer();
		long tail = -1;
		String sqlReplace = null;
		if(pageing) {
			// 先查询多少记录
			tail = System.currentTimeMillis() % 10000;
			countSql.append("select count(1) C");
			countSql.append(tail).append(" from ( ");
			sqlReplace = spacePt.matcher(sql).replaceAll(" ");
			int orderIdx = sqlReplace.toLowerCase().lastIndexOf("order by");
			if (haveOrder && orderIdx != -1) {
				if (sqlReplace.indexOf('(', orderIdx) == -1) {
					// 计算记录数时，可以去掉order by来节省时间
					sqlReplace = sqlReplace.substring(0, orderIdx);
				}
			}else{
				if(orderIdx != -1){
					// 计算记录数时，可以去掉order by来节省时间
					sqlReplace = sqlReplace.substring(0, orderIdx);
				}
			}
			countSql.append(sqlReplace);
			countSql.append(" ) T").append(tail);
		}

		SqlSession session = null;
		int rowCount = 0;
		List<?> list = null;

		try {
			// 自动提交
			session = getSessionFactory(userDs).openSession(true);
			if(pageing) {
				try {
					list = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.query", countSql.toString());
				} catch (Exception e) {
					// TODO: handle exception
					log.warn("执行计数查询失败。", e);
					// 继续不去掉order by再尝试查询记录数
					countSql = new StringBuffer("Select count(1) C");
					countSql.append(tail).append(" from ( ");
					sqlReplace = spacePt.matcher(sql).replaceAll(" ");
					countSql.append(sqlReplace);
					countSql.append(" ) T").append(tail);
				}
				if (list == null) {
					list = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.query", countSql.toString());
				}
				if (list != null && list.size() > 0) {
					Map<String, Object> map = (Map<String, Object>) list.get(0);
					rowCount = Integer.parseInt(map.get("C" + tail).toString());
				}
				list = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.query", sql, page);
				session.commit();
			}else {
				//非分页查询进程处理！
//				list = querySql(sql,userDs);
				list = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.query", sql);
			}
		} catch (Exception e) {
			log.error("查询错误：" + e.getMessage()+"，SQL--》"+sql, e);
			throw new QueryException(e.getMessage(), e);
		} finally {
			if (session != null) {
				session.close();
			}
		}

		if(pageing) {
			page.setTotalCount(rowCount);
			if (rowCount % page.getPageSize() > 0) {
				page.setTotalPages(rowCount / page.getPageSize() + 1);
			} else {
				page.setTotalPages(rowCount / page.getPageSize());
			}
		}else {
			page.setTotalCount(list == null?0:list.size());
			page.setTotalPages(1);
			page.setPageSize(list==null||list.size()==0?1:list.size());
			page.setPageNo(1);
		}

		List<Map<String, Object>> result = new ArrayList<>();
		if (list != null && list.size() > 0) {
			for (Object obj : list) {
				result.add((Map<String, Object>) obj);
			}
		}
		return result;
	}
	
	private static DataSource getDataSource( UserDataSource userDs) {
		DruidDataSourceUtils druidDs = DruidDataSourceUtils.getInstance();
		DataSource ds = druidDs.getDataSource(userDs.getDsName());
		if (ds == null) {
			ds = druidDs.buildDataSource(userDs.getDsName(), userDs.getDsType(), userDs.getHost(), userDs.getPort(),
					userDs.getDbName(), userDs.getUserName(), userDs.getPassword(), userDs.getOraMode(), userDs.getAppendCode());
			Assert.notNull(ds, String.format("不能创建数据源:%s,请检查数据源相关参数!", userDs.getDsName()));
		}
		return ds;
	}
	
	//默认300兆
	private static int maxSize = 300;
	
	public static void setMaxSize(int max) {
		maxSize = max;
	}
	
	private static List<?> querySql(String sql, UserDataSource userDs) throws SQLException {
		QueryRunner runner = new QueryRunner(getDataSource(userDs));
		log.debug("将进行非分页查询，允许最大数据量：{}兆",maxSize);
		return runner.query(sql, new MyMapListHandler(maxSize));
	}

	/**
	 * @Title: queryPageUseMybatis
	 * @Description: 指定数据源及sql语句进行mybatis分页查询
	 * @Author:huangkui
	 * @Date: 2019年6月4日 上午9:58:02
	 * @param userDs
	 * @param sql
	 * @param page
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<Map<String, Object>> queryPageUseMybatis(UserDataSource userDs, String sql, Page page) {
		return queryUseMybatis(userDs, sql, page, true);
	}

	private static String findColumnInSql(String sql, String column) {
		int begin = sql.toLowerCase().indexOf("select");
		int end = sql.toLowerCase().indexOf("from");
		String col = null;
		if (begin > -1 && begin + 6 < end) {
			String colStr = sql.substring(begin + 6, end) + ",";
			begin = 0;
			end = begin + 1;
			int count = 0;
			while (end < colStr.length()) {
				char cc = colStr.charAt(end);
				if (cc == '(') {
					count++;
				} else if (cc == ')') {
					count--;
				} else if (colStr.charAt(end) == ',' && count == 0) {
					String cs = colStr.substring(begin, end);
					cs = cs.trim();
					String spaceStr = spacePt.matcher(cs).replaceAll("").toUpperCase();
					if (spaceStr.equals(column)) {
						// 说明找到了对应字段
						col = cs;
						break;
					}
					begin = end + 1;
					end = begin + 1;
				}
				end++;
			}
		}
		return col;
	}

	private static SqlSessionFactory getSessionFactory(UserDataSource userDs) {
		SqlSessionFactory sqlSessionFactory = getSessionFactory(userDs.getDsName());
		if (sqlSessionFactory == null) {
//			log.info("数据源没有获取到，将重新创建：{}", userDs);
			DruidDataSourceUtils druidDs = DruidDataSourceUtils.getInstance();
			DataSource ds = druidDs.getDataSource(userDs.getDsName());
			if (ds == null) {
				ds = druidDs.buildDataSource(userDs.getDsName(), userDs.getDsType(), userDs.getHost(), userDs.getPort(),
						userDs.getDbName(), userDs.getUserName(), userDs.getPassword(), userDs.getOraMode(), userDs.getAppendCode());
				Assert.notNull(ds, String.format("不能创建数据源:%s,请检查数据源相关参数!", userDs.getDsName()));
			}
			buildSqlSessionFactory(userDs.getDsName(), userDs.getDsType(), ds);
			sqlSessionFactory = getSessionFactory(userDs.getDsName());
		}
		log.info("会话工厂-》{}", sqlSessionFactory);
		return sqlSessionFactory;
	}

	/**
	 * @Title: queryFirstPageUseMybatis
	 * @Description: 指定数据源及sql语句进行mybatis查询第一页数据
	 * @Author:huangkui
	 * @Date: 2019年6月3日 上午10:53:35
	 * @param userDs
	 * @param sql
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<Map<String, Object>> queryFirstPageUseMybatis(UserDataSource userDs, String sql) {
		SqlSession session = getSessionFactory(userDs).openSession();
		Page rowBounds = new Page();
		rowBounds.setPageNo(1);
		List<?> list = session.selectList("cn.trasen.query.report.dao.UserDefineSqlMapper.query", sql, rowBounds);
		session.commit();
		session.close();
		List<Map<String, Object>> result = new ArrayList<>();
		if (list != null && list.size() > 0) {
			for (Object obj : list) {
				result.add((Map<String, Object>) obj);
			}
		}
		return result;
	}

	private static void buildSqlSessionFactory(String dsName, Integer dbType, DataSource ds) {
		synchronized (factoryMap) {
			if (!factoryMap.containsKey(dsName)) {
				TransactionFactory transactionFactory = new JdbcTransactionFactory();
				Environment environment = new Environment("development", transactionFactory, ds);
				Configuration configuration = new Configuration(environment);
				configuration.setCallSettersOnNulls(true);
				configuration.addMappers("cn.trasen.query.report.dao");
				// configuration.addMapper(UserDefineQueryMapper.class);
				Properties variables = new Properties();
				String dialect = null;
				if (Contants.DATA_SOURCE_TYPE_ORACLE.equals(dbType)) {
					dialect = "cn.trasen.core.feature.orm.dialect.OracleDialect";
				} else if (Contants.DATA_SOURCE_TYPE_SQLSERVER.equals(dbType)) {
					dialect = "cn.trasen.core.feature.orm.dialect.MsSqlDialect";
				} else if (Contants.DATA_SOURCE_TYPE_DB2.equals(dbType)) {
					dialect = "cn.trasen.core.feature.orm.dialect.DB2Dialect";
				} else if (Contants.DATA_SOURCE_TYPE_MYSQL.equals(dbType)) {
					dialect = "cn.trasen.core.feature.orm.dialect.MySqlDialect";
				}
				variables.setProperty("dialectClass", dialect);
//				variables.setProperty("callSettersOnNulls", Boolean.TRUE.toString());
				configuration.setVariables(variables);
				configuration.setCallSettersOnNulls(Boolean.TRUE);

				DataScopeInterceptor interceptor = new DataScopeInterceptor();
				SqlConditionHelper conditionHelper = new SqlConditionHelper(new ITableFieldConditionDecision() {
					@Override
					public  Map<String, String> adjudge(String tableName) {
						return PermissionUtil.adjudge(tableName);
					}
				});
				interceptor.setConditionHelper(conditionHelper);
				configuration.addInterceptor(interceptor);

				SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);
				log.info("数据源会话工厂创建成功：{}", dsName);
				factoryMap.put(dsName, sqlSessionFactory);
			}
		}
	}

	public static <T> String paraseParameter(HttpServletRequest request, String parameters, List<T> list,Function<T, String> nameFun) {
		Set<String> names = new HashSet<String>();
		Set<String> qnames = new HashSet<String>();
		if(list != null && list.size()>0) {
			qnames = list.stream().map(nameFun).collect(Collectors.toSet());
		}
		JSONArray array= new JSONArray();
		if(StringUtils.hasText(parameters)) {
			//这里也有可能是无效的参数
			JSONArray array1= JSONObject.parseArray(parameters);
			if(array1 != null && array1.size() > 0) {
				for(int i=0;i<array1.size();i++) {
					JSONObject obj = array1.getJSONObject(i);
					String name = obj.getString("name");
					if(qnames.contains(name)) {
						array.add(obj);
					}
				}
			}
		}
		Map<String, String[]> map = request.getParameterMap();
		if(map != null) {
			for(String name:map.keySet()) {
				if(names.contains(name)) {
					continue;
				}
				if("parameters,id,pageNo,pageSize,sidx,sord,nd,_search".indexOf(name) != -1) {
					continue;
				}
				if(!qnames.contains(name)) {
					continue;
				}
				String[] vss = map.get(name);
				List<String> values = new ArrayList<String>();
				for(String vv:vss) {
					if(vv.length()>2 && vv.charAt(0)=='$' && vv.charAt(vv.length()-1)=='$') {
						continue;
					}
					values.add(vv);
				}
				if(values.size()==0) {
					continue;
				}
				if(values.size() == 1) {
					String value = values.get(0);
					if(value.length()>2 && value.charAt(0)=='$' && value.charAt(value.length()-1)=='$') {
						continue;
					}
					JSONObject obj = new JSONObject();
					obj.put("name", name);
					obj.put("value", value);
					array.add(obj);
				}else {
					JSONObject obj = new JSONObject();
					obj.put("name", name);
					JSONArray va = new JSONArray();
					for(String vv:values) {
						va.add(vv);
					}
					obj.put("list", va);
					array.add(obj);
				}
			}
		}
		return array.toJSONString();
	}

	public static void main(String[] args) throws SQLException {
		setMaxSize(1);
		UserDataSource userDs = new UserDataSource();
		userDs.setDbName("TSHIP");
		userDs.setDsName("testss");
		userDs.setDsType(1);
		userDs.setHost("**************");
		userDs.setPassword("Trasen_123");
		userDs.setPort(1521);
		userDs.setUserName("HIP");
		
	 List<?> list = querySql("select * from DATA_DICTS", userDs);
	 System.out.println(list.size());
	}


}
