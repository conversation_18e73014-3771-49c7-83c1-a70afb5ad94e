package cn.trasen.homs.SQLToAPI.vo;

import java.util.List;

import cn.trasen.homs.core.model.DataSet;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 ********************************************** 
 * @Description: 包含错误信息的分页结果集对象
 * @Author:huangkui
 * @Date: 2019年6月13日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Getter
@Setter
public class DataSetVo<T> extends DataSet<T> {
	
	@ApiModelProperty("是否成功")
	private boolean success = true;
	@ApiModelProperty("错误提示消息")
	private String errMsg;
	@ApiModelProperty("自定义查询的ID")
	private String id;
	
	public DataSetVo() {
		super();
	}

	public DataSetVo(Integer pageNo, Integer pageSize, Integer pageCount, Integer totalCount, List<T> rows) {
		super(pageNo, pageSize, pageCount, totalCount, rows);
	}

	public static <T> DataSetVo<T> fail(String errMsg) {
		DataSetVo<T> vo = new DataSetVo<>();
		vo.errMsg = errMsg;
		vo.success = false;
		return vo;
	}

}
