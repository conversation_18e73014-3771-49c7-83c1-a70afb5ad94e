package cn.trasen.homs.SQLToAPI.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnsVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineProcFunVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

/**
 ********************************************** 
 * @Description: 用户自定义查询-数据接口
 * @Author:huangkui
 * @Date: 2019年5月28日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
public interface UserDefineServiceMapper extends Mapper<UserDefineService> {
	List<UserDefineQueryVo> listQuery(Page page, @Param("condition") String condition);
	List<UserDefineProcFunVo> listProcFun(Page page, @Param("condition") String condition);
	List<UserDefineQueryVo> listAll(Page page, @Param("categoryCode") String categoryCode,@Param("topicCateCode") String topicCateCode,
			@Param("condition") String condition,@Param("systemService") String systemService,@Param("queryColumn") String queryColumn);

	/**
	 * 获取查询表的所有字段
	 * @param tableName
	 * @return
	 */
	List<TableColumnsVo> getTableColumns(@Param("tableName") String tableName);
}