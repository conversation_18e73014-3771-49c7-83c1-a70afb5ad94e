package cn.trasen.homs.SQLToAPI.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName DateUtils
 * @Description DateUtils
 * @date 2019年1月17日 上午10:09:11
 * <AUTHOR>
 */
public class DateUtils {

	public static void main(String[] a) throws ParseException {
		Date date = new Date();
		System.out.println(dateBegin(date));
		System.out.println(dateEnd(date));
		System.out.println(weekBegin(date));
		System.out.println(weekEnd(date));
		System.out.println(monthBegin(date));
		System.out.println(monthEnd(date));
		System.out.println(quarterBegin(date));
		System.out.println(quarterEnd(date));
		System.out.println(yearBegin(date));
		System.out.println(yearEnd(date));

		System.out.println(monthAdd(date, 10));
		System.out.println(monthAdd(date, 11));
		System.out.println(monthAdd(date, 22));
		System.out.println("##############");
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		date = monthEnd(monthAdd(date, 1));
		System.out.println(format.format(date));
		System.out.println(format.format(monthAdd(date, 1)));
		System.out.println(format.format(monthAdd(date, 11)));
		System.out.println(format.format(monthAdd(date, 23)));
		System.out.println(format.format(monthAdd(date, 35)));
		System.out.println(format.format(monthAdd(date, 47)));
		System.out.println("##############");
		System.out.println(format.format(monthAdd(date, -1)));
		System.out.println(format.format(monthAdd(date, -13)));
		System.out.println(format.format(monthAdd(date, -2)));
		System.out.println(format.format(monthAdd(date, -26)));
		System.out.println(format.format(monthAdd(date, -3)));
		System.out.println(format.format(monthAdd(date, -39)));
		System.out.println("##############");
		date = new Date();
		System.out.println(format.format(dateAdd(date, 366)));
		System.out.println(format.format(dateAdd(date, -365)));
		System.out.println("##############");
		date = yearEnd(date);
		System.out.println(format.format(dateAdd(date, 366)));
		System.out.println(format.format(dateAdd(date, 365 * 2)));
		System.out.println(format.format(dateAdd(date, 365 * 3)));
		System.out.println(format.format(dateAdd(date, 365 * 4)));
		System.out.println(format.format(dateAdd(date, -366)));
		System.out.println(format.format(dateAdd(date, -365 * 2)));
		System.out.println(format.format(dateAdd(date, -365 * 3)));
		System.out.println(format.format(dateAdd(date, -365 * 4)));
		System.out.println(format.format(dateAdd(date, -366 * 100 + 75)));
		System.out.println(format.format(dateAdd(date, 366 * 100 - 76)));

		System.out.println("###############");
		date = new Date();
		System.out.println(format.format(dateAdd(date, -51)));

		System.out.println("#####compareMonths");
		System.out.println(compareMonths(format.parse("2019-03-01 12:12:12"), format.parse("2019-04-01 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-03-01 12:12:12"), format.parse("2019-02-02 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-01-31 12:12:12"), format.parse("2019-02-28 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-02-28 12:12:12"), format.parse("2019-01-31 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-01-31 12:12:12"), format.parse("2019-04-30 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-04-30 12:12:12"), format.parse("2019-01-31 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-02-28 12:12:12"), format.parse("2017-01-31 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-02-28 12:12:12"), format.parse("2017-03-31 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-02-27 12:12:12"), format.parse("2017-01-31 12:12:12")));
		System.out.println(compareMonths(format.parse("2019-02-27 12:12:12"), format.parse("2017-03-31 12:12:12")));

		System.out.println("#####compareDays");
		System.out.println(compareDays(format.parse("2019-03-04 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareDays(format.parse("2019-04-05 12:12:12"), format.parse("2019-03-04 12:12:12")));
		System.out.println(compareDays(format.parse("2018-03-06 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareDays(format.parse("2019-04-05 12:12:12"), format.parse("2018-03-06 12:12:12")));
		System.out.println(compareDays(format.parse("2018-05-04 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareDays(format.parse("2019-04-05 12:12:12"), format.parse("2018-05-04 12:12:12")));
		System.out.println(compareDays(format.parse("2018-05-06 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareDays(format.parse("2019-04-05 12:12:12"), format.parse("2018-05-06 12:12:12")));

		System.out.println("#####compareYears");
		System.out.println(compareYears(format.parse("2019-03-04 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareYears(format.parse("2019-04-05 12:12:12"), format.parse("2019-03-04 12:12:12")));
		System.out.println(compareYears(format.parse("2017-03-06 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareYears(format.parse("2019-04-05 12:12:12"), format.parse("2017-03-06 12:12:12")));
		System.out.println(compareYears(format.parse("2013-05-04 12:12:12"), format.parse("2019-04-05 12:12:12")));
		System.out.println(compareYears(format.parse("2019-04-05 12:12:12"), format.parse("2013-05-04 12:12:12")));
		System.out.println(compareYears(format.parse("2012-05-06 12:12:12"), format.parse("2019-05-05 12:12:12")));
		System.out.println(compareYears(format.parse("2019-05-05 12:12:12"), format.parse("2012-05-06 12:12:12")));

	}

	public static Date monthAdd(Date date, int i) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int month = calendar.get(Calendar.MONTH);
		month += i;
		int year = 0;
		if (month < 0) {
			year = (0 - month) / 12;
			if ((0 - month) % 12 > 0) {
				year++;
				month = 12 + month % 12;
			} else {
				month = 0;
			}
			year = 0 - year;

		} else {
			year = month / 12;
			month = month % 12;
		}
		int monthDay = calendar.get(Calendar.DAY_OF_MONTH);
		if (monthDay > 28) {
			// 日期如果为28号以后暂时设置为1号
			calendar.set(Calendar.DAY_OF_MONTH, 1);
		}
		calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + year);
		calendar.set(Calendar.MONTH, month);
		if (monthDay > 28) {
			// 日期如果为28号以后
			Date monthEnd = monthEnd(calendar.getTime());
			Calendar c = Calendar.getInstance();
			c.setTime(monthEnd);
			// 得到当月最大天数
			int maxMonthDay = c.get(Calendar.DAY_OF_MONTH);
			// 如果日期大于月份最大天数则设置为最大天数
			calendar.set(Calendar.DAY_OF_MONTH, monthDay > maxMonthDay ? maxMonthDay : monthDay);
		}
		return calendar.getTime();
	}

	public static Date yearAdd(Date date, int i) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + i);
		return calendar.getTime();
	}

	public static Date dateAdd(Date date, int i) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int day = calendar.get(Calendar.DAY_OF_YEAR);
		day += i;
		boolean flag = day < 0;
		int yadd = 1;
		if (flag) {
			day = 0 - day;
			yadd = -1;
			calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
		}
		do {
			int year = calendar.get(Calendar.YEAR);
			int yearDay = 365;
			if (day >= 365) {
				int y = year;
				if (flag) {
					// 减天数时计算上一年的天数
					// 加天数时计算本年的天数
					y += yadd;
				}
				if (y % 4 == 0) {
					if (y % 100 == 0) {
						if (y % 400 == 0) {
							yearDay = 366;
						}
					} else {
						yearDay = 366;
					}
				}
			}
			if (day >= yearDay) {
				// 如果日期大于一个年度天数，加一年或减一年，日期减掉年度天数，循环继续
				calendar.set(Calendar.YEAR, year + yadd);
				day -= yearDay;
			} else {
				// 如果日期没有大于一个年度天数，循环结束
				if (flag) {
					// 如果是减日期，需要倒置天数进行设置
					day = yearDay - day;
				}
				calendar.set(Calendar.DAY_OF_YEAR, day);
				break;
			}
		} while (true);
		return calendar.getTime();
	}
	
	/**
	* @Title: weekBegin
	* @Description: 返回当前日期的周一
	* @Author:huangkui
	* @Date: 2019年7月31日 下午4:10:22
	* @param date
	* @return
	 */
	public static Date weekBegin(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		int week = calendar.get(Calendar.DAY_OF_WEEK);
		week = (week+12)%7;
		return new Date(calendar.getTimeInMillis() - week*3600*24*1000L);
	}
	
	/**
	* @Title: weekEnd
	* @Description: 返回当前日期的周日
	* @Author:huangkui
	* @Date: 2019年7月31日 下午4:13:33
	* @param date
	* @return
	 */
	public static Date weekEnd(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		int week = calendar.get(Calendar.DAY_OF_WEEK);
		week = (week+12)%7;
		return new Date(calendar.getTimeInMillis() + (6-week)*3600*24*1000L);
	}

	public static Date quarterBegin(Date date) {
		int querter = getQuarter(date);
		int month = (querter - 1)*3;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, month);
		return monthBegin(calendar.getTime());
	}
	
	public static Date quarterEnd(Date date) {
		int querter = getQuarter(date);
		int month = querter*3-1;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, month);
		return monthEnd(calendar.getTime());
	}
	
	public static Date dateEnd(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime();
	}

	public static Date dateBegin(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static Date yearEnd(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, Calendar.DECEMBER);
		calendar.set(Calendar.DAY_OF_MONTH, 31);
		return dateEnd(calendar.getTime());
	}

	public static Date yearBegin(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, Calendar.JANUARY);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return dateBegin(calendar.getTime());
	}

	public static Date monthEnd(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int endDate = 0;
		int month = calendar.get(Calendar.MONTH);
		switch (month) {
		case Calendar.APRIL:
		case Calendar.JUNE:
		case Calendar.SEPTEMBER:
		case Calendar.NOVEMBER:
			endDate = 30;
			break;
		case Calendar.FEBRUARY:
			int year = calendar.get(Calendar.YEAR);
			endDate = year % 100 == 0 ? (year % 400 == 0 ? 29 : 28) : (year % 4 == 0 ? 29 : 28);
			break;
		default:
			endDate = 31;
			break;
		}
		calendar.set(Calendar.DAY_OF_MONTH, endDate);
		return dateEnd(calendar.getTime());
	}

	public static Date monthBegin(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return dateBegin(calendar.getTime());
	}

	/**
	 * 
	 * @Title isLastDayOfYear
	 * @Description 是否每年最后一天
	 * @param date
	 * @return boolean
	 * @date 2019年1月17日 上午10:08:27
	 * <AUTHOR>
	 */
	public static boolean isLastDayOfYear(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
		if (calendar.get(Calendar.DAY_OF_YEAR) == 1) {
			return true;
		}
		return false;
	}

	/**
	 * 
	 * @Title isLastDayOfMonth
	 * @Description 是否每月最後一天
	 * @param date
	 * @return boolean
	 * @date 2019年1月17日 上午10:08:16
	 * <AUTHOR>
	 */
	public static boolean isLastDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
		if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
			return true;
		}
		return false;
	}

	public static int compareYears(Date start, Date end) {
		Calendar calendar1 = Calendar.getInstance();
		Calendar calendar2 = Calendar.getInstance();
		boolean flag = setStartEnd(calendar1, calendar2, start, end);
		int y1 = calendar1.get(Calendar.YEAR);
		int y2 = calendar2.get(Calendar.YEAR);
		int d1 = calendar1.get(Calendar.DAY_OF_MONTH);
		int d2 = calendar2.get(Calendar.DAY_OF_MONTH);
		int m1 = calendar1.get(Calendar.MONTH);
		int m2 = calendar2.get(Calendar.MONTH);
		int year = y2 - y1;
		if (m1 > m2 || m1 == m2 && d1 > d2) {
			// 只精确到天
			year--;
		}
		if (flag) {
			year = 0 - year;
		}
		return year;
	}

	public static int compareMonths(Date start, Date end) {
		Calendar calendar1 = Calendar.getInstance();
		Calendar calendar2 = Calendar.getInstance();
		boolean flag = setStartEnd(calendar1, calendar2, start, end);
		int d1 = calendar1.get(Calendar.DAY_OF_MONTH);
		int d2 = calendar2.get(Calendar.DAY_OF_MONTH);
		int m1 = calendar1.get(Calendar.MONTH);
		int m2 = calendar2.get(Calendar.MONTH);
		int y1 = calendar1.get(Calendar.YEAR);
		int y2 = calendar2.get(Calendar.YEAR);
		int month = (y2 - y1) * 12;
		month += m2 - m1;
		if (d2 < d1) {
			Date d2ME = monthEnd(calendar2.getTime());
			Calendar c = Calendar.getInstance();
			c.setTime(d2ME);
			if (c.get(Calendar.DAY_OF_MONTH) > d2) {
				// 如果d2为当月最后一天，则不管d2是否小于d1
				// 例如：start-2019-01-31 end-2019-02-28，虽然28小于31但是是2月最后一天就不减1了
				month--;
			}
		}
		return flag ? 0 - month : month;
	}

	private static boolean setStartEnd(Calendar c1, Calendar c2, Date start, Date end) {
		if (start.before(end)) {
			c1.setTime(start);
			c2.setTime(end);
			return false;
		} else {
			c1.setTime(end);
			c2.setTime(start);
			return true;
		}
	}

	public static int compareDays(Date start, Date end) {
		Calendar calendar1 = Calendar.getInstance();
		Calendar calendar2 = Calendar.getInstance();
		boolean flag = setStartEnd(calendar1, calendar2, start, end);
		int d1 = calendar1.get(Calendar.DAY_OF_YEAR);
		int d2 = calendar2.get(Calendar.DAY_OF_YEAR);
		int y1 = calendar1.get(Calendar.YEAR);
		int y2 = calendar2.get(Calendar.YEAR);
		int days = 0;
		if (y1 == y2) {
			days = d2 - d1;
		} else {
			int dayCount = 0;
			for (int i = y1; i < y2; i++) {
				if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
					dayCount += 366;
				} else {
					dayCount += 365;
				}
			}
			days = dayCount + (d2 - d1);
		}
		if (flag) {
			days = 0 - days;
		}
		return days;
	}

	public static int getMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.MONTH) + 1;
	}

	public static int getQuarter(Date date) {
		int m = getMonth(date);
		int q = m / 3;
		if (m % 3 > 0) {
			q++;
		}
		return q;
	}

	public static int getYear(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.YEAR);
	}

	public static Date stringToDate(String value) {
		String format = "yyyy-MM-dd HH:mm:ss.SSS";
		String v = value;
		if(v.length()>format.length()) {
			v = value.substring(0, format.length());
		}else {
			format = format.substring(0, v.length());
		}
		try {
			SimpleDateFormat df = new SimpleDateFormat(format);
			return df.parse(v);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	

}
