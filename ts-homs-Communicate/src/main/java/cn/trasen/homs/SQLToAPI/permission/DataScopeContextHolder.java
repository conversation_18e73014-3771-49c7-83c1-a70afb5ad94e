package cn.trasen.homs.SQLToAPI.permission;

import cn.trasen.homs.SQLToAPI.permission.model.DataScopeModel;

/**
 * @ClassName DataScopeContextHolder
 * @Description 数据权限
 * @date 2020年4月26日 下午8:02:46
 * <AUTHOR>
 */
public class DataScopeContextHolder {

	private static final ThreadLocal<DataScopeModel> LOCAL = new ThreadLocal<>();

	public static void set(DataScopeModel dataScopeModel) {
		LOCAL.set(dataScopeModel);
	}

	public static DataScopeModel get() {
		return LOCAL.get();
	}

	public static void remove() {
		LOCAL.remove();
	}
}
