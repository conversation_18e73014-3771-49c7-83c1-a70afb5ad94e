package cn.trasen.homs.SQLToAPI.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "COMM_DATASOURCE")
@Setter
@Getter
public class CommDatasource implements Serializable {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "ID")
	private String id;

	/**
	 * 系统ID
	 */
	@Column(name = "SYS_ID")
	@ApiModelProperty(value = "系统ID", required = true)
	private String sysId;

	/**
	 * 系统代码
	 */
	@Column(name = "SYS_CODE")
	@ApiModelProperty(value = "系统代码", required = true)
	private String sysCode;

	/**
	 * 系统名称
	 */
	@Column(name = "SYS_NAME")
	@ApiModelProperty(value = "系统名称", required = true)
	private String sysName;

	/**
	 * 微服务名称
	 */
	@Column(name = "SERVICE_NAME")
	@ApiModelProperty(value = "微服务名称")
	private String serviceName;

	/**
	 * 数据源名称
	 */
	@Column(name = "DS_NAME")
	@ApiModelProperty(value = "数据源名称", required = true)
	private String dsName;

	/**
	 * 数据源类型：（1-ORACLE，2-SQLSERVER，3-DB2，4-MYSQL）
	 */
	@Column(name = "DB_TYPE")
	@ApiModelProperty(value = "数据源类型：（1-ORACLE，2-SQLSERVER，3-DB2，4-MYSQL， 5-REDIS， 6-KAFKA， 7-CLICKHOUSE，8-DORIS，9-POSTGRE_SQL）", required = true)
	private Integer dbType;

	/**
	 * 数据库服务器地址
	 */
//	@Column(name = "HOST")
//	@ApiModelProperty(value = "数据库服务器地址", required = true)
//	private String host;

	/**
	 * 数据库服务器端口
	 */
//	@Column(name = "PORT")
//	@ApiModelProperty(value = "数据库服务器端口", required = true)
//	private Integer port;

	/**
	 * 数据库名称
	 */
//	@Column(name = "DB_NAME")
//	@ApiModelProperty(value = "数据库名称", required = true)
//	private String dbName;

	/**
	 * 数据库用户名
	 */
//	@Column(name = "USER_NAME")
//	@ApiModelProperty(value = "数据库用户名", required = true)
//	private String userName;

	/**
	 * 数据库密码
	 */
//	@Column(name = "PASSWORD")
//	@ApiModelProperty(value = "数据库密码", required = true)
//	private String password;

	/**
	 * 附加代码
	 */
	@Column(name = "APPEND_CODE")
	@ApiModelProperty(value = "附加代码")
	private String appendCode;

	/**
	 * orace模式：0、正常模式，1、pdb模式
	 */
	@Column(name = "ORA_MODE")
	@ApiModelProperty(value = "orace模式：0、正常模式，1、pdb模式")
	private Integer oraMode;

	/**
	 * 备注
	 */
	@Column(name = "REMARK")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 创建人
	 */
	@Column(name = "CREATE_USER")
	@ApiModelProperty(value = "创建人")
	private String createUser;

	/**
	 * 创建时间
	 */
	@Column(name = "CREATE_DATE")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private Date createDate;

	/**
	 * 修改人
	 */
	@Column(name = "UPDATE_USER")
	@ApiModelProperty(value = "修改人")
	private String updateUser;

	/**
	 * 修改时间
	 */
	@Column(name = "UPDATE_DATE")
	@ApiModelProperty(value = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private Date updateDate;

	/**
	 * 创建人姓名
	 */
	@Column(name = "CREATE_USER_NAME")
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;

	/**
	 * 修改人姓名
	 */
	@Column(name = "UPDATE_USER_NAME")
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;

	/**
	 * 删除标识
	 */
	@Column(name = "IS_DELETED")
	@ApiModelProperty(value = "删除标识")
	private String isDeleted;
	
	/**********************新增属性**********************/

    /**
     * 状态：1启用，0停用
     */
    @NotBlank(message = "状态不能为空")
    @Column(name = "STATUS")
    @ApiModelProperty(value = "状态：1启用，0停用")
    private Integer status;

    /**
     * redis连接名称
     */
    @Column(name = "CONN_NAME")
    @ApiModelProperty(value = "redis连接名称")
    private String connName;

    /**
     * 分隔符
     * 带引号为mysql字段，打mysql版本包时，需要带引号的，oracle不需要带引号的
     */
//    @Column(name = "`SEPARATOR`")
////    @Column(name = "SEPARATOR")
//    @ApiModelProperty(value = "分隔符")
//    private String separator;

    /**
     * 连接类型：1:SSH、2:SSL、3:Sentinel、4:Cluster
     */
    @Column(name = "CONN_TYPE")
    @ApiModelProperty(value = "连接类型：1:SSH、2:SSL、3:Sentinel、4:Cluster")
    private Integer connType;

    /**
     * 数据库地址
     */
    @Column(name = "DB_ADDRESS")
    @ApiModelProperty(value = "数据库地址")
    private String dbAddress;

    /**
     * 数据库端口号
     */
    @Column(name = "DB_PORT")
    @ApiModelProperty(value = "数据库端口号")
    private Integer dbPort;

    /**
     * 数据库账号
     */
    @Column(name = "DB_ACCOUNT")
    @ApiModelProperty(value = "数据库账号")
    private String dbAccount;

    /**
     * 数据库密码
     */
    @Column(name = "DB_PASSWORD")
    @ApiModelProperty(value = "数据库密码")
    private String dbPassword;

    /**
     * 私钥
     */
    @Column(name = "PRIVATE_KEY")
    @ApiModelProperty(value = "私钥")
    private String privateKey;

    /**
     * 私钥密码
     */
    @Column(name = "PRIVATE_PASSWORD")
    @ApiModelProperty(value = "私钥密码")
    private String privatePassword;

    /**
     * 超时
     */
    @Column(name = "TIMEOUT")
    @ApiModelProperty(value = "超时")
    private Integer timeout;

    /**
     * 公钥
     */
    @Column(name = "PUBLIC_KEY")
    @ApiModelProperty(value = "公钥")
    private String publicKey;

    /**
     * 授权
     */
    @Column(name = "AUTHORIZATION_")
    @ApiModelProperty(value = "授权")
    private String authorization;

    /**
     * Master组名称
     */
    @Column(name = "MASTER_NAME")
    @ApiModelProperty(value = "Master组名称")
    private String masterName;

    /**
     * 集群名称
     */
    @Column(name = "CLUSTER_NAME")
    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    /**
     * 版本号
     */
    @Column(name = "VERSION_NUMBER")
    @ApiModelProperty(value = "版本号")
    private Integer versionNumber;

    /**
     * zookeeper地址
     */
    @Column(name = "ZK_ADDRESS")
    @ApiModelProperty(value = "zookeeper地址")
    private String zkAddress;

    /**
     * chroot路径
     */
    @Column(name = "CHROOT_PATH")
    @ApiModelProperty(value = "chroot路径")
    private String chrootPath;

    /**
     * JDBC地址
     */
    @Column(name = "JDBC_URL")
    @ApiModelProperty(value = "JDBC地址")
    private String jdbcUrl;

    /**
     * 数据库类型：1关系型数据库,2非关系型数据库
     */
    @Column(name = "REL_TYPE")
    @ApiModelProperty(value = "数据库类型：1关系型数据库,2非关系型数据库")
    private Integer relType;

    /**
     * 数据源公共配置
     */
    @Column(name = "PUBLIC_CONFIG")
    @ApiModelProperty(value = "数据源公共配置")
    private String publicConfig;
    /**********************end**********************/
    
    @Transient
    @ApiModelProperty(value = "密码是否加密：0不需要，1需要")
    private Integer pwdEncrypt;
	
	@Transient
	private String pwd;
}