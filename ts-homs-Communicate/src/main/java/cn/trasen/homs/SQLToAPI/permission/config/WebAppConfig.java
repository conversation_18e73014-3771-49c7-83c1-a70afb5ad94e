package cn.trasen.homs.SQLToAPI.permission.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import cn.trasen.homs.SQLToAPI.permission.OrgMapComponent;
import cn.trasen.homs.SQLToAPI.permission.StaffMapComponent;
import cn.trasen.homs.SQLToAPI.permission.interceptor.TokenInterceptor;

/***
 *
 * @ClassName WebAppConfig
 * @Description WebAppConfig
 * @date 2020年4月27日 下午4:56:08
 * <AUTHOR>
 */
//@Configuration
public class WebAppConfig implements WebMvcConfigurer {

    @Value("${sso.verifyUrl}")
    private String verifyUrl;

    // 是否开启人员ID当做ysdm
    @Value("${thps.permission.userIdEnabled:false}")
    private Boolean userIdEnabled = Boolean.FALSE;
    
    @Autowired
    private OrgMapComponent orgMapComponent;

    @Autowired
    private StaffMapComponent staffMapComponent;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        TokenInterceptor tokenInterceptor = new TokenInterceptor();
        tokenInterceptor.setVerifyUrl(verifyUrl);
        tokenInterceptor.setUserIdEnabled(userIdEnabled);
        tokenInterceptor.setOrgMapComponent(orgMapComponent);
        tokenInterceptor.setStaffMapComponent(staffMapComponent);
        registry.addInterceptor(tokenInterceptor).addPathPatterns("/**");
    }

}