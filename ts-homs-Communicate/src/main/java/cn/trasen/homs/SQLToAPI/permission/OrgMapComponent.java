package cn.trasen.homs.SQLToAPI.permission;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import cn.trasen.homs.SQLToAPI.permission.model.MapVo;
import cn.trasen.homs.SQLToAPI.permission.model.OrgMapProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName OrgMapComponent
 * @Description OrgMapComponent
 * @date 2020年4月29日 上午11:26:48
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrgMapComponent implements CommandLineRunner {

	@Autowired
	private OrgMapProperties orgMapProperties;

	/**
	 * @Title initOrgMapData
	 * @Description 初始化加载映射表
	 * @date 2020年4月29日 下午12:15:34
	 * <AUTHOR>
	 */
	public void initOrgMapData() {
		log.debug("org map enabled :{}", orgMapProperties.isEnabled());
		log.debug("org map laod url:{}", orgMapProperties.getUrl());
		if (orgMapProperties.isEnabled() && StringUtils.isNotBlank(orgMapProperties.getUrl())) {
			List<MapVo> orgMapList = loadOrgMapData(orgMapProperties.getUrl());
			if (orgMapList != null && orgMapList.size() > 0) {
				orgMapList.stream().forEach(item -> {
					OrgMapHolder.getInstance().set(item.getSourceCode(), item.getTargetCode());
				});
			}
		}
	}

	private List<MapVo> loadOrgMapData(String url) {
		// 获取用户信息
		InputStream is = null;
		BufferedReader bf = null;
		String body = "";
		try {
			CloseableHttpClient client = HttpClients.custom().build();
			HttpGet httpGet = new HttpGet(url);
			is = client.execute(httpGet).getEntity().getContent();
			bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = bf.readLine()) != null) {
				buffer.append(line);
			}
			body = buffer.toString();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
				}
			}
			if (bf != null) {
				try {
					bf.close();
				} catch (IOException e) {
				}
			}
		}
		log.debug("loadOrgMapData===body:{}", body);
		R<MapVo> orgMapList = JSONObject.parseObject(body, new TypeReference<R<MapVo>>() {
		});

		if (orgMapList != null) {
			return orgMapList.getRows();
		}
		return null;
	}

	public OrgMapProperties getOrgMapProperties() {
		return orgMapProperties;
	}

	public void setOrgMapProperties(OrgMapProperties orgMapProperties) {
		this.orgMapProperties = orgMapProperties;
	}

	@Override
	public void run(String... args) throws Exception {
		this.initOrgMapData();
	}
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class R<T> {
	private List<T> rows;
}
