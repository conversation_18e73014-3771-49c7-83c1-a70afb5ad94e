package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
**********************************************   
* @Description: 自定义查询的查询参数-视图对象
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
public class QueryParamVo {
	
	@ApiModelProperty("参数名")
	private String name;
	
	@ApiModelProperty("参数值")
	private String value;
	
	@ApiModelProperty("参数值列表")
	private List<String> list;
}
