package cn.trasen.homs.SQLToAPI.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.SQLToAPI.vo.DataSourceTestVo;
import cn.trasen.homs.bean.sso.UserDataSource;

/**
**********************************************   
* @Description: 用户自定义数据源-服务定义
* @Author:huangkui  
* @Date: 2019年5月28日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public interface UserDataSourceService {

	/**
	* @Title: selectDecrypt
	* @Description: 获取数据源，并且解密密码
	* @Author:huangkui
	* @Date: 2019年6月10日 下午6:00:33
	* @param id
	* @return
	 */
	UserDataSource selectDecrypt(String id);

	/**
	* @Title: testConnection
	* @Description: 测试连接
	* @Author:huangkui
	* @Date: 2019年5月28日 上午9:56:11
	* @param id
	* @return
	 */
	List<Object> testConnection(DataSourceTestVo testVo);

	/**
	 * 数据源发生了改变
	 * @param dsName
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2020年1月20日 上午11:54:36
	 */
	Boolean change(String id,String dsName);

	/**
	 * 获取数据源id、name
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2020年2月7日 下午9:18:14
	 */
	Map<String, String> names();
}
