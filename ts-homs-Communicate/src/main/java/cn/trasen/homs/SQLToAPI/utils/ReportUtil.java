package cn.trasen.homs.SQLToAPI.utils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import org.springframework.util.CollectionUtils;

import cn.trasen.homs.SQLToAPI.QueryConstants;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.TableTitleVo;
import lombok.Data;

/**
 ********************************************** 
 * @Description: 报表工具类
 * @Author:huangkui
 * @Date: 2019年5月10日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
public class ReportUtil {
	private static SimpleDateFormat MONTH_DF = new SimpleDateFormat("yyyy-MM");	
	
	/**
	* @Title: transTdNodeTree
	* @Description: 根据字段定义转换TD节点
	* @Author:huangkui
	* @Date: 2019年7月30日 下午4:35:04
	* @param cols
	* @return
	 */
	public static <T extends TableTitleVo<T>> List<TdNode> transTdNodeTree(List<T> cols) {
		List<TdNode> nodes = new ArrayList<>();
		for (TableTitleVo<T> col : cols) {
			TdNode node = new TdNode();
			node.setName(col.getTitle());
			node.setField(col.getName());
			node.setVirtual(col.isVirtual());
			if (col.getChildren() != null && col.getChildren().size() > 0) {
				node.setChildren(transTdNodeTree(col.getChildren()));
			}
			nodes.add(node);
		}
		return nodes;
	}

	/**
	 * @Title: treeToHtmlTableHead
	 * @Description: 根据表头设置及数据生成对应html表格代码
	 * @Author:huangkui
	 * @Date: 2019年6月17日 上午9:14:41
	 * @param tdTree
	 *            表头设置列表
	 * @param dataList
	 *            数据列表
	 * @return
	 */
	public static String treeToHtmlTableHead(List<TdNode> tdTree, List<Map<String, Object>> dataList) {
		Map<Integer, String> fieldIdxMap = new HashMap<>();
		StringBuffer table = new StringBuffer("  <table>");
		appendTr(table, tdTree, calListSize(tdTree), fieldIdxMap, 0);

		if (dataList != null) {
			for (Map<String, Object> row : dataList) {
				if (row == null) {
					continue;
				}
				table.append("<tr>");
				for (int i = 0; i < fieldIdxMap.size(); i++) {
					String field = fieldIdxMap.get(new Integer(i));
					if (field != null) {
						Object obj = row.get(field + "_TITLE");
						if (obj == null) {
							obj = row.get(field);
						}
						if (obj == null) {
							obj = "";
						}
						table.append("<td>").append(obj).append("</td>\r\n");
					}
				}
				table.append("</tr>\r\n");
			}
		}
		table.append("</table>");
		return table.toString();
	}

	@Data
	public static class TdNode {
		private String name;
		private String field;
		private Boolean virtual;
		private List<TdNode> children;
		private int idx;
	}

	private static void appendTr(StringBuffer table, List<TdNode> list, int rowspan, Map<Integer, String> fieldIdxMap,int idx) {
		table.append("<tr>");
		List<TdNode> children = new ArrayList<TdNode>();
		int thisIdx = idx;
		for (int i=0;i<list.size();i++) {
			TdNode node = list.get(i);
			if (!CollectionUtils.isEmpty(node.children)) {
				int colspan = calColspan(node.children);
				table.append("<td colspan='").append(colspan).append("'>").append(node.name).append("</td>\r\n");
				node.children.get(0).idx = thisIdx;
				children.addAll(node.children);
				thisIdx += colspan;
			} else {
				table.append("<td rowspan='").append(rowspan).append("'>").append(node.name).append("</td>\r\n");
				if(node.getIdx() == 0) {
					//不是父节点设置的第一个
					fieldIdxMap.put(thisIdx++, node.getField());
				}else {
					//父节点设置的第一个
					thisIdx = node.getIdx();
					fieldIdxMap.put(thisIdx++,node.getField());
				}
			}
		}
		table.append("</tr>\r\n");
		if (children.size() > 0) {
			appendTr(table, children, rowspan - 1, fieldIdxMap, children.get(0).idx);
		}
	}

	private static int calColspan(List<TdNode> nodes) {
		int colspan = 0;
		for (TdNode node : nodes) {
			if (node.getChildren() != null && node.getChildren().size() > 0) {
				colspan += calColspan(node.getChildren());
			} else if (node.getVirtual() == null || !node.getVirtual().booleanValue()) {
				colspan += 1;
			}
		}
		return colspan;
	}

	private static int calListSize(List<TdNode> children) {
		int max = 1;
		for (TdNode node : children) {
			if (node.getChildren() != null) {
				int c = 1 + calListSize(node.getChildren());
				if (c > max) {
					max = c;
				}
			}
		}
		return max;
	}
	
	/**
	* @Title: checkType
	* @Description: 检查字段类型
	* @Author:huangkui
	* @Date: 2019年7月25日 上午10:08:17
	* @param columnType
	* @param value
	* @return
	 */
	public static boolean checkDefaultValue(ColumnType columnType, String value, DateDefaultCfg dateCfg) {
		switch (columnType) {
		case STRING:
			//文字允许值为空字符串，但是不允许出现单引号
			return value != null && value.indexOf('\'') == -1;
		case NUMBER:
			return QueryConstants.NUMBER_PT.matcher(value).find();
		case DATE:
		case DATEUNIT:
			//日期类型允许使用配置code串
			String[] vs = QueryConstants.dateSplitPt.split(value);
			int count = 0;
			for(int i = 0; i<vs.length; i++) {
				if(dateCfg.getByCode(vs[i])!=null) {
					count++;
				}
			}
			if(count == vs.length) {
				return true;
			}
			String format = "yyyy-MM-dd HH:mm:ss";
			int len = value.length();
			if (len == 19 || len == 10 || len == 7 || len == 16 || len == 13 || len == 4) {
				// 只支持 yyyy-MM-dd HH:mm:ss、yyyy-MM-dd、yyyy-MM、yyyy-MM-dd HH:mm、yyyy-MM-dd HH、yyyy六种格式
				format = format.substring(0, len);
				try {
					Date date = new SimpleDateFormat(format).parse(value);
					return date != null;
				} catch (ParseException e) {
					e.printStackTrace();
					return false;
				}
			}
			break;
		/*case DATEUNIT:
			//月份类型允许使用配置code串
			vs = QueryConstants.dateSplitPt.split(value);
			count = 0;
			for(int i = 0; i<vs.length; i++) {
				if(dateCfg.getByCode(vs[i])!=null) {
					count++;
				}
			}
			if(count == vs.length) {
				return true;
			}
			
			if(value.length() != 7) {
				return false;
			}
			try {
				Date date = new SimpleDateFormat("yyyy-MM").parse(value);
				return date != null;
			} catch (ParseException e) {
				e.printStackTrace();
				return false;
			}*/
		case SELECT:
			//下拉框暂不做校验
		case PAGE:
			//页签暂不做任何校验
		case TEXT:
			//文本直接替换不做校验
			return true;
		default:
			break;
		}
		return false;
	}
	
	/**
	 * @Title: yearQuarterCount
	 * @Description: 生成年报表的季度小计，并返回年度总计。
	 * @Author:huangkui
	 * @Date: 2019年5月10日 上午10:11:12
	 * @param list
	 *            对象列表，注意：需要有 quarter，month两个属性
	 * @param begin
	 *            年初时间
	 * @param fields
	 *            需要累加的字段名
	 * @param monthConsumer
	 *            对每个月的数据处理
	 * @param quarterConsumer
	 *            对季度的数据处理
	 * @param clazz
	 * @return
	 */
	public static <T> T yearQuarterCount(List<T> list, Date begin, String[] fields, Consumer<T> monthConsumer,
			Consumer<T> quarterConsumer, Class<T> clazz) {
		Method[] quarterMethod = new Method[2], monthMethod = new Method[2];
		Method[] getMethods = new Method[fields.length];
		Method[] setMethods = new Method[fields.length];
		try {
			quarterMethod[0] = clazz.getDeclaredMethod("getQuarter");
			quarterMethod[1] = clazz.getDeclaredMethod("setQuarter", String.class);
			monthMethod[0] = clazz.getDeclaredMethod("getMonth");
			monthMethod[1] = clazz.getDeclaredMethod("setMonth", String.class);
			for (int i = 0; i < fields.length; i++) {
				String nn = fields[i].substring(0, 1).toUpperCase() + fields[i].substring(1);
				getMethods[i] = clazz.getDeclaredMethod("get" + nn);
				setMethods[i] = clazz.getDeclaredMethod("set" + nn, BigDecimal.class);
			}
		} catch (NoSuchMethodException | SecurityException e) {
			e.printStackTrace();
			throw new IllegalArgumentException("类（" + clazz.getName() + "）方法缺失。", e);
		}
		Date date = new Date(begin.getTime());
		Date end = DateUtils.yearEnd(begin);
		Date thisMonthEnd = DateUtils.monthEnd(new Date());
		if (end.getTime() > thisMonthEnd.getTime()) {
			end = thisMonthEnd;
		}
		long lastTime = DateUtils.monthBegin(end).getTime();
		BigDecimal[] counts = new BigDecimal[fields.length];
		BigDecimal[] totals = new BigDecimal[fields.length];
		Arrays.fill(counts, BigDecimal.ZERO);
		Arrays.fill(totals, BigDecimal.ZERO);
		if (list.size() > 0) {
			while (date.getTime() < end.getTime()) {
				int m = DateUtils.getMonth(date);
				String month = MONTH_DF.format(date);
				T obj = null;
				try {
					obj = list.stream().filter(t -> month.equals(getValue(monthMethod[0], t))).findFirst()
							.orElse(clazz.newInstance());
				} catch (InstantiationException | IllegalAccessException e1) {
					e1.printStackTrace();
					throw new IllegalArgumentException("类不能初始化：" + clazz.getName());
				}

				if (monthConsumer != null) {
					if (getValue(monthMethod[0], obj) == null) {
						setValue(monthMethod[1], obj, month);
					}
					monthConsumer.accept(obj);
				}
				for (int i = 0; i < getMethods.length; i++) {
					BigDecimal value = (BigDecimal) getValue(getMethods[i], obj);
					if (value == null) {
						value = BigDecimal.ZERO;
						setValue(setMethods[i], obj, value);
					}
					counts[i] = counts[i].add(value);
				}
				// 设置季度月份
				int q = DateUtils.getQuarter(date);
				setValue(quarterMethod[1], obj, "第" + (q) + "季度");
				setValue(monthMethod[1], obj, m + "月");

				if (m % 3 == 0 || date.getTime() == lastTime) {
					// 计算小计
					T object = null;
					try {
						object = clazz.newInstance();
					} catch (InstantiationException | IllegalAccessException e) {
						throw new IllegalArgumentException("创建对象失败：" + clazz.getName(), e);
					}
					// 设置季度
					int mm = m / 3;
					if (m % 3 > 0) {
						mm++;
					}
					setValue(quarterMethod[1], object, "第" + mm + "季度");
					setValue(monthMethod[1], object, "小计");
					// 设置各项统计值
					for (int i = 0; i < setMethods.length; i++) {
						BigDecimal value = counts[i];
						setValue(setMethods[i], object, value);
						// 累加总计
						totals[i] = totals[i].add(value);
						// 清零
						counts[i] = BigDecimal.ZERO;
					}
					if (quarterConsumer != null) {
						quarterConsumer.accept(object);
					}
					// 加入小计
					list.add(object);
				}

				date = DateUtils.monthAdd(date, 1);
			}
			list.sort((t1, t2) -> {
				String quarter1 = (String) getValue(quarterMethod[0], t1);
				String month1 = (String) getValue(monthMethod[0], t1);
				String quarter2 = (String) getValue(quarterMethod[0], t2);
				String month2 = (String) getValue(monthMethod[0], t2);
				if (quarter1.equals(quarter2)) {
					if (month1.equals("小计")) {
						return 1;
					} else if (month2.equals("小计")) {
						return -1;
					} else {
						return month1.compareTo(month2);
					}
				} else {
					return quarter1.compareTo(quarter2);
				}
			});
		}
		// 以下设置年度合计
		T obj = null;
		try {
			obj = clazz.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
			throw new IllegalArgumentException("创建对象失败：" + clazz.getName(), e);
		}
		setValue(quarterMethod[1], obj, DateUtils.getYear(begin) + "年总计");
		// 设置各项统计值
		for (int i = 0; i < setMethods.length; i++) {
			BigDecimal value = totals[i];
			setValue(setMethods[i], obj, value);
		}
		if (quarterConsumer != null) {
			quarterConsumer.accept(obj);
		}
		return obj;
	}

	private static void setValue(Method method, Object obj, Object value) {
		try {
			method.invoke(obj, value);
		} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
			throw new IllegalArgumentException("调用set方法失败：" + method.getName(), e);
		}
	}

	private static Object getValue(Method method, Object obj) {
		try {
			return method.invoke(obj);
		} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
			throw new IllegalArgumentException("调用get方法失败：" + method.getName(), e);
		}
	}

	/**
	 * @Title: calPercent
	 * @Description: 计算列表的数据占百分比
	 * @Author:huangkui
	 * @Date: 2019年5月10日 下午5:35:47
	 * @param list
	 * @param countFun
	 *            累加用字段函数
	 * @param percentFun
	 *            设置百分比函数
	 */
	public static <T> void calPercent(List<T> list, Function<T, BigDecimal> countFun,
			BiConsumer<T, BigDecimal> percentFun) {
		BigDecimal total = list.stream().map(countFun).reduce(BigDecimal.ZERO, (b1, b2) -> b1.add(b2));
		list.stream().forEach(t -> percentFun.accept(t, BigDecimalUtil.percent(countFun.apply(t), total)));
	}

	public static List<String> genMonthList(Date start, Date end) {
		List<String> list = new ArrayList<>();
		Date date = new Date(start.getTime());
		while (date.getTime() <= end.getTime()) {
			String moon = DateUtils.getYear(date) + "年";
			moon += DateUtils.getMonth(date) + "月";
			list.add(moon);
			date = DateUtils.monthAdd(date, 1);
		}
		return list;
	}

	public static List<String> genDateList(Date start, Date end) {
		List<String> list = new ArrayList<>();
		Date date = new Date(start.getTime());
		while (date.getTime() <= end.getTime()) {
			list.add(QueryConstants.DATE_FORMAT.format(date));
			date.setTime(date.getTime() + 24 * 3600 * 1000L);
		}
		return list;
	}

	public static <T> void totalCount(List<T> list, List<Function<T, BigDecimal>> gets, List<Consumer<BigDecimal>> sets,
			T total) {
		if (list.size() == 0) {
			// 如果列表为空，将总计数据初始化为空
			for (Consumer<BigDecimal> set : sets) {
				set.accept(BigDecimal.ZERO);
			}
			return;
		}
		for (T t : list) {
			for (int i = 0; i < gets.size(); i++) {
				Function<T, BigDecimal> get = gets.get(i);
				Consumer<BigDecimal> set = sets.get(i);
				BigDecimal v = get.apply(t);
				if (v == null) {
					v = BigDecimal.ZERO;
				}
				BigDecimal tv = get.apply(total);
				if (tv == null) {
					tv = BigDecimal.ZERO;
				}
				set.accept(v.add(tv));
			}
		}
	}

	public static Map<String, BigDecimal> transBigdecimalMap(List<Map<String, Object>> rows) {
		Map<String, BigDecimal> map = new HashMap<>();
		if (rows != null) {
			for (Map<String, Object> row : rows) {
				if (row != null) {
					String name = null;
					BigDecimal value = null;
					for (String key : row.keySet()) {
						Object obj = row.get(key);
						if (obj instanceof String) {
							name = (String) obj;
						} else {
							if (obj == null) {
								value = BigDecimal.ZERO;
							} else {
								value = new BigDecimal(obj.toString());
							}
						}
					}
					if (name != null && value != null) {
						map.put(name, value);
					}
				}
			}
		}
		return map;
	}

}
