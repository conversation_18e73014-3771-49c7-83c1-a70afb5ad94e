package cn.trasen.homs.SQLToAPI.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class DataEntity<PK extends Serializable> implements Serializable {

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1641034814997310698L;

	/**
	 * 主鍵ID
	 */
	@Id
	@Column(name = "id")
	@ApiModelProperty(value = "主鍵ID")
	private PK id;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDate;

	/**
	 * 创建人
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createUser;

	/**
	 * 创建人名称
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建人名称", hidden = true)
	private String createUserName;

	/**
	 * 修改时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "修改时间", hidden = true)
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 修改人名称
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "修改人", hidden = true)
	private String updateUser;

	/**
	 * 修改人名称
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "修改人名称", hidden = true)
	private String updateUserName;

	/**
	 * 刪除标志
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "刪除标志", hidden = true)
	private String isDeleted;
}