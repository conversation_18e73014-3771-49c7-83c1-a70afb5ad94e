package cn.trasen.homs.SQLToAPI.permission;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class PermissionUtil {
    public static String depFiled = "1";
    public static String staffFiled = "2";

    public static Map<String, String> adjudge(String tableName) {

        if (StringUtils.isEmpty(tableName)) {
            return null;
        }

        Map<String, String> tableMap = ((MyPermissionConfig) SpringUtil.getBean("myPermissionConfig")).getTableMap();
        Map<String, String> tablePatternMap = ((MyPermissionConfig) SpringUtil.getBean("myPermissionConfig")).getTablePatternMap();

        if (null != tableMap && tableMap.size() > 0) {
            for (Map.Entry<String, String> entry : tableMap.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(tableName)) {
                    return parseFiled(entry.getValue());
                }
            }
        }

        tableName = tableName.toUpperCase();
        if (null != tablePatternMap && tablePatternMap.size() > 0) {
            for (Map.Entry<String, String> entry : tablePatternMap.entrySet()) {
                if (tableName.matches(entry.getValue().toUpperCase())) {
                    return parseFiled(entry.getKey());
                }
            }
        }

        return null;
    }

    // KSDM#1,YSDM#2
    public static Map<String, String> parseFiled(String files) {
        if (StringUtils.isEmpty(files)) {
            return null;
        }

        String[] fss = files.split(",");
        Map<String, String> m = new HashMap<>();

        for (int i = 0; i < fss.length; i++) {
            String[] fm = fss[i].split("#");
            if (fm.length != 2 || !(PermissionUtil.depFiled.equals(fm[1]) || PermissionUtil.staffFiled.equals(fm[1]))) {
                continue;
            }
            m.put(fm[1], fm[0]);
        }

        return m;
    }

}
