package cn.trasen.homs.SQLToAPI.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;

import cn.trasen.homs.SQLToAPI.QueryException;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.service.UserDefineQueryProcFunService;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil.TdNode;
import cn.trasen.homs.SQLToAPI.utils.RequestBodyUtils;
import cn.trasen.homs.SQLToAPI.vo.ParseConditionVo;
import cn.trasen.homs.SQLToAPI.vo.ProcFunExcelVo;
import cn.trasen.homs.SQLToAPI.vo.UserDefineServiceVo;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcFunResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultPlace;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineProcFunVo;
import cn.trasen.homs.bean.sso.UserDataSource;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
//import cn.trasen.query.utils.TableToExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户自定义存储过程、函数查询Controller
 * @Author:huangkui
 * @Date: 2019年7月24日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@RestController
@Api(tags = "用户自定义存储过程、函数查询Controller")
@Slf4j
public class UserDefineQueryProcFunController {

	@Autowired
	private UserDefineQueryProcFunService userDefineQueryProcFunService;

	@Autowired
	private UserDataSourceService userDataSourceService;
	
	@Autowired
	private DateDefaultCfg dateCfg;
	
	@ApiOperation("第一步：解析SQL语句，返回参数列表")
	@PostMapping("/api/user/query/procFun/parseConditionSimple")
//	public PlatformResult<UserDefineProcFunVo> parseConditionSimple(
//			@ApiParam(value = "查询SQL") @RequestParam("sql") String sql) {
	public PlatformResult<UserDefineProcFunVo> parseConditionSimple(HttpServletRequest request) {
		try {
			ParseConditionVo vo = readParseConditionBody(request);
			if(vo == null){
				throw new IllegalArgumentException("参数不能为空");
			}
			return PlatformResult.success(userDefineQueryProcFunService.parseConditionSimple(vo.getSql() != null ? vo.getSql() : vo.getQuerySql()));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return exception(e);
		}
	}
	
	/**
     * 保存自定义报表数据BASE64处理
     * @param req
     * @return
     * @date 2022年01月07日 15:29:05
     * <AUTHOR>
     */
    private ParseConditionVo readParseConditionBody(HttpServletRequest req) {
        String body = null;
        Charset utf8 = Charset.forName("UTF-8");
        try {
            ServletInputStream reader = req.getInputStream();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] bs = new byte[4096];
            int len = reader.read(bs);
            while (len > 0) {
                bos.write(bs, 0, len);
                len = reader.read(bs);
            }

            if (bos.size() > 0) {
                body = new String(bos.toByteArray(),utf8);
            }
        } catch (IOException e) {
            log.error(e.getMessage(),e);;
        }
        Assert.notNull(body,"没有提交数据");
        JSONObject obj = JSONObject.parseObject(body);
        if(obj.containsKey("base64")){
            String str = obj.getString("base64");
            str = new String(Base64.getMimeDecoder().decode(str),utf8);
            obj = JSONObject.parseObject(str);
        }
        return obj.toJavaObject(ParseConditionVo.class);
    }

	@ApiOperation("第一步：解析SQL语句，返回参数列表")
	@PostMapping("/api/user/query/procFun/parseCondition")
//	public PlatformResult<UserDefineProcFunVo> parseCondition(
//			@ApiParam(value = "查询SQL") @RequestParam("sql") String sql) {
	public PlatformResult<UserDefineProcFunVo> parseCondition(HttpServletRequest request) {
		try {
			ParseConditionVo vo = readParseConditionBody(request);
			if(vo == null){
				throw new IllegalArgumentException("参数不能为空");
			}
			return PlatformResult.success(userDefineQueryProcFunService.parseCondition(vo.getSql()));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return exception(e);
		}
	}

	private PlatformResult<UserDefineProcFunVo> exception(Exception e) {
		if (e instanceof IllegalArgumentException) {
			return PlatformResult.failure(e.getMessage());
		} else {
			return PlatformResult.failure("系统错误：" + e.getMessage());
		}
	}

//	@ApiOperation("自定义存储过程、函数导出csv")
//	@PostMapping("/api/user/query/procFun/export/csv")
//	public ResponseEntity<byte[]> exportCsv(@RequestBody ProcFunExcelVo excelPo) throws IOException{
//		return export(excelPo,true,(map,bos)->{
//			try {
//				TableToExcelUtil.tableToCsv(map,bos);
//				return ".csv";
//			} catch (IOException e) {
//				e.printStackTrace();
//				log.error(e.getMessage(),e);
//			}
//			return null;
//		});
//	}

	private ResponseEntity<byte[]> export(ProcFunExcelVo excelPo,boolean isCsv, BiFunction<Map<String, String>, OutputStream, String> biFun) throws IOException{
		UserDefineProcFunVo query = userDefineQueryProcFunService.selectById(excelPo.getId());
		Assert.notNull(query, "找不到对应配置");
		String fileName = excelPo.getFileName();
		if (!StringUtils.hasText(fileName)) {
			fileName = query.getQueryName();
		} else {
			if (fileName.indexOf('.') != -1) {
				fileName = fileName.substring(0, fileName.indexOf('.'));
			}
		}
		List<ProcFunResultVo> resultList = query(query, excelPo.getParameters());
		log.info("查询结果size={}", resultList.size());

		Map<String, String> htmlMap = new HashMap<>();
		StringBuffer oneColBf = new StringBuffer("<table>");
		for (ProcFunResultVo rvo : resultList) {
			ProcedureResultVo result = query.getResultColumns().stream().filter(r -> r.getName().equals(rvo.getName()))
					.findFirst().orElse(null);
			ProcedureResultType type = result.getType();
			List<TableColumnVo> cols = result.getTableColumns();
			List<Map<String, Object>> rows = rvo.getResult();
			if (type == ProcedureResultType.DATASET || type == ProcedureResultType.CURSOR) {
				List<TdNode> tdTree = CollectionUtils.isEmpty(excelPo.getTitles())?
						ReportUtil.transTdNodeTree(cols): ReportUtil.transTdNodeTree(excelPo.getTitles());
				String table = ReportUtil.treeToHtmlTableHead(tdTree, rows);
				htmlMap.put(result.getTitle(), table);
			} else {
				// 其它类型放到同一个sheet
				TableColumnVo tcVo = cols.iterator().next();
				oneColBf.append("<tr><td>");
				oneColBf.append(tcVo.getTitle());
				oneColBf.append("</td><td>");
				Object value = null;
				if (rows != null && rows.size() > 0) {
					Map<String, Object> map = rows.iterator().next();
					if (map != null && map.size() > 0) {
						value = map.values().iterator().next();
					}
				}
				oneColBf.append(value == null ? "-" : value.toString());
				oneColBf.append("</td></tr>\r\n");
			}
		}
		if (!isCsv && oneColBf.length() > 7) {
			//CSV只支持导出数据集或游标！！
			oneColBf.append("</table>");
			htmlMap.put("结果集", oneColBf.toString());
		}
		log.info("将导出sheet名：{}", htmlMap.keySet());
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		String suffix = biFun.apply(htmlMap, bos);
		//TableToExcelUtil.tableToExcel(htmlMap, bos);
		bos.close();

		HttpHeaders headers = new HttpHeaders();
		if (fileName.equals("")) {
			fileName = "Export"+suffix;
		} else {
			fileName += suffix;
		}
		// 设置编码
		String downloadFileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
		headers.setContentDispositionFormData("attachment", downloadFileName);
		headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		return new ResponseEntity<byte[]>(bos.toByteArray(), headers, HttpStatus.CREATED);
	}

//	@ApiOperation("自定义存储过程、函数导出excel")
//	@PostMapping("/api/user/query/procFun/export/excel")
//	public ResponseEntity<byte[]> exportExcel(@RequestBody ProcFunExcelVo excelPo)
//			throws IOException {
//		return export(excelPo,false,(map,bos)->{
//			try {
//				TableToExcelUtil.tableToExcel(map,bos);
//				return ".xls";
//			} catch (IOException e) {
//				e.printStackTrace();
//				log.error(e.getMessage(),e);
//			}
//			return null;
//		});
//	}
	
	@ApiOperation("自定义存储过程、函数查询（body参数方式）")
	@RequestMapping( value = "/api/user/query/procFun/bodyQuery",method = {RequestMethod.GET,RequestMethod.POST})
	public PlatformResult<List<ProcFunResultVo>> query(HttpServletRequest request) {
		byte[] bodys = RequestBodyUtils.readBody(request);
		if(bodys == null) {
			return PlatformResult.failure("没有请求体内容");
		}
		String bodyString = new String(bodys,Charset.forName("UTF-8"));
		JSONObject object = null;
		try {
			object = JSONObject.parseObject(bodyString);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			return PlatformResult.failure("请求的JSON格式错误："+bodyString);
		}
		if(!object.containsKey("id")) {
			return PlatformResult.failure("没有请求体内容必须提供存储过程ID");
		}
		String[] idAndPara = new String[2];
		CommonSqlUtil.parseIdAndParasFromJson(object, (i,p)->{idAndPara[0]=i;idAndPara[1]=p;});
		return query(idAndPara[0],idAndPara[1],request);
	}

	@ApiOperation("自定义存储过程、函数查询")
	@RequestMapping( value = "/api/user/query/procFun/query",method = {RequestMethod.GET,RequestMethod.POST})
	public PlatformResult<List<ProcFunResultVo>> query(@ApiParam(value = "自定义查询ID") @RequestParam("id") String id,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},...]）") 
	@RequestParam(value = "parameters", required = false) String parameters,
	HttpServletRequest request) {
		try {
			UserDefineProcFunVo query = userDefineQueryProcFunService.selectById(id);
			if (query == null) {
				throw new IllegalArgumentException("没找到ID为：【" + id + "】的【函数】");
			}
			String paraStr = CommonSqlUtil.paraseParameter(request,parameters,query.getQueryConditions(),ProcedureParamVo::getName);	
			return PlatformResult.success(query(query, paraStr));
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof QueryException) {
				return PlatformResult.failure("查询发生了错误，请检查SQL语句及参数、结果配置后重试。");
			}
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	private List<ProcFunResultVo> query(UserDefineProcFunVo query, String parameters) {
		String paraStr = parameters;
		List<QueryParamVo> queryParamVo = null;
		paraStr = CommonSqlUtil.loadDefaultParas(paraStr,query.getQueryConditions(), dateCfg);
//		if (!StringUtils.hasText(paraStr)) {
//			paraStr = CommonSqlUtil.loadDefaultParas(query.getQueryConditions(), dateCfg);
//			log.info("加载默认值作为参数--》{}", paraStr);
//		}
		if (StringUtils.hasText(paraStr)) {
			queryParamVo = JSONObject.parseArray(paraStr, QueryParamVo.class);
		}
		if (queryParamVo == null) {
			log.warn("无参数的存储过程？{}-->{}", query.getQueryName(), query.getQuerySql());
			queryParamVo = new ArrayList<QueryParamVo>();
		}
		//转换带逗号数字类型参数值
		CommonSqlUtil.transNumberParam(queryParamVo);
		String chkResult = userDefineQueryProcFunService.check(query, queryParamVo, true);
		if (chkResult != null) {
			throw new IllegalArgumentException(chkResult);
		}
		List<ProcFunResultVo> resultList = userDefineQueryProcFunService.query(query, queryParamVo);
		log.info("结果数量：{}", resultList.size());

		// 需要对结果进行处理，根据结果配置格式化数据！！！
		List<ProcedureResultVo> resultColumns = query.getResultColumns();
		for (ProcedureResultVo rvo : resultColumns) {
			String name = rvo.getName();
			ProcFunResultVo vo = resultList.stream().filter(p -> p.getName().equals(name)).findFirst().orElse(null);
			if (vo != null) {
				List<Map<String, Object>> resultData = vo.getResult();
				List<TableColumnVo> colSettings = rvo.getTableColumns();
				List<TableColumnVo> colList = new ArrayList<>();
				// 必须有字段设置
				CommonSqlUtil.treeToList(colSettings, colList);
				vo.setResult(CommonSqlUtil.transResult(colList, resultData));
				log.info("转换了结果：{}", name);
			} else {
				log.warn("没有对应结果设置：{}", name);
			}
		}
		return resultList;
	}

	@ApiOperation("第二步：预查询，返回字段列表")
	@PostMapping("/api/user/query/procFun/preQuery")
//	public PlatformResult<UserDefineProcFunVo> preQuery(UserDefineService query,
//			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},...]）") @RequestParam(value = "parameters", required = false) String parameters) {
	public PlatformResult<UserDefineProcFunVo> preQuery(HttpServletRequest request) {
		try {
			UserDefineServiceVo queryVo = readFromBody(request);
			UserDefineProcFunVo vo = userDefineQueryProcFunService.copyFrom(queryVo);
			Assert.hasText(vo.getQuerySql(), "必须输入调用SQL语句");
			Assert.hasText(vo.getDsId(), "必需选择数据源");
			UserDataSource ds = userDataSourceService.selectDecrypt(vo.getDsId());
			Assert.notNull(ds, "数据源不存在");
			List<ProcedureParamVo> cds = vo.getQueryConditions();

			List<QueryParamVo> paramList = new ArrayList<>();
			if (queryVo.getParameters() != null) {
				// 前端传递了参数
				log.info("预查询参数：{}", queryVo.getParameters());
				paramList = JSONObject.parseArray(queryVo.getParameters(), QueryParamVo.class);
			} else {
				if (cds != null) {
					for (ProcedureParamVo cd : cds) {
						QueryParamVo param = new QueryParamVo();
						param.setName(cd.getName());
						String value = cd.getDefaultValue();
						Assert.notNull(value, String.format("参数%s需要设置默认值进行预查询", cd.getName()));
						param.setValue(value);
						paramList.add(param);
					}
					log.info("根据参数默认值设置了参数：{}", paramList);
				}
			}

			String checkResult = userDefineQueryProcFunService.check(vo, paramList, false);
			Assert.isNull(checkResult, checkResult);
			List<ProcFunResultVo> result = userDefineQueryProcFunService.query(vo, paramList);
			log.info("预查询结果数量：{}", result == null ? 0 : result.size());
			// 对结果进行处理，根据结果配置返回的数据格式
			if (result != null && result.size() > 0) {
				List<ProcedureResultVo> resultColumns = vo.getResultColumns();
				for (ProcFunResultVo rvo : result) {
					ProcedureResultVo cvo = resultColumns.stream().filter(r -> r.getName().equals(rvo.getName()))
							.findFirst().orElse(null);
					if (cvo != null) {
						List<TableColumnVo> tableCols = cvo.getTableColumns();
						if (tableCols == null) {
							tableCols = new ArrayList<>();
							cvo.setTableColumns(tableCols);
						} else {
							// 先清除掉原有字段配置
							tableCols.clear();
						}
						List<Map<String, Object>> resultList = rvo.getResult();
						if (resultList != null && resultList.size() > 0) {
							Map<String, Object> resultMap = resultList.iterator().next();
							if (resultMap != null) {
								for (String key : resultMap.keySet()) {
									Object obj = resultMap.get(key);
									TableColumnVo tcvo = new TableColumnVo();
									tcvo.setName(key);
									tcvo.setTitle(key);
									if (obj instanceof BigDecimal) {
										tcvo.setType(ColumnType.NUMBER);
									} else {
										tcvo.setType(ColumnType.STRING);
									}
									tableCols.add(tcvo);
								}
							}
							List<TableColumnVo> colList = new ArrayList<>();
							// 转换成显示数据
							CommonSqlUtil.treeToList(tableCols, colList);
							rvo.setResult(CommonSqlUtil.transResult(colList, resultList));
							log.info("预查询转换了结果：{}-->{}", colList, rvo.getResult().size());
						} else {
							rvo.setResult(new ArrayList<>());
							log.warn("预查询获取结果为空：{}", rvo.getName());
							throw new IllegalArgumentException(String.format("结果%s为空，请检查对应的默认值后重试", rvo.getName()));
						}
					} else {
						log.warn("预查询找不到结果配置：{}", rvo.getName());
						throw new IllegalArgumentException(String.format("返回了名为%s的结果，但是没有对应的结果配置", rvo.getName()));
					}
				}
				queryVo.setColumnDefine(JSONObject.toJSONString(resultColumns));
			} else {
				log.warn("预查询返回结果为空：{}", result);
				throw new IllegalArgumentException("预查询结果为空， 请检查参数及结果配置后重试");
			}
			vo.setQueryResult(result);
			return PlatformResult.success(vo);
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof QueryException) {
				return PlatformResult.failure("预查询发生了错误，请检查SQL语句及参数、结果配置后重试。");
			}
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @param query
	 * @param otherSave 是否是列表上更新数据，默认为false
	 * @return
	 */
	@ApiOperation("第三步：添加或修改自定义查询")
	@PostMapping("/api/user/query/procFun/saveOrUpdate")
//	public PlatformResult<UserDefineProcFunVo> saveOrUpdate(UserDefineService query, boolean otherSave) {
	public PlatformResult<UserDefineProcFunVo> saveOrUpdate(HttpServletRequest request) {
		try {
			UserDefineServiceVo query = readFromBody(request);
			//判断是否是列表更新，是则将数据库替换取出来替换当前传递过来的tableColumns
			if(query.isOtherSave() && StringUtils.hasText(query.getId())){
				UserDefineProcFunVo oldVo = userDefineQueryProcFunService.selectById(query.getId());
				if(null != oldVo){
					query.setColumnDefine(oldVo.getColumnDefine());
					query.setConditionDefine(oldVo.getConditionDefine());
				}
			}
			
			UserDefineProcFunVo vo = userDefineQueryProcFunService.copyFrom(query);
			Assert.hasText(vo.getQueryName(), "请设置查询名称");
			Assert.hasText(vo.getDsId(), "必需选择数据源");
			UserDataSource ds = userDataSourceService.selectDecrypt(vo.getDsId());
			Assert.notNull(ds, "数据源不存在");

			String checkResult = userDefineQueryProcFunService.check(vo, null, true);
			Assert.isNull(checkResult, checkResult);
			// 增加返回结果字段设置检查
			List<ProcedureResultVo> resultColumns = vo.getResultColumns();
			for (ProcedureResultVo rvo : resultColumns) {
				List<TableColumnVo> cols = rvo.getTableColumns();
				Assert.notEmpty(cols, String.format("返回结果[%s]缺少字段定义", rvo.getName()));
				Assert.notNull(rvo.getType(), String.format("返回结果[%s]未定义类型", rvo.getName()));
				ProcedureResultType type = rvo.getType();
				if (type != ProcedureResultType.CURSOR && type != ProcedureResultType.DATASET) {
					// 返回单个结果
					if(rvo.getLocation() == null) {
						rvo.setLocation(ProcedureResultPlace.ORDERLEFT);
					}
					//Assert.notNull(rvo.getLocation(), String.format("返回结果[%s]为单值，必须定义“显示位置”", rvo.getName()));
				}
				long count = cols.stream().filter(t -> t.getType() == ColumnType.PAGE).count();
				Assert.isTrue(count == 0, String.format("返回结果[%s]字段类型不能为PAGE(页签)", rvo.getName()));
			}
			return PlatformResult.success(userDefineQueryProcFunService.saveOrUpdate(query));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return exception(e);
		}
	}

    /**
     * 保存自定义报表数据BASE64处理
     * @param req
     * @return
     * @date 2022年01月0日 15:29:05
     * <AUTHOR>
     */
    private UserDefineServiceVo readFromBody(HttpServletRequest req) {
        String body = null;
        Charset utf8 = Charset.forName("UTF-8");
        try {
            ServletInputStream reader = req.getInputStream();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] bs = new byte[4096];
            int len = reader.read(bs);
            while (len > 0) {
                bos.write(bs, 0, len);
                len = reader.read(bs);
            }

            if (bos.size() > 0) {
                body = new String(bos.toByteArray(),utf8);
            }
        } catch (IOException e) {
            log.error(e.getMessage(),e);;
        }
        Assert.notNull(body,"没有提交数据");
        JSONObject obj = JSONObject.parseObject(body);
        if(obj.containsKey("base64")){
            String str = obj.getString("base64");
            str = new String(Base64.getMimeDecoder().decode(str),utf8);
            obj = JSONObject.parseObject(str);
        }
        return obj.toJavaObject(UserDefineServiceVo.class);
    }

	@ApiOperation("获取自定义查询")
	@GetMapping("/api/user/query/procFun/select/{id}")
	public PlatformResult<UserDefineProcFunVo> selectById(@ApiParam(value = "自定义查询ID") @PathVariable("id") String id) {
		UserDefineProcFunVo vo = userDefineQueryProcFunService.selectById(id);
		UserDefineProcFunVo pvo = userDefineQueryProcFunService.parseCondition(vo.getQuerySql());
		vo.setSavedConditions(vo.getQueryConditions());
		vo.getSavedConditions().forEach(c -> c.setId(IdGeneraterUtils.nextId()));
//		vo.setQueryConditions(pvo.getQueryConditions());
		return PlatformResult.success(vo);
	}

	@ApiOperation("自定义查询列表（可以按照名称模糊检索）")
	@PostMapping("/api/user/query/procFun/list")
	public DataSet<UserDefineProcFunVo> list(Page page,
			@ApiParam(value = "查询条件，模糊查询匹配名称") @RequestParam(name = "condition", required = false) String condition) {
		List<UserDefineProcFunVo> list = userDefineQueryProcFunService.list(condition, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	@ApiOperation("删除自定义查询")
	@PostMapping("/api/user/query/procFun/delete/{id}")
	public PlatformResult<Integer> deleteById(@ApiParam(value = "自定义查询ID") @PathVariable("id") String id) {
		return PlatformResult.success(userDefineQueryProcFunService.delete(id));
	}
}
