package cn.trasen.homs.SQLToAPI.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;

import cn.trasen.homs.SQLToAPI.QueryConstants;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.dao.UserDefineServiceMapper;
import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.permission.ThpUserScopeContextHolder;
import cn.trasen.homs.SQLToAPI.permission.model.ThpsUser;
import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.service.UserDefineQueryProcFunService;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil.ProcFunIO;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcFunResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultPlace;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultType;
import cn.trasen.homs.SQLToAPI.vo.report.ProcedureResultVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineProcFunVo;
import cn.trasen.homs.bean.sso.UserDataSource;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户自定义存储过程、函数查询-服务实现
 * @Author:huangkui
 * @Date: 2019年7月24日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class UserDefineQueryProcFunServiceImpl implements UserDefineQueryProcFunService {

	@Autowired
	private UserDataSourceService userDataSourceService;
	
	@Autowired
	private UserDefineServiceMapper serviceMapper;

	@Autowired
	private DateDefaultCfg dateCfg;

	private static final Pattern paramPt = Pattern.compile(":(\\w+)\\,?", Pattern.MULTILINE | Pattern.CASE_INSENSITIVE);

	private static final Pattern procFunPt = Pattern.compile(
			"^\\s*(:(\\w+)\\s*=)?\\s*call\\s+(\\w+)?\\(([:\\w,\\s]*)\\)\\s*$",
			Pattern.MULTILINE | Pattern.CASE_INSENSITIVE);

	@Override
	public UserDefineProcFunVo selectById(String id) {
		UserDefineService query = serviceMapper.selectByPrimaryKey(id);
		if (query != null) {
			UserDataSource ds = userDataSourceService.selectDecrypt(query.getDsId());
			UserDefineProcFunVo vo = copyFrom(query);
			if (ds != null) {
				vo.setDsName(ds.getDsName());
				vo.setDsType(ds.getDsType());
			}
			return vo;
		}
		return null;
	}

	@Override
	public UserDefineProcFunVo copyFrom(UserDefineService query) {
		UserDefineProcFunVo vo = new UserDefineProcFunVo();
		BeanUtils.copyProperties(query, vo);
		if (StringUtils.hasText(query.getConditionDefine())) {
			vo.setQueryConditions(JSONObject.parseArray(query.getConditionDefine(), ProcedureParamVo.class));
		}
		if (StringUtils.hasText(query.getColumnDefine())) {
			vo.setResultColumns(JSONObject.parseArray(query.getColumnDefine(), ProcedureResultVo.class));
		}
		return vo;
	}

	@Override
	public List<QueryParamVo> transParam(String paramStr) {
		return JSONObject.parseArray(paramStr, QueryParamVo.class);
	}

	@Override
	public String check(UserDefineProcFunVo vo, List<QueryParamVo> queryParamList, boolean checkColumn) {
		// 1、第一步解析
		UserDefineProcFunVo pvo = parseCondition(vo.getQuerySql());
		// 2、第二部对比解析查看返回值、参数
		List<ProcedureParamVo> params = vo.getQueryConditions();
		List<ProcedureResultVo> results = vo.getResultColumns();
		if (results == null || results.size() == 0) {
			return "至少设置一个结果对象";
		}
		List<ProcedureParamVo> pParams = pvo.getQueryConditions();
		List<ProcedureResultVo> pResults = pvo.getResultColumns();
		String pResultName = null;
		if (pResults.size() > 0) {
			// 检查结果配置是否有函数返回值
			ProcedureResultVo resultVo = pResults.iterator().next();
			ProcedureResultVo rvo = results.stream().filter(r -> r.getName().equals(resultVo.getName())).findFirst()
					.orElse(null);
			if (rvo == null) {
				return String.format("必须包含名为%s结果配置", resultVo.getName());
			}
			pResultName = resultVo.getName();
		}
		Set<String> pNames = pParams.stream().map(ProcedureParamVo::getName).collect(Collectors.toSet());
		Set<String> names = new HashSet<>();
		Set<String> notRepeat = new HashSet<>();
		if (params != null) {
			for (ProcedureParamVo param : params) {
				String name = param.getName();
				if (!pNames.contains(name)) {
					return String.format("查询SQL不包含名为%s的参数", name);
				}
				String defaultValue = param.getDefaultValue();
				ColumnType colType = param.getColumnType();
				if (colType == ColumnType.PAGE) {
					if (!StringUtils.hasText(defaultValue)) {
						return String.format("参数%s的类型为%s，必须输入默认值（范例：人次,'1';收入,'2'）", name, colType.getTypeName());
					} else if (!checkPageDefaultValue(defaultValue, defaultValue)) {
						return String.format("参数%s的类型为%s，默认值值格式不符合规范（范例：人次,'1';收入,'2'）", name, colType.getTypeName());
					}
				} else if (!ReportUtil.checkDefaultValue(colType, defaultValue, dateCfg)) {
					return String.format("参数%s默认值与类型[%s]不符。", name, colType.getTypeName());
				}
				names.add(name);
				if (notRepeat.contains(name)) {
					return String.format("重复的参数%s", name);
				}
				notRepeat.add(name);
			}
		}
		notRepeat.clear();
		for (ProcedureResultVo result : results) {
			String name = result.getName();
			if (pResultName == null || !pResultName.equals(name)) {
				if (!pNames.contains(name)) {
					return String.format("查询SQL不包含名为%s的输出参数", name);
				}
				names.add(name);
			}
			List<TableColumnVo> cols = result.getTableColumns();
			if (checkColumn && CollectionUtils.isEmpty(cols)) {
				return String.format("返回数据[%s]必须设置返回字段", name);
			}
			for(TableColumnVo cvo:cols) {
				String rs = new UserDefineQueryTableServiceImpl().checkSubQuery(cvo, serviceMapper);
				if(rs != null) {
					return rs;
				}
			}
			if (notRepeat.contains(name)) {
				return String.format("重复的返回数据%s", name);
			}
			notRepeat.add(name);
		}
		if (names.size() != pNames.size()) {
			return "参数个数与查询SQL不符";
		}

		// 第3步：检查参数
		if (queryParamList != null) {
			for (ProcedureParamVo param : params) {
				String name = param.getName();
				List<QueryParamVo> stream = queryParamList.stream().filter(p -> p.getName().equals(name))
						.collect(Collectors.toList());
				int count = (int) stream.size();
				if (count > 1) {
					return String.format("参数%s重复", name);
				}
				QueryParamVo qvo = null;
				if (count == 0) {
					if (param.getColumnType() == ColumnType.STRING
							|| StringUtils.hasText(param.getDefaultValue())) {
						//如果类型是文字允许默认值为空字符串
						qvo = new QueryParamVo();
						qvo.setName(name);
						qvo.setValue(param.getDefaultValue());
						if (param.getColumnType() == ColumnType.PAGE) {
							String v = param.getDefaultValue();
							int idx = v.indexOf(';');
							if (idx == -1) {
								return String.format("参数%s默认值不符合规范。范例：人次,1;收入,2", name);
							}
							v = v.substring(0, idx);
							idx = v.indexOf(',');
							if (idx == -1) {
								return String.format("参数%s默认值不符合规范。范例：人次,'1';收入,'2'", name);
							}
							v = v.substring(idx + 1);
							if (!StringUtils.hasText(v)) {
								return String.format("参数%s默认值不符合规范。范例：人次,'a';收入,'b'", name);
							}
							qvo.setValue(v);
						}
						// 利用默认值添加一个参数
						queryParamList.add(qvo);
					} else {
						return String.format("缺少%s参数值", name);
					}
				} else {
					qvo = stream.iterator().next();
				}
				ColumnType colType = param.getColumnType();
				if (colType == null) {
					return String.format("参数%s没有设置数据类型", name);
				}
				if(colType == ColumnType.PAGE) {
					if (!checkPageDefaultValue(qvo.getValue(), param.getDefaultValue())) {
						return String.format("参数%s的类型为%s，参数值不符合设置", name, colType.getTypeName());
					}
				}else if(!ReportUtil.checkDefaultValue(colType, qvo.getValue(), dateCfg)){
					return String.format("%s%s不符合所选类型[%s]", name, count == 1 ? "参数值" : "参数默认值",
							colType.getTypeName());
				}

			}
		}
		return null;
	}

	private boolean checkPageDefaultValue(String value, String defaultValue) {
		String[] vs = defaultValue.split(";");
		if (vs.length < 2) {
			return false;
		}
		List<String[]> list = new ArrayList<>();
		Boolean isNum = null;
		for (String v : vs) {
			int idx = v.indexOf(',');
			if (idx == -1) {
				return false;
			}
			String vv = v.substring(idx + 1);
			if (QueryConstants.NUMBER_PT.matcher(vv).find()) {
				if (isNum != null && !isNum.booleanValue()) {
					// 前面有文字，现在又出现数字
					return false;
				} else {
					isNum = true;
				}
			} else {
				if (isNum != null && isNum.booleanValue()) {
					// 前面有数字，现在又出现文字
					return false;
				} else {
					isNum = false;
				}
			}
			list.add(new String[] { v.substring(0, idx), vv });
		}
		if (!value.equals(defaultValue)) {
			// 检查取值
			for (int i = 0; i < list.size(); i++) {
				String[] vvs = list.get(i);
				if (value.equals(vvs[1])) {
					return true;
				}
			}
			// 没有匹配值
			return false;
		}
		return true;
	}

	@Override
	@Transactional(readOnly = false)
	public Integer delete(String id) {
		Assert.hasText(id, "ID不能为空.");
		UserDefineService record = new UserDefineService();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = ThpUserScopeContextHolder.get();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return serviceMapper.updateByPrimaryKeySelective(record);
	}

	public static void main(String[] args) {
		// String str = " :aa = call func1(:p1,:p2,:p3)";
		// String str = " :aa = call func1( )";
		// String str = " call func1(:p1,:p2,:p3)";
		String str = " :a  = call func1(:p1,:p2,:p3)";
		Matcher mc = procFunPt.matcher(str);
		if (mc.find()) {
			int g = mc.groupCount();
			for (int i = 1; i <= g; i++) {
				System.out.println(i + "-->" + mc.group(i));
			}
			System.out.println(mc.start(4) + "--" + mc.end(4));
			String pstr = str.substring(mc.start(4), mc.end(4));
			System.out.println(pstr);
			mc = paramPt.matcher(pstr);
			while (mc.find()) {
				System.out.println(mc.group(1));
			}
		} else {
			System.out.print("未匹配");
		}
	}

	@Override
	public UserDefineProcFunVo parseCondition(String sql) {
		Matcher mc = procFunPt.matcher(sql);
		if (mc.find()) {
			String rtName = mc.group(2);
			// String funName = mc.group(3);
			String paraString = mc.group(4);
			Set<String> names = new HashSet<>();
			if (rtName != null) {
				names.add(rtName);
			}
			List<String> paraList = new ArrayList<>();
			mc = paramPt.matcher(paraString);
			while (mc.find()) {
				String name = mc.group(1);
				paraList.add(name);
				if (names.contains(name)) {
					// 重复的参数名
					throw new IllegalArgumentException(String.format("参数名%s重复", name));
				} else {
					names.add(name);
				}
			}
			UserDefineProcFunVo vo = new UserDefineProcFunVo();
			vo.setQuerySql(QueryConstants.spacePt.matcher(sql).replaceAll(" "));
			vo.setReturnName(rtName);
			List<ProcedureParamVo> queryConditions = new ArrayList<>();
			for (String para : paraList) {
				ProcedureParamVo paramVo = new ProcedureParamVo();
				paramVo.setName(para);
				paramVo.setTitle(para);
				paramVo.setColumnType(ColumnType.STRING);
				paramVo.setDefaultValue("");
				queryConditions.add(paramVo);
			}
			vo.setQueryConditions(queryConditions);
			List<ProcedureResultVo> resultColumns = new ArrayList<>();

			if (rtName != null) {
				// 说明是函数调用，返回值
				ProcedureResultVo paramVo = new ProcedureResultVo();
				paramVo.setName(rtName);
				paramVo.setLocation(ProcedureResultPlace.HEADLEFT);
				paramVo.setType(ProcedureResultType.STRING);
				TableColumnVo tvo = new TableColumnVo();
				tvo.setName(rtName);
				tvo.setTitle(rtName);
				tvo.setType(ColumnType.STRING);
				paramVo.setTableColumns(Arrays.asList(tvo));
				resultColumns.add(paramVo);
			}
			// 不做定义
			vo.setResultColumns(resultColumns);
			return vo;
		} else {
			log.warn("语句不符合规范：{}", sql);
			throw new IllegalArgumentException(
					"错误的存储过程/函数调用SQL。（函数示例-- :retrun = call func(:name,:age)\r\n存储过程示例-- call proc(:name,:return)）");
		}
	}

	@Override
	public List<UserDefineProcFunVo> list(String condition, Page page) {
		List<UserDefineProcFunVo> result = serviceMapper.listProcFun(page, condition);
		Map<String, String> map = userDataSourceService.names();
		if(map != null) {
			for(UserDefineProcFunVo vo:result) {
				vo.setDsName(map.get(vo.getDsId()));
			}
		}
		return result;
	}

	@Override
	public List<ProcFunResultVo> query(UserDefineProcFunVo query, List<QueryParamVo> paramList) {
		UserDataSource userDs = userDataSourceService.selectDecrypt(query.getDsId());
		List<ProcFunIO> ios = new ArrayList<>();
		List<ProcedureResultVo> results = query.getResultColumns();
		if (results == null) {
			results = new ArrayList<>();
		}
		Set<String> ioNames = new HashSet<>();
		if (query.getQueryConditions() != null) {
			for (ProcedureParamVo paramVo : query.getQueryConditions()) {
				String value = null;
				Optional<QueryParamVo> op = paramList.stream().filter(p -> p.getName().equals(paramVo.getName()))
						.findFirst();
				if (op.isPresent()) {
					value = op.get().getValue();
				} else {
					throw new IllegalArgumentException(String.format("缺少参数%s值", paramVo.getName()));
				}
				// 判断值为空，设置默认值,防止不传值，条件为空问题。
				if(StringUtils.isEmpty(value)  && !StringUtils.isEmpty(paramVo.getDefaultValue())) {
					value = paramVo.getDefaultValue();
				}
				Integer type = null;
				switch (paramVo.getColumnType()) {
				case SELECT:
					//下拉框只支持字符串
				case STRING:
				case DATE:
				case DATEUNIT:
					type = CommonSqlUtil.ProcFun_Type_String;
					break;
				case NUMBER:
					type = CommonSqlUtil.ProcFun_Type_Number;
					break;
				case PAGE:
					if (value.charAt(0) == '\'') {
						type = CommonSqlUtil.ProcFun_Type_String;
					} else {
						type = CommonSqlUtil.ProcFun_Type_Number;
					}
					break;
				default:
					throw new IllegalArgumentException(String.format("参数%s类型错误", paramVo.getName()));
				}
				Integer ioType = CommonSqlUtil.ProcFun_Input;
				if (results.stream().filter(r -> r.getName().equals(paramVo.getName())).findFirst().isPresent()) {
					ioType = CommonSqlUtil.ProcFun_InOut;
					ioNames.add(paramVo.getName());
				}
				ProcFunIO io = new ProcFunIO(ioType, paramVo.getName(), type, value);
				ios.add(io);
			}
		}
		for (ProcedureResultVo rvo : results) {
			if (ioNames.contains(rvo.getName())) {
				// 如果前面已经设置成输入输出参数，则跳过
				continue;
			}
			Integer type = null;
			switch (rvo.getType()) {
			case STRING:
			case DATE:
				type = CommonSqlUtil.ProcFun_Type_String;
				break;
			case NUMBER:
				type = CommonSqlUtil.ProcFun_Type_Number;
				break;
			case CURSOR:
				type = CommonSqlUtil.ProcFun_Type_Cursor;
				break;
			case DATASET:
				type = CommonSqlUtil.ProcFun_Type_DataSet;
				break;
			default:
				throw new IllegalArgumentException(String.format("结果%s类型错误", rvo.getName()));
			}
			ProcFunIO io = new ProcFunIO(CommonSqlUtil.ProcFun_Output, rvo.getName(), type, null);
			ios.add(io);
		}
		log.info(String.format("开始调用存储过程/函数：%s-->%s", query.getQuerySql(), ios.toString()));
		return CommonSqlUtil.callProcFun(userDs, query.getQuerySql(), ios);
	}

	@Override
	@Transactional(readOnly = false)
	public UserDefineProcFunVo saveOrUpdate(UserDefineService query) {
		ThpsUser user = ThpUserScopeContextHolder.get();
		if (StringUtils.hasText(query.getId())) {
			UserDefineService db = serviceMapper.selectByPrimaryKey(query.getId());
			Assert.notNull(db, "自定义服务不存在！");
			Assert.isTrue(db.getModifyFlag() != null && db.getModifyFlag() == 1, "该自定义服务是历史数据，不允许修改！");
			
			// 更新
			query.setUpdateDate(new Date());
			if (user != null) {
				query.setUpdateUser(user.getUsercode());
				query.setUpdateUserName(user.getUsername());
			}
			serviceMapper.updateByPrimaryKeySelective(query);
		} else {
			// 新增
			//类型定死为2
			query.setServiceType(2);
			query.setId(IdGeneraterUtils.nextId());
			query.setCreateDate(new Date());
			query.setUpdateDate(new Date());
			query.setIsDeleted("N");
			//新增加的允许修改
			query.setModifyFlag(1);
			if (user != null) {
				query.setCreateUser(user.getUsercode());
				query.setCreateUserName(user.getUsername());
				query.setUpdateUser(user.getUsercode());
				query.setUpdateUserName(user.getUsername());
			}
			serviceMapper.insertSelective(query);
		}
		return copyFrom(query);
	}

	/**
	 * 简单解析：
	 * 1、出现在等于号左边的肯定是数据集
	 * 2、
	 */
	@Override
	public UserDefineProcFunVo parseConditionSimple(String sql) {
		Matcher mc = procFunPt.matcher(sql);
		if (mc.find()) {
			String rtName = mc.group(2);
			// String funName = mc.group(3);
			String paraString = mc.group(4);
			Set<String> names = new HashSet<>();
			if (rtName != null) {
				names.add(rtName);
			}
			List<String> paraList = new ArrayList<>();
			mc = paramPt.matcher(paraString);
			while (mc.find()) {
				String name = mc.group(1);
				paraList.add(name);
				if (names.contains(name)) {
					// 重复的参数名
					throw new IllegalArgumentException(String.format("参数名%s重复", name));
				} else {
					names.add(name);
				}
			}
			UserDefineProcFunVo vo = new UserDefineProcFunVo();
			vo.setQuerySql(QueryConstants.spacePt.matcher(sql).replaceAll(" "));
			vo.setReturnName(rtName);
			List<ProcedureResultVo> resultColumns = new ArrayList<>();
			List<ProcedureParamVo> queryConditions = new ArrayList<>();
			for (String para : paraList) {
				if(para.toLowerCase().startsWith("cursor")) {
					//如果参数名称是cursor打头，就说明是游标！！
					ProcedureResultVo paramVo = new ProcedureResultVo();
					paramVo.setName(para);
					paramVo.setLocation(ProcedureResultPlace.HEADLEFT);
					paramVo.setType(ProcedureResultType.CURSOR);
					paramVo.setTableColumns(new ArrayList<TableColumnVo>());
					resultColumns.add(paramVo);
				}else {
					//否则就是输入参数
					ProcedureParamVo paramVo = new ProcedureParamVo();
					paramVo.setName(para);
					paramVo.setTitle(para);
					paramVo.setColumnType(ColumnType.STRING);
					paramVo.setDefaultValue("");
					queryConditions.add(paramVo);
				}
			}
			vo.setQueryConditions(queryConditions);

			if (rtName != null) {
				// 说明是函数调用，返回值
				ProcedureResultVo paramVo = new ProcedureResultVo();
				paramVo.setName(rtName);
				paramVo.setLocation(ProcedureResultPlace.HEADLEFT);
				paramVo.setType(ProcedureResultType.DATASET);
				paramVo.setTableColumns(new ArrayList<TableColumnVo>());
				resultColumns.add(paramVo);
			}
			// 不做定义
			vo.setResultColumns(resultColumns);
			return vo;
		} else {
			log.warn("语句不符合规范：{}", sql);
			throw new IllegalArgumentException(
					"错误的存储过程/函数调用SQL。（函数示例-- :retrun = call func(:name,:age)\r\n存储过程示例-- call proc(:name,:return)）");
		}
	}

	@Override
	public UserDefineProcFunVo preQuery(UserDefineProcFunVo query, List<QueryParamVo> queryParamList) {
		// TODO Auto-generated method stub
		return null;
	}

}
