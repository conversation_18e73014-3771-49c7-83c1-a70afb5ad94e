package cn.trasen.homs.SQLToAPI.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
**********************************************   
* @Description: 存储过程、函数参数-视图对象
* @Author:huangkui  
* @Date: 2019年7月24日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Setter
@Getter
public class ProcedureParamVo {
	@ApiModelProperty("参数名称")
	private String name;
	
	@ApiModelProperty("参数标题")
	private String title;
	
	@ApiModelProperty("字段类型")
	private ColumnType columnType;
	
	@ApiModelProperty("下拉框配置ID")
	private String cfgid;
	
	@ApiModelProperty("下拉框配置名")
	private String cfgName;
	
	@ApiModelProperty("是否精确时间")
	private boolean dateTime = false;
	
	@ApiModelProperty("日期格式(1、日期，2、月份，3、年)")
	private Integer dateFormat = 1;
	
	@ApiModelProperty("时间是否限制到今天")
	private boolean dateTodayMax = true;
	
	@ApiModelProperty("列表数据接口地址")
	private String listDataUrl;
	
	@ApiModelProperty("列表数据值字段名")
	private String listDataValField;
	
	@ApiModelProperty("列表数据名称字段")
	private String listDataNameField;
	
	@ApiModelProperty("默认值")
	private String defaultValue = "";
	
	@ApiModelProperty("时间按钮值")
	private String dateButtonValue = "";
	
	@ApiModelProperty("是否为范围（此处用于时间）")
	private boolean range = false;
	
	private String id;

}
