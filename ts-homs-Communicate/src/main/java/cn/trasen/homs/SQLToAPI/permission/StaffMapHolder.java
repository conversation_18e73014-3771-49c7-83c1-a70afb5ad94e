package cn.trasen.homs.SQLToAPI.permission;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @ClassName StaffMapHolder
 * @Description StaffMapHolder
 * @date 2020年4月29日 下午12:13:24
 */
public class StaffMapHolder {

    private static final Map<String, String> STAFF_MAP = new ConcurrentHashMap<>();

    private static final Object LOCK = new Object();
    private volatile static StaffMapHolder staffMapHolder = null;

    private StaffMapHolder() {

    }

    public static StaffMapHolder getInstance() {
        if (staffMapHolder == null) {
            synchronized (LOCK) {
                if (staffMapHolder == null) {
                    staffMapHolder = new StaffMapHolder();
                }
            }
        }
        return staffMapHolder;
    }

    public void set(String sourceCode, String targetCode) {
        if (StringUtils.isNotBlank(sourceCode)) {
            STAFF_MAP.put(sourceCode, targetCode);
        }
    }

    public String get(String sourceCode) {
        if (StringUtils.isNotBlank(sourceCode)) {
            return STAFF_MAP.get(sourceCode);
        }
        return null;
    }
}
