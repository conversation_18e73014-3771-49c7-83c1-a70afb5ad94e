package cn.trasen.homs.SQLToAPI.permission;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@ConfigurationProperties(prefix = "data.permission")
@Data
@Accessors(chain = true)
@Component
public class MyPermissionConfig {

    //     @Value("${tableMap}")
    private Map<String, String> tableMap = new HashMap<String, String>();

    //    @Value("${tablePatternMap}")
    private Map<String, String> tablePatternMap = new HashMap<String, String>();

    //  能查看所有数据权限的角色代码  初使化 no_role_all
    private String roleAll = "no_role_all";

    //  能查看组织机构organization数据权限的角色代码 初使化 no_role_org
    private String roleOrg = "no_role_org";

    // 没认证的用户是否走所有数据权限开关
    private boolean noLoginUserCanVisitAllData = Boolean.FALSE;

}
