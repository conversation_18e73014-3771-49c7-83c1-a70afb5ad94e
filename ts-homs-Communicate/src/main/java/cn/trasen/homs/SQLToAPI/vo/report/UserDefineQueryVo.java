package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
**********************************************   
* @Description: 用户自定义查询-视图对象
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Getter
@Setter
public class UserDefineQueryVo extends UserDefineService{
	/**
	 * 
	 */
	private static final long serialVersionUID = -7546218465685384964L;
	
	@ApiModelProperty(value = "数据源名称")
    private String dsName;
	@ApiModelProperty(value = "数据源类型")
	private Integer dsType;
	
	@ApiModelProperty(value = "保存的条件(用于编辑)")
	private List<QueryConditionVo> savedConditions;
	
	@ApiModelProperty("字段定义")
	private List<TableColumnVo> tableColumns;
	@ApiModelProperty("查询条件")
	private List<QueryConditionVo> queryConditions;
	@ApiModelProperty("查询结果")
	private List<Map<String,Object>> queryResult;
	
	@ApiModelProperty("时间初始值")
	private List<DateInit> dateInitValues;
	
	@Setter
	@Getter
	@AllArgsConstructor
	public static class DateInit{
		private String name,value;
	}
}
