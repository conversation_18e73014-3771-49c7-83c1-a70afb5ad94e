package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
**********************************************   
* @Description: 用户自定义表字段视图对象
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
public class TableColumnVo extends TableTitleVo<TableColumnVo>{
	
	@ApiModelProperty("是否隐藏")
	private Boolean hidden;
	
	@ApiModelProperty("字段位置")
	private Integer index = 0;	
	
	@ApiModelProperty("字段类型")
	private ColumnType type = ColumnType.STRING;	
	
	@ApiModelProperty("宽度")
	private Integer width;
	
	@ApiModelProperty("对齐方式")
	private ColumnAlign align = ColumnAlign.center;
	
	@ApiModelProperty("时间格式(例如yyyy-MM-dd HH:mm:ss)")
	private String dateFormat = "";
	
	@ApiModelProperty("数字是否为百分比")
	private boolean numPercent = false;
	
	@ApiModelProperty("是否排序")
	private boolean sort = true;	
	
	@ApiModelProperty("所占列数")
	private Integer colspan = 1;
	
	@ApiModelProperty("是否为合并列")
	private boolean combine = false;
	
	@ApiModelProperty("是否列悬浮固定")
	private boolean frost = false;
	
	@ApiModelProperty("子查询ID")
	private String subQueryId;
	
	@ApiModelProperty("子查询类型：0-查询，1-存储过程")
	private Integer subQueryType=0;
	
	@ApiModelProperty("子查询名称")
	private String subQueryName;
	
	@ApiModelProperty("子查询参数名")
	private String subQueryParam;
	
}
