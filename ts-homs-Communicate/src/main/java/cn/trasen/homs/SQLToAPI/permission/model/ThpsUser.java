package cn.trasen.homs.SQLToAPI.permission.model;

import java.io.Serializable;

import lombok.Data;

/**
 * @ClassName ThpsUser
 * @Description ThpsUser
 * @date 2020年4月27日 下午9:58:21
 * <AUTHOR>
 */
@Data
public class ThpsUser implements Serializable {
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = -144679110375085670L;

	private String id;
	private String corpcode;
	private String usercode;
	private String username;
	private String deptcode;
	private String pdcode;
	private String deptname;

	private String orgRang;

	private String corpList;

	private String sysRoleCode;

	private String hospCode;

	private String hospName;

	private String orgName;

}