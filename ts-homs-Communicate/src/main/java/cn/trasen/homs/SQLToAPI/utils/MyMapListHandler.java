package cn.trasen.homs.SQLToAPI.utils;

import java.nio.charset.Charset;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

import org.apache.commons.dbutils.handlers.MapListHandler;

import com.alibaba.fastjson.JSONObject;

public class MyMapListHandler extends MapListHandler {
	
	private int maxSize;
	private double sizeSum = 0.0;
	private int rowCount = 0;
	private Charset cs = Charset.forName("UTF-8");
	
	public MyMapListHandler(int maxSize) {
		super();
		this.maxSize = maxSize;
	}
	

	@Override
	protected Map<String, Object> handleRow(ResultSet rs) throws SQLException {	
		Map<String, Object> map = super.handleRow(rs);
		if(map == null) {
			return map;
		}
		rowCount++;
		if(rowCount%31 == 0) {
			int size = JSONObject.toJSONString(map).getBytes(cs).length;
			double rowSize = size /1024.0/1024;
			sizeSum += rowSize*31;
			if(sizeSum > maxSize) {
				throw new SQLException("查询的数据量已经超过了系统允许的最大数据量："+maxSize+"兆");
			}
		}
		return map;
	}
	
}
