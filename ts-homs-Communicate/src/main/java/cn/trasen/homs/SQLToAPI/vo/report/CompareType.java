package cn.trasen.homs.SQLToAPI.vo.report;

/**
**********************************************   
* @Description: 条件比较类型枚举
* @Author:huangkui  
* @Date: 2019年6月10日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public enum CompareType {
	
	EQ("=","等于"),GT(">","大于"),LT("<","小于"),NEQ("!=","不等于"),GTE(">=","大于等于"),LTE("<=","小于等于");
	
	private String op,desc;
	private CompareType(String op,String desc) {
		this.op = op;
		this.desc = desc;
	}
	public String getOp() {
		return op;
	}
	public String getDesc() {
		return desc;
	}
	
}
