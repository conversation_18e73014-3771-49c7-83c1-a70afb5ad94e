package cn.trasen.homs.SQLToAPI.permission.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName OrgMapProperties
 * @Description OrgMapProperties
 * @date 2020年4月29日 上午11:24:39
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "permission.staff-map")
public class StaffMapProperties {

    /**
     * URL
     */
    private String url = "";

    /**
     * 是否启用
     */
    private boolean enabled = false;
}
