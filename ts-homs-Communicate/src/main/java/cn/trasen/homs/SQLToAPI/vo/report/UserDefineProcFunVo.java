package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
**********************************************   
* @Description: 用户自定义存储过程、函数视图对象
* @Author:huangkui  
* @Date: 2019年7月24日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Getter
@Setter
public class UserDefineProcFunVo extends UserDefineService{
	/**
	 * 
	 */
	private static final long serialVersionUID = -7546218465685384964L;
	
	@ApiModelProperty(value = "数据源名称")
    private String dsName;
	@ApiModelProperty(value = "数据源类型")
	private Integer dsType;
	
	@ApiModelProperty(value = "输入参数")
	private List<ProcedureParamVo> queryConditions;
	
	@ApiModelProperty(value = "保存的条件(用于编辑)")
	private List<ProcedureParamVo> savedConditions;
	
	
	@ApiModelProperty("返回结果定义")
	private List<ProcedureResultVo> resultColumns;
	
	@ApiModelProperty("查询结果")
	private List<ProcFunResultVo> queryResult;
	
	@ApiModelProperty("返回名称")
	private String returnName;
	
}
