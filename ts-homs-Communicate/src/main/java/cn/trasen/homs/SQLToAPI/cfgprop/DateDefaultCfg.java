package cn.trasen.homs.SQLToAPI.cfgprop;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.trasen.homs.SQLToAPI.utils.DateUtils;
import lombok.Data;

/**
**********************************************   
* @Description: 日期默认值自定义设置
* @Author:huangkui  
* @Date: 2019年8月1日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "date")
public class DateDefaultCfg {

	private List<DateCfg> configs = new ArrayList<>();

	@Data
	public static class DateCfg {
		private String code;
		private DateEnum set;
		private Integer add;
		private AddUnit unit;
	}

	public static enum DateEnum {
		TODAY,TODAYEND, WEEKBEGIN, WEEKEND, MONTHBEGIN, MONTHEND, QUARTERBEGIN, QUARTEREND, YEARBEGIN, YEAREND;
	}

	public static enum AddUnit {
		DAY, WEEK, MONTH, QUARTER, YEAR;
	}

	public DateCfg getByCode(String code) {
		return configs.stream().filter(c -> c.getCode().equalsIgnoreCase(code)).findFirst().orElse(null);
	}

	public Date getDateByConfig(DateCfg cfg) {
		Date date = new Date();
		switch (cfg.set) {
			case TODAY:	
				date = DateUtils.dateBegin(date);
				break;
			case TODAYEND:	
				date = DateUtils.dateEnd(date);
				break;
			case WEEKBEGIN:
				date = DateUtils.weekBegin(date);
				break;
			case WEEKEND:
				date = DateUtils.weekEnd(date);
				break;
			case MONTHBEGIN:
				date = DateUtils.monthBegin(date);
				break;
			case MONTHEND:
				date = DateUtils.monthEnd(date);
				break;
			case QUARTERBEGIN:
				date = DateUtils.quarterBegin(date);
				break;
			case QUARTEREND:
				date = DateUtils.quarterEnd(date);
				break;
			case YEARBEGIN:
				date = DateUtils.yearBegin(date);
				break;
			case YEAREND:
				date = DateUtils.yearEnd(date);
				break;
			default:
				break;
		}
		if(cfg.add != 0) {
			switch (cfg.unit) {
			case DAY:
				date = DateUtils.dateAdd(date, cfg.add);
				break;
			case WEEK:
				date = DateUtils.dateAdd(date, cfg.add*7);
				break;
			case MONTH:
				date = DateUtils.monthAdd(date, cfg.add);
				break;
			case QUARTER:
				date = DateUtils.monthAdd(date, cfg.add*3);
				break;
			case YEAR:
				date = DateUtils.yearAdd(date, cfg.add);
				break;
			default:
				break;
			}
		}
		return date;
	}

}
