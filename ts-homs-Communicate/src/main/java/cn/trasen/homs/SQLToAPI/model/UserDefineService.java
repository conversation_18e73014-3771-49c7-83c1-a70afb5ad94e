package cn.trasen.homs.SQLToAPI.model;

import javax.persistence.Column;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "COMM_CUST_SERVICE")
@Setter
@Getter
public class UserDefineService extends DataEntity<String>{
    /**
	 * 
	 */
	private static final long serialVersionUID = 4652904171485265624L;

	/**
     * 查询名称
     */
    @Column(name = "QUERY_NAME")
    @ApiModelProperty(value = "查询名称")
    private String queryName;

    /**
     * 数据源ID
     */
    @Column(name = "DS_ID")
    @ApiModelProperty(value = "数据源ID")
    private String dsId;
    /**
     * 查询SQL
     */
    @Column(name = "QUERY_SQL")
    @ApiModelProperty(value = "查询SQL")
    private String querySql;

    
    @Column(name = "PAGE")
    @ApiModelProperty(value = "是否分页：1-分页，0-不分页")
    private Integer page = 0;
    
    @Column(name = "PAGE_SIZE")
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;
    
    @Column(name = "SERVICE_TYPE")
    @ApiModelProperty(value = "服务类型：1、SQL查询，2、存储过程、函数")
    private Integer serviceType;
    
    @Column(name = "CATEGORY_NAME")
    @ApiModelProperty(value = "分类名称")
    private String categoryName;
    
    @Column(name = "CATEGORY_CODE")
    @ApiModelProperty(value = "分类代码")
    private String categoryCode;
    
    @Column(name = "NEED_COUNT")
    @ApiModelProperty(value = "是否需要合计（0：不需要，1：需要）")
    private Integer needCount;
    
    @Column(name = "TOPIC_CATE_NAME")
    @ApiModelProperty(value = "主题分类名称")
    private String topicCateName;  
    
    @Column(name = "TOPIC_CATE_CODE")
    @ApiModelProperty(value = "主题分类代码")
    private String topicCateCode;   
    
    /*
     * 字段定义（json）
     */
    @Column(name = "COLUMN_DEFINE")
    @ApiModelProperty(value = "字段定义json字符串")
    private String columnDefine;    
    
    /**
     * 条件定义（json）
     */
    @Column(name = "CONDITION_DEFINE")
    @ApiModelProperty(value = "条件定义json字符串")
    private String conditionDefine; 
    
    /**
     * 图表配置定义（json）
     */
    @Column(name = "FORM_DATA")
    @ApiModelProperty(value = "图表定义json字符串")
    private String formData; 
    
    /**
     * 说明
     */
    @Column(name = "QUERY_DESCRIPTION")
    @ApiModelProperty(value = "说明")
    private String queryDescription;
    
    
    @Column(name = "MODIFY_FLAG")
    @ApiModelProperty(value = "修改标记（0：不允许修改，1：允许修改）")
    private Integer modifyFlag = 0;

    /**
     * 系统服务，01-是,02-否
     */
    @ApiModelProperty(value = "系统服务：01-是,02-否")
    @Column(name = "SYSTEM_SERVICE")
//    @ExcelColumn(value = "SYSTEM_SERVICE", col = 13)
    private String systemService;
    @Column(name="SERVICE_URL")
    @ApiModelProperty(value = "服务接口访问地址")
    private String serviceUrl;
}