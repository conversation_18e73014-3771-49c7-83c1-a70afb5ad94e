package cn.trasen.homs.SQLToAPI.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.reflect.FieldUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import cn.trasen.homs.SQLToAPI.vo.DataSetVo;
import cn.trasen.homs.core.utils.DateUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName XmlUtils xml相关工具类
 * @Description TODO
 * @date 2020年04月26日  下午 1:46
 */
public class XmlUtils {

    public static void main(String[] args) throws Exception {
      /*  String jsonStr = "{'pageNo':1,'pageSize':3,'pageCount':1,'totalCount':3,'rows':[{'USERCODE':'admin','name':'张三','menuid':null,'DEPT':'0001','value':'9','url':'#','funtype':null},{'USERCODE':'admin','name':'李四','menuid':null,'DEPT':'0002','value':'2','url':'#','funtype':null},{'USERCODE':'admin2','name':'王五','menuid':null,'DEPT':'0003','value':'1','url':'#','funtype':null}],'success':true,'errMsg':null}";
        DataSetVo<Map<String, Object>> dls = JSON.parseObject(jsonStr, new TypeReference<DataSetVo<Map<String, Object>>>() {});
        Map<String,String> alias = new HashMap<String,String>();
        alias.put("rows","row");
        XmlUtils.beanToXml(DataSetVo.class,dls,"result",alias);*/



    }


    /**
     * 将bean转换成xml
     * @param clazz 最外层类class
     * @param obj  待转换的bean对象
     * @param rootName 根节点名称
     * @param complexTypeNodeNameMap  复杂类型节点名称映射
     * @return xml格式字符串
     */
    public static String beanToXml(Class clazz,Object obj,String rootName,Map<String,String> complexTypeNodeNameMap){
        HashMap<String, Object> map = new HashMap<>();
        Class<DataSetVo> dataSetVoClass = DataSetVo.class;
        Field[] fields = FieldUtils.getAllFields(dataSetVoClass);
        for (Field field:fields) {
            if(!complexTypeNodeNameMap.containsKey(field.getName())){
                Method method = null;
                String first = null;
                String other = null;
                try{
                    String fieldName = field.getName();
                    first = fieldName.substring(0, 1).toUpperCase();
                    other = fieldName.substring(1);
                    method = dataSetVoClass.getMethod("get" + first+other);
                }catch (NoSuchMethodException e1){
                    try{
                        method = dataSetVoClass.getMethod("is" + first+other);
                    }catch (Exception e){
                        e.printStackTrace();
                    }

                }catch (Exception e2){
                    e2.printStackTrace();
                }finally {
                    try{
                        Object retureObject = method.invoke(obj)==null?"":method.invoke(obj);
                        if(retureObject instanceof Date){
                            map.put(field.getName(),DateUtil.format(retureObject, DateUtil.DATE_PATTERN.YYYY_MM_DD_HH_MM_SS));
                        } else{
                            map.put(field.getName(),retureObject.toString());
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }
        String xml = XmlUtil.parseDto2Xml(map, rootName);
        Document document =null;
        try{
            document = DocumentHelper.parseText(xml);
            Element rootElement = document.getRootElement();
            for (Map.Entry entry:complexTypeNodeNameMap.entrySet()) {
                String rowsName = entry.getKey().toString();
                String rowName = entry.getValue().toString();
                String first = rowsName.substring(0, 1).toUpperCase();
                String other = rowsName.substring(1);
                Method method = dataSetVoClass.getMethod("get" + first + other);
                Object rows = method.invoke(obj);
                Element rowsElement = null;
                if(rows instanceof List){
                    rowsElement = XmlUtils.parseList2XmlBasedNode((List) method.invoke(obj), rowsName, rowName);
                }else{
                    Map<String, String> resultMap = beanToMap(method.invoke(obj), null);
                    rowsElement = XmlUtils.parseDto2Xml(resultMap, rowsName);
                }
                rootElement.add(rowsElement);
            }


        }catch (Exception e){
            e.printStackTrace();
        }
        String documentStr = document.asXML();
        System.out.println(documentStr);
        return documentStr;
    }


    public static final Element parseDto2Xml(Map map, String rootName) {
        Document document = DocumentHelper.createDocument();
        // 增加一个根元素节点
        document.addElement(rootName);
        Element root = document.getRootElement();
        Iterator keyIterator = map.keySet().iterator();
        while (keyIterator.hasNext()) {
            String key = (String) keyIterator.next();
            String value = (String) map.get(key);
            Element leaf = root.addElement(key);
            leaf.setText(value);
        }

        return root;
    }

    public static Map<String,String> beanToMap(Object obj,Map<String,String> complexTypeNodeNameMap){
        HashMap<String, String> resultMap = new HashMap<>();
        complexTypeNodeNameMap = complexTypeNodeNameMap==null?new HashMap<String,String>():complexTypeNodeNameMap;
        Class<?> clazz = obj.getClass();
        Field[] fields = FieldUtils.getAllFields(clazz);
        for (Field field:fields) {
            if(!complexTypeNodeNameMap.containsKey(field.getName())){
                Method method = null;
                String first = null;
                String other = null;
                try{
                    String fieldName = field.getName();
                    first = fieldName.substring(0, 1).toUpperCase();
                    other = fieldName.substring(1);
                    method = clazz.getMethod("get" + first+other);
                }catch (NoSuchMethodException e1){
                    try{
                        method = clazz.getMethod("is" + first+other);
                    }catch (Exception e){
                        e.printStackTrace();
                    }

                }catch (Exception e2){
                    e2.printStackTrace();
                }finally {
                    try{
                        Object retureObject = method.invoke(obj)==null?"":method.invoke(obj);
                        if(retureObject instanceof Date){
                            resultMap.put(field.getName(),DateUtil.format(retureObject, DateUtil.DATE_PATTERN.YYYY_MM_DD_HH_MM_SS));
                        } else{
                            resultMap.put(field.getName(),retureObject.toString());
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }

        return resultMap;
    }






    public static final Element parseList2XmlBasedNode(List pList, String pRootNodeName, String pFirstNodeName) {
        Document document = DocumentHelper.createDocument();
        Element output = document.addElement(pRootNodeName);
        for (int i = 0; i < pList.size(); i++) {
            Map map = (Map) pList.get(i);
            Element elRow = output.addElement(pFirstNodeName);
            Iterator it = map.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry entry = (Map.Entry) it.next();
                Element leaf = elRow.addElement((String) entry.getKey());
                leaf.setText(String.valueOf(entry.getValue()));
            }
        }
        return output;
    }

}
