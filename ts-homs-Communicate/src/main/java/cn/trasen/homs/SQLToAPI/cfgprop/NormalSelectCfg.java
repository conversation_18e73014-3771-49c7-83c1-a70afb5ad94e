package cn.trasen.homs.SQLToAPI.cfgprop;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
**********************************************   
* @Description: 下拉选框配置
* @Author:huangkui  
* @Date: 2019年8月13日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "select")
public class NormalSelectCfg {
	
	private List<SelectCfg> configs = new ArrayList<>();
	
	public SelectCfg loadByCode(String code) {
		return configs.stream().filter(c -> c.getCode().equalsIgnoreCase(code)).findFirst().orElse(null);
	}
	
	@Data
	public static class SelectCfg{
		private String code;
		private String dsId;
		private String table;
		private String nameColumn;
		private String valueColumn;
		private String where;
		private String paraWhere;
	}

}
