package cn.trasen.homs.SQLToAPI.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;

public class RequestBodyUtils {
	public static byte[] readBody(HttpServletRequest req) {
		try {
			ServletInputStream reader = req.getInputStream();
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			byte[] bs = new byte[4096];
			int len = reader.read(bs);
			while (len > 0) {
				bos.write(bs, 0, len);
				len = reader.read(bs);
			}

			if (bos.size() > 0) {
				return bos.toByteArray();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return  null;
	}
}
