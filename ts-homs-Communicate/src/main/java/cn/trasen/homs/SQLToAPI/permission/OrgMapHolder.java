package cn.trasen.homs.SQLToAPI.permission;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @ClassName OrgMapHolder
 * @Description OrgMapHolder
 * @date 2020年4月29日 下午12:13:24
 */
public class OrgMapHolder {

    private static final Map<String, String> ORG_MAP = new ConcurrentHashMap<>();

    private static final Object LOCK = new Object();
    private volatile static OrgMapHolder orgMapHolder = null;

    private OrgMapHolder() {

    }

    public static OrgMapHolder getInstance() {
        if (orgMapHolder == null) {
            synchronized (LOCK) {
                if (orgMapHolder == null) {
                    orgMapHolder = new OrgMapHolder();
                }
            }
        }
        return orgMapHolder;
    }

    public void set(String sourceCode, String targetCode) {
        if (StringUtils.isNotBlank(sourceCode)) {
            ORG_MAP.put(sourceCode, targetCode);
        }
    }

    public String get(String sourceCode) {
        if (StringUtils.isNotBlank(sourceCode)) {
            return ORG_MAP.get(sourceCode);
        }
        return null;
    }
}
