package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
**********************************************   
* @Description: 存储过程、函数查询结果对象
* @Author:huangkui  
* @Date: 2019年7月25日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
public class ProcFunResultVo {
	@ApiModelProperty("结果名称")
	private String name;
	
	@ApiModelProperty("结果内容")
	private List<Map<String,Object>> result;
}
