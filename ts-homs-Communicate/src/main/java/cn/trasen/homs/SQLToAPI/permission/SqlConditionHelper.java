package cn.trasen.homs.SQLToAPI.permission;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.util.StringUtils;

import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOperator;
import com.alibaba.druid.sql.ast.expr.SQLCharExpr;
import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.druid.sql.ast.expr.SQLInListExpr;
import com.alibaba.druid.sql.ast.expr.SQLPropertyExpr;
import com.alibaba.druid.sql.ast.expr.SQLQueryExpr;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelect;
import com.alibaba.druid.sql.ast.statement.SQLSelectQuery;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.druid.sql.ast.statement.SQLTableSource;
import com.alibaba.druid.sql.ast.statement.SQLUnionQuery;
import com.alibaba.druid.sql.ast.statement.SQLUnionQueryTableSource;
import com.alibaba.druid.sql.ast.statement.SQLWithSubqueryClause;
import com.alibaba.fastjson.JSONObject;

import cn.trasen.homs.SQLToAPI.permission.model.DataScopeModel;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName SqlConditionHelper
 * @Description SqlConditionHelper
 * @date 2020年4月26日 下午9:33:50
 */
@Slf4j
public class SqlConditionHelper {

    private final ITableFieldConditionDecision conditionDecision;

    public SqlConditionHelper(ITableFieldConditionDecision conditionDecision) {
        this.conditionDecision = conditionDecision;
    }


    public void addStatementCondition(SQLStatement sqlStatement) {
        // 只考虑查询语句
        if (sqlStatement instanceof SQLSelectStatement) {
            SQLSelect sqlSelect = ((SQLSelectStatement) sqlStatement).getSelect();

            // 处理withSubQuery
            if(null != sqlSelect.getWithSubQuery()){
                List<SQLWithSubqueryClause.Entry> entryList = sqlSelect.getWithSubQuery().getEntries();
                if(!entryList.isEmpty()){
                    SQLSelectQueryBlock queryObject2 ;
                    SQLSelect subSQLSelect;
                    for(int i=0; i<entryList.size(); i++){
                    	subSQLSelect = entryList.get(i).getSubQuery();
                    	// union all查询
                    	if(subSQLSelect.getQuery() instanceof SQLUnionQuery) {
                             processSQLUnionQuery((SQLUnionQuery) subSQLSelect.getQuery());
                    	} else {
                    		queryObject2 = subSQLSelect.getQueryBlock();
                            if(queryObject2 != null) {
                            	addSelectStatementCondition(queryObject2,queryObject2.getFrom(),true) ;
                            }
                    	}
                    }
                }
            }

            if (sqlSelect.getQuery() instanceof SQLUnionQuery) {
                SQLUnionQuery sqlUnionQuery = (SQLUnionQuery) sqlSelect.getQuery();
                processSQLUnionQuery(sqlUnionQuery);
            } else {
                SQLSelectQueryBlock queryObject = sqlSelect.getQueryBlock();
                addSelectStatementCondition(queryObject, queryObject.getFrom(), true);
            }

            // handleWhere(sqlSelect.getQueryBlock().getWhere(), fieldName);
        }
    }

    private void addSelectStatementCondition(SQLSelectQueryBlock queryObject, SQLTableSource from, boolean doWhere) {

        if (from == null || queryObject == null) {
            return;
        }

        SQLExpr originCondition = queryObject.getWhere();
        if (from instanceof SQLExprTableSource) {
            SQLExpr sqlExpr = ((SQLExprTableSource) from).getExpr();
            if (sqlExpr instanceof SQLIdentifierExpr) {
                String tableName = ((SQLIdentifierExpr) sqlExpr).getName();
                String alias = from.getAlias();
                SQLExpr newCondition = newEqualityCondition(tableName, alias, originCondition);
                if (newCondition != null) {
                    // queryObject.replace(originCondition,newCondition);
                    // queryObject.setWhere(newCondition);
                    queryObject.addWhere(newCondition);
                }
            } else if (sqlExpr instanceof SQLPropertyExpr) {
                String tableName = ((SQLPropertyExpr) sqlExpr).getName();
                String alias = from.getAlias();
                SQLExpr newCondition = newEqualityCondition(tableName, alias, originCondition);
                if (newCondition != null) {
                    queryObject.addWhere(newCondition);
                }
            }
        } else if (from instanceof SQLJoinTableSource) {
            SQLJoinTableSource joinObject = (SQLJoinTableSource) from;
            SQLTableSource left = joinObject.getLeft();
            SQLTableSource right = joinObject.getRight();

            addSelectStatementCondition(queryObject, left, false);
            addSelectStatementCondition(queryObject, right, false);

        } else if (from instanceof SQLSubqueryTableSource) {
            SQLSelect subSelectObject = ((SQLSubqueryTableSource) from).getSelect();

            if (subSelectObject.getQuery() instanceof SQLUnionQuery) {
                processSQLUnionQuery((SQLUnionQuery) subSelectObject.getQuery());
            } else {
                SQLSelectQueryBlock subQueryObject = (SQLSelectQueryBlock) subSelectObject.getQuery();
                addSelectStatementCondition(subQueryObject, subQueryObject.getFrom(), true);
            }
        } else if (from instanceof SQLUnionQueryTableSource) {
            doWhere = false;
            SQLUnionQuery uoinObject = ((SQLUnionQueryTableSource) from).getUnion();

            // right
            SQLSelectQuery rightSQLSelectQuery = uoinObject.getRight();
            if (rightSQLSelectQuery instanceof SQLUnionQuery) {
                SQLUnionQuery righSql = (SQLUnionQuery) rightSQLSelectQuery;
                processSQLUnionQuery(righSql);
            } else {
                SQLSelectQueryBlock right = (SQLSelectQueryBlock) uoinObject.getRight();
                addSelectStatementCondition(right, right.getFrom(), true);
            }

            // left
            SQLSelectQueryBlock left = (SQLSelectQueryBlock) uoinObject.getLeft();
            addSelectStatementCondition(left, left.getFrom(), true);

        } else {
            throw new RuntimeException("未处理的异常");
        }

        if (doWhere) {
            handleWhere(originCondition);
        }
    }

    private void handleWhere(SQLExpr where) {
        if (null == where) {
            return;
        }

        if (!(where instanceof SQLBinaryOpExpr)) {
            return;
        }

        SQLExpr left = ((SQLBinaryOpExpr) where).getLeft();
        if (left instanceof SQLBinaryOpExpr) {
            handleWhere(left);
        }

        SQLExpr right = ((SQLBinaryOpExpr) where).getRight();
        if (right instanceof SQLBinaryOpExpr) {
            handleWhere(right);
        } else if (right instanceof SQLQueryExpr) {
            if (((SQLQueryExpr) right).subQuery.getQuery() instanceof SQLSelectQueryBlock) {
                SQLSelectQueryBlock queryObject = ((SQLQueryExpr) right).subQuery.getQueryBlock();
                addSelectStatementCondition(queryObject, queryObject.getFrom(), true);
            } else if (((SQLQueryExpr) right).subQuery.getQuery() instanceof SQLUnionQuery) {
                SQLUnionQuery uoinObject = (SQLUnionQuery) ((SQLQueryExpr) right).subQuery.getQuery();
                // right
                SQLSelectQuery rightSQLSelectQuery = uoinObject.getRight();
                if (rightSQLSelectQuery instanceof SQLUnionQuery) {
                    SQLUnionQuery righSql = (SQLUnionQuery) rightSQLSelectQuery;
                    processSQLUnionQuery(righSql);
                } else {
                    SQLSelectQueryBlock right_2 = (SQLSelectQueryBlock) uoinObject.getRight();
                    addSelectStatementCondition(right_2, right_2.getFrom(), true);
                }

                // left
                SQLSelectQueryBlock left_2 = (SQLSelectQueryBlock) uoinObject.getLeft();
                addSelectStatementCondition(left_2, left_2.getFrom(), true);

            }
        }
    }

    private void processSQLUnionQuery(SQLUnionQuery sqlUnionQuery) {
        if (sqlUnionQuery.getRight() instanceof SQLUnionQuery) {
            processSQLUnionQuery((SQLUnionQuery) sqlUnionQuery.getRight());
        } else {
            SQLSelectQueryBlock right = (SQLSelectQueryBlock) sqlUnionQuery.getRight();
            addSelectStatementCondition(right, right.getFrom(), true);
        }
        if(sqlUnionQuery.getLeft() instanceof SQLUnionQuery) {
            processSQLUnionQuery((SQLUnionQuery) sqlUnionQuery.getLeft());
        } else {
        	SQLSelectQueryBlock left = (SQLSelectQueryBlock) sqlUnionQuery.getLeft();
            addSelectStatementCondition(left, left.getFrom(), true);
        }
    }

    private SQLExpr newEqualityCondition(String tableName, String tableAlias, SQLExpr originCondition) {
        Map<String, String> fieldNameMap = conditionDecision.adjudge(tableName);
        if (null == fieldNameMap || fieldNameMap.size() < 1) {
            return null;
        }
        log.debug("==================>" + tableName);
//		if (!tableName.equalsIgnoreCase("tc")) {
//			return null;
//		}
        DataScopeModel dataScopeModel = DataScopeContextHolder.get();
        //走个人数据
        if (dataScopeModel != null && dataScopeModel.isHasOnlySelfData() && (!StringUtils.isEmpty(dataScopeModel.getStaffCode())) && !dataScopeModel.isHasAllData()) {

            String fieldName = fieldNameMap.get(PermissionUtil.staffFiled);
            if (StringUtils.isEmpty(fieldName)) {
                return null;
            }
            String filedName = StringUtils.isEmpty(tableAlias) ? fieldName : tableAlias + "." + fieldName;

            SQLBinaryOpExpr condition = new SQLBinaryOpExpr(
                    new SQLIdentifierExpr(filedName),
                    new SQLCharExpr(dataScopeModel.getStaffCode()),
                    SQLBinaryOperator.Equality);
            log.debug("=======走个人数据 加条件=={}=={}====StaffCode===>{}",tableName,filedName, dataScopeModel.getStaffCode());
            return condition;
        }


        // 走科室数据
        if (dataScopeModel != null && (dataScopeModel.getDeptList() != null && dataScopeModel.getDeptList().size() > 0) && !dataScopeModel.isHasAllData()) {

            String fieldName = fieldNameMap.get(PermissionUtil.depFiled);
            if (StringUtils.isEmpty(fieldName)) {
                return null;
            }

            String filedName = StringUtils.isEmpty(tableAlias) ? fieldName : tableAlias + "." + fieldName;
            SQLInListExpr condition = new SQLInListExpr();
            condition.setExpr(new SQLIdentifierExpr(filedName));
            List<SQLExpr> list = new ArrayList<>();
            Set<String> deptList = dataScopeModel.getDeptList();
            if (deptList != null && deptList.size() > 0) {
                deptList.stream().forEach(item -> {
                    list.add(new SQLCharExpr(item));
                });
            }
            log.debug("=======走科室数据 加条件=={}=={}=======>{}",tableName,filedName, JSONObject.toJSONString(deptList));
            condition.setTargetList(list);
//            return SQLUtils.buildCondition(SQLBinaryOperator.BooleanAnd, condition, false, originCondition);
            return condition;
        }
        return null;
    }

}
