package cn.trasen.homs.SQLToAPI.permission.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName DataScopeModel
 * @Description DataScopeModel
 * @date 2020年4月27日 下午10:17:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataScopeModel implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 权限范围
     */
    private String dataScope;

    /**
     * 机构列表
     */
    private Set<String> deptList;

    /**
     * 所有数据权限标志
     */
    private boolean hasAllData = Boolean.FALSE;

    /**
     * 个人数据权限标志
     */
    private boolean hasOnlySelfData = Boolean.FALSE;

    /**
     * 个人数据权限员工代码(医生代码)
     */
    private String staffCode;

    /**
     *  在有数据权限控制的表上是否有访问数据的权限
     */
    private boolean hasVisitData = Boolean.TRUE;
}
