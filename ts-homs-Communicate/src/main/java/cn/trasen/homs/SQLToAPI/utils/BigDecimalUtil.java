package cn.trasen.homs.SQLToAPI.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
**********************************************   
* @Description: 大数工具类
* @Author:huangkui  
* @Date: 2019年6月14日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public class BigDecimalUtil {
	public static BigDecimal divide(BigDecimal dividend, BigDecimal divisor, int scale) {
		if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		} else if (dividend == null || dividend.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		} else {
			return dividend.divide(divisor, scale, RoundingMode.HALF_UP);
		}
	}

	public static BigDecimal add(BigDecimal... bs) {
		BigDecimal data = BigDecimal.ZERO;
		for (BigDecimal b : bs) {
			if (b != null) {
				data = data.add(b);
			}
		}
		return data;
	}

	private static final BigDecimal b100 = new BigDecimal(100);
	
	public static BigDecimal percent(BigDecimal dividend, BigDecimal divisor) {
		return divide(dividend, divisor, 4).multiply(b100);
	}

	public static BigDecimal fromObject(Object object) {
		return object == null? BigDecimal.ZERO:new BigDecimal(object.toString());
	}

	public static BigDecimal fromMap(Map<String, Object> row, String key) {
		if(row == null) {
			return BigDecimal.ZERO;
		}
		Object object = row.get(key);
		return object == null? BigDecimal.ZERO:new BigDecimal(object.toString());
	}
}
