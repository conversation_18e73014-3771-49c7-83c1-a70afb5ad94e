package cn.trasen.homs.SQLToAPI.vo.report;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
**********************************************   
* @Description: 存储过程、函数返回结果视图对象
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
@Data
public class ProcedureResultVo {
	
	@ApiModelProperty("结果名称")
	private String name;
	
	@ApiModelProperty("结果标题")
	private String title;
	
	@ApiModelProperty("结果类型")
	private ProcedureResultType type;
	
	@ApiModelProperty("结果位置")
	private ProcedureResultPlace location;
	
	@ApiModelProperty("字段定义（如果是单个结果就只有一个定义）")
	private List<TableColumnVo> tableColumns;
	
	@ApiModelProperty("图表设置（客户端配置json）")
	private String formData = "[]";
	
	
}
