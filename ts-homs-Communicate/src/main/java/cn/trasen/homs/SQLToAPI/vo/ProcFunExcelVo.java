package cn.trasen.homs.SQLToAPI.vo;

import java.util.List;

import cn.trasen.homs.SQLToAPI.vo.report.ExportTitleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
**********************************************   
 * @Description: 存储过程导出参数
 * @Author:huangkui  
 * @Date: 2019年12月5日 下午4:25:37
 * @Copyright: Copyright (c)  2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
***********************************************
 */
@Data
public class ProcFunExcelVo {
	@ApiModelProperty("下载文件名")
	private String fileName;
	@ApiModelProperty("自定义存储过程ID")
	private String id;
	@ApiModelProperty("参数字符串")
	private String parameters;
	@ApiModelProperty("表头定义")
	private List<ExportTitleVo> titles; 
}
