package cn.trasen.homs.SQLToAPI.vo.report;

/**
**********************************************   
* @Description: 存储过程、函数结果类型
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public enum ProcedureResultType {
	STRING(0, "文字"), NUMBER(1, "数字"), DATE(2, "日期字符"), DATASET(3,"结果集（表格）"), CURSOR(4, "游标（表格）");

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public Integer getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(Integer typeCode) {
		this.typeCode = typeCode;
	}

	private String typeName;
	private Integer typeCode;

	private ProcedureResultType(Integer code, String name) {
		this.typeName = name;
		this.typeCode = code;
	}
	
	public static ProcedureResultType fromCode(Integer code) {
		if(code == 0) {
			return STRING;
		}else if(code == 1) {
			return NUMBER;
		}else if(code == 2) {
			return DATE;
		}else if(code == 4) {
			return CURSOR;
		}
		return null;
	}
}
