package cn.trasen.homs.SQLToAPI.controller;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

//import cn.trasen.bi.bo.zbk.OfBusiCodeBo;
//import cn.trasen.bi.service.OfBusiCodeService;
import cn.trasen.homs.SQLToAPI.QueryException;
import cn.trasen.homs.SQLToAPI.cfgprop.DateDefaultCfg;
import cn.trasen.homs.SQLToAPI.service.UserDataSourceService;
import cn.trasen.homs.SQLToAPI.service.UserDefineQueryTableService;
import cn.trasen.homs.SQLToAPI.utils.CommonSqlUtil;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil;
import cn.trasen.homs.SQLToAPI.utils.ReportUtil.TdNode;
import cn.trasen.homs.SQLToAPI.utils.RequestBodyUtils;
import cn.trasen.homs.SQLToAPI.utils.XmlUtils;
import cn.trasen.homs.SQLToAPI.vo.DataSetVo;
import cn.trasen.homs.SQLToAPI.vo.DataSourceTestVo;
import cn.trasen.homs.SQLToAPI.vo.ParseConditionVo;
import cn.trasen.homs.SQLToAPI.vo.UserDefineServiceVo;
import cn.trasen.homs.SQLToAPI.vo.report.ColumnType;
import cn.trasen.homs.SQLToAPI.vo.report.ExportTitleVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryConditionVo;
import cn.trasen.homs.SQLToAPI.vo.report.QueryParamVo;
import cn.trasen.homs.SQLToAPI.vo.report.TableColumnVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo.DateInit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
//import cn.trasen.query.utils.*;
//import cn.trasen.query.vo.report.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户自定义表格Controller
 * @Author:huangkui
 * @Date: 2019年5月27日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@RestController
@Api(tags = "用户自定义查询表格Controller")
@Slf4j
public class UserDefineQueryTableController {

	@Autowired
	private UserDefineQueryTableService userDefineQueryTableService;

	@Autowired
	private UserDataSourceService userDataSourceService;
	
	@Autowired
	private DateDefaultCfg dateCfg;

//	@Autowired
//	private OfBusiCodeService ofBusiCodeService;
	
	@Value("${trasen.date.begin.range:true}")
	private Boolean beginEnd;

	@ApiOperation("数据源改变")
	@GetMapping("/api/ds/change")
	public PlatformResult<Boolean> dataSourceSelect(@ApiParam(value = "数据源ID") @RequestParam String id,
			@ApiParam(value = "数据源名称") @RequestParam String dsName) {
		return PlatformResult.success(userDataSourceService.change(id,dsName));
	}

	@ApiOperation("测试数据源")
	@PostMapping("/api/ds/test")
	public PlatformResult<List<Object>> dataSourceTest(@RequestBody DataSourceTestVo testVo) {
		return PlatformResult.success(userDataSourceService.testConnection(testVo));
	}

	@ApiOperation("解析SQL语句，返回条件列表、字段列表")
	@PostMapping("/api/user/query/table/parseCondition")
//	public PlatformResult<UserDefineQueryVo> parseCondition(
//			@ApiParam(value = "查询SQL") @RequestParam("sql") String sql,@ApiParam(value = "主键ID") @RequestParam(value="id",required = false) String id) {
	public PlatformResult<UserDefineQueryVo> parseCondition(HttpServletRequest request) {
		try {
			ParseConditionVo vo = readParseConditionBody(request);
			if(vo == null){
				throw new IllegalArgumentException("参数不能为空");
			}
			UserDefineQueryVo userDefineQueryVo = userDefineQueryTableService.selectById(vo.getId());
			return PlatformResult.success(userDefineQueryTableService.parseCondition(vo.getSql(), null, userDefineQueryVo));
		} catch (Exception e) {
			e.printStackTrace();
			return exception(e);
		}
	}
	
	/**
     * 保存自定义报表数据BASE64处理
     * @param req
     * @return
     * @date 2022年01月07日 15:29:05
     * <AUTHOR>
     */
    private ParseConditionVo readParseConditionBody(HttpServletRequest req) {
        String body = null;
        Charset utf8 = Charset.forName("UTF-8");
        try {
            ServletInputStream reader = req.getInputStream();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] bs = new byte[4096];
            int len = reader.read(bs);
            while (len > 0) {
                bos.write(bs, 0, len);
                len = reader.read(bs);
            }

            if (bos.size() > 0) {
                body = new String(bos.toByteArray(),utf8);
            }
        } catch (IOException e) {
            log.error(e.getMessage(),e);;
        }
        Assert.notNull(body,"没有提交数据");
        JSONObject obj = JSONObject.parseObject(body);
        if(obj.containsKey("base64")){
            String str = obj.getString("base64");
            str = new String(Base64.getMimeDecoder().decode(str),utf8);
            obj = JSONObject.parseObject(str);
        }
        return obj.toJavaObject(ParseConditionVo.class);
    }

//	@ApiOperation("解析指标SQL语句，返回条件列表、字段列表")
//	@PostMapping("/api/user/query/busicode/parseCondition")
////	public PlatformResult<UserDefineQueryVo> parseBusicodeCondition(
////			@ApiParam(value = "查询SQL") @RequestParam("sql") String sql,@ApiParam(value = "主键ID") @RequestParam(value="id",required = false) String id) {
//	public PlatformResult<UserDefineQueryVo> parseBusicodeCondition(HttpServletRequest request) {
//		try {
//			ParseConditionVo vo = readParseConditionBody(request);
//			if(vo == null){
//				throw new IllegalArgumentException("参数不能为空");
//			}
//			OfBusiCodeBo ofBusiCodeBo = ofBusiCodeService.selectById(vo.getId(), false);
//			return PlatformResult.success(userDefineQueryTableService.parseBusicodeCondition(vo.getId(), null, ofBusiCodeBo));
//		} catch (Exception e) {
//			e.printStackTrace();
//			if (e instanceof IllegalArgumentException) {
//				return PlatformResult.failure(e.getMessage());
//			} else {
//				return PlatformResult.failure("系统错误：" + e.getMessage());
//			}
//		}
//	}
//	
	private PlatformResult<UserDefineQueryVo> exception(Exception e) {
		if (e instanceof IllegalArgumentException) {
			return PlatformResult.failure(e.getMessage());
		} else {
			return PlatformResult.failure("系统错误：" + e.getMessage());
		}
	}

//	@ApiOperation("自定义报表查询导出excel")
//	@PostMapping("/api/user/query/table/export/excel")
//	public ResponseEntity<byte[]> exportExcel(
//			@ApiParam(value = "文件名") @RequestParam(value = "fileName", required = false) String fileName,
//			@ApiParam(value = "自定义查询ID") @RequestParam("id") String id, Page page,
//			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
//			@RequestParam(value = "parameters", required = false) String parameters,
//			@ApiParam(value = "表头配置（json示例：[{\"name\":\"DEPT\",\"title\":\"部门ID\"},{\"name\":\"USER\",\"title\":\"用户姓名\"},........]）")
//			@RequestParam(value = "titles", required = false) String titles,
//			HttpServletRequest request)
//			throws IOException {
//		return export(fileName, id, page, parameters, titles, request, (map,bos)->{
//			try {
//				TableToExcelUtil.tableToExcel(map,bos);
//				return ".xls";
//			}catch (Exception e) {
//				e.printStackTrace();
//				log.error(e.getMessage(), e);
//			}
//			return null;
//		});	
//	}
//	
	public static void main(String[] args) throws IOException {
//		String titles = "[{\"id\":\"cbf983f9597472b4\",\"title\":\"机构数\",\"align\":\"center\",\"field\":\"a\",\"width\":14,\"fontSize\":12,\"fontColor\":\"#333\",\"backgroundColor\":\"\",\"pid\":\"237879\",\"resizable\":true,\"children\":[],\"type\":\"col\",\"targetId\":\"a\",\"targetName\":\"a\",\"columnType\":\"STRING\",\"sortable\":true,\"isHasChild\":false,\"sortBy\":\"number\",\"name\":\"a\"},{\"title\":\"诊疗人次数\",\"children\":[{\"title\":\"急诊：门。急诊人次数\",\"children\":[{\"title\":\"急诊人次\",\"children\":[{\"title\":\"内死亡人次\",\"children\":[],\"name\":\"a\"},{\"title\":\"小计\",\"children\":[],\"name\":\"b\"}],\"name\":\"b\",\"virtual\":true},{\"title\":\"合计\",\"children\":[],\"name\":\"b\"},{\"title\":\"门诊人次\",\"children\":[{\"title\":\"预约人\",\"children\":[],\"name\":\"c\"},{\"title\":\"小计\",\"children\":[],\"name\":\"b\"}],\"name\":\"c\",\"virtual\":true}],\"name\":\"a\",\"virtual\":true},{\"title\":\"总计\",\"children\":[],\"name\":\"a\"}],\"name\":\"b\",\"virtual\":true},{\"title\":\"观察室\",\"children\":[{\"title\":\"其实死亡人\",\"children\":[],\"name\":\"a\"},{\"title\":\"留观病例数\",\"children\":[],\"name\":\"b\"}],\"name\":\"c\",\"virtual\":true},{\"title\":\"健康检测人数\",\"children\":[],\"name\":\"d\"},{\"title\":\"门、急诊诊次占总诊次之比   %\",\"children\":[],\"name\":\"e\"},{\"title\":\"急诊死亡率 %\",\"children\":[],\"name\":\"f\"},{\"title\":\"g\",\"children\":[],\"name\":\"g\"}]";
		String titles = "[{\"title\":\"机构数\",\"name\":\"a\"},{\"title\":\"诊疗人次数\",\"children\":[{\"title\":\"急诊：门。急诊人次数\",\"children\":[{\"title\":\"急诊人次\",\"children\":[{\"title\":\"内死亡人次\",\"name\":\"a\"},{\"title\":\"小计\",\"name\":\"b\"}],\"name\":\"b\",\"virtual\":true},{\"title\":\"合计\",\"name\":\"b\"},{\"title\":\"门诊人次\",\"children\":[{\"title\":\"预约人\",\"name\":\"c\"},{\"title\":\"小计\",\"name\":\"b\"}],\"name\":\"c\",\"virtual\":true}],\"name\":\"a\",\"virtual\":true},{\"title\":\"总计\",\"name\":\"a\"}],\"name\":\"b\",\"virtual\":true},{\"title\":\"观察室\",\"children\":[{\"title\":\"其实死亡人\",\"name\":\"a\"},{\"title\":\"留观病例数\",\"name\":\"b\"}],\"name\":\"c\",\"virtual\":true},{\"title\":\"健康检测人数\",\"name\":\"d\"},{\"title\":\"门、急诊诊次占总诊次之比   %\",\"name\":\"e\"},{\"title\":\"急诊死亡率 %\",\"name\":\"f\"},{\"title\":\"g\",\"name\":\"g\"}]";
		List<ExportTitleVo> cols = JSONArray.parseArray(titles, ExportTitleVo.class);
		List<TdNode> tdTree = ReportUtil.transTdNodeTree(cols);
		System.out.println(tdTree);
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		//
		String mapString = "{\r\n" + 
				"            \"a\": \"a\\r\\nb\",\r\n" + 
				"            \"b\": \"\\\"看看\\\",\\\",,,,\\\"\",\r\n" + 
				"            \"c\": \"-123.345\",\r\n" + 
				"            \"d\": \"123,223,333.90\",\r\n" + 
				"            \"e\": \"-123,456,099\",\r\n" + 
				"            \"f\": \"430190201001318976\",\r\n" + 
				"            \"ff\": \"430190201001318976\",\r\n" + 
				"            \"fe\": \"430190201001318976\",\r\n" + 
				"            \"g\": \"430190100131897\"\r\n" + 
				"        }";
		JSONObject obj = JSONObject.parseObject(mapString);

		Map<String, Object> data =new HashMap<>();
		//循环转换
		 Iterator<Entry<String, Object>> it =obj.entrySet().iterator();
		 while (it.hasNext()) {
		       Map.Entry<String, Object> entry = (Entry<String, Object>) it.next();
		       data.put(entry.getKey(), entry.getValue());
		 }
		
		list.add(data );
		String table = ReportUtil.treeToHtmlTableHead(tdTree, list );
		System.out.println(table);
		Map<String, String> map = new HashMap<String, String>();
		map.put("test", table);
		
		OutputStream bos = new FileOutputStream("test.xls");
//		TableToExcelUtil.tableToExcel(map ,bos );
	}
	
	private ResponseEntity<byte[]> export(String fileName,
			String id, Page page,String parameters,String titles,
			HttpServletRequest request,BiFunction<Map<String, String>, OutputStream, String> biFun) throws IOException{
		UserDefineQueryVo query = userDefineQueryTableService.selectById(id);
		Assert.notNull(query, "找不到对应配置");
		if (!StringUtils.hasText(fileName)) {
			fileName = query.getQueryName();
		} else {
			if (fileName.indexOf('.') != -1) {
				fileName = fileName.substring(0, fileName.indexOf('.'));
			}
		}
		List<TdNode> tdTree = null;
		if(StringUtils.hasText(titles)) {
			List<ExportTitleVo> cols = JSONArray.parseArray(titles, ExportTitleVo.class);
			tdTree = ReportUtil.transTdNodeTree(cols);
		}else {
			List<TableColumnVo> cols = query.getTableColumns();
			tdTree = ReportUtil.transTdNodeTree(cols);
		}

		page.setPageNo(1);
		// 最多查6万条
		page.setPageSize(60000);
		String paraStr = CommonSqlUtil.paraseParameter(request,parameters,query.getQueryConditions(),QueryConditionVo::getName);	
		DataSetVo<Map<String, Object>> dataSet = userDefineQueryTableService.queryPageResult(query, paraStr, page);
		List<Map<String, Object>> rows = dataSet.getRows();
		
		
		String table = ReportUtil.treeToHtmlTableHead(tdTree, rows);

		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		Map<String, String> htmlMap = new HashMap<>();
		htmlMap.put(fileName, table);
		String suffix = biFun.apply(htmlMap, bos);
		bos.close();

		HttpHeaders headers = new HttpHeaders();
		// 设置编码
		if (fileName.equals("")) {
			fileName = "Export"+suffix;
		} else {
			fileName += suffix;
		}
		String downloadFileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
		headers.setContentDispositionFormData("attachment", downloadFileName);
		headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		return new ResponseEntity<byte[]>(bos.toByteArray(), headers, HttpStatus.CREATED);
	}
	
//	@ApiOperation("自定义报表查询导出excel")
//	@PostMapping("/api/user/query/table/export/csv")
//	public ResponseEntity<byte[]> exportCsv(
//			@ApiParam(value = "文件名") @RequestParam(value = "fileName", required = false) String fileName,
//			@ApiParam(value = "自定义查询ID") @RequestParam("id") String id, Page page,
//			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
//			@RequestParam(value = "parameters", required = false) String parameters,
//			@ApiParam(value = "表头配置（json示例：[{\"name\":\"DEPT\",\"title\":\"部门ID\"},{\"name\":\"USER\",\"title\":\"用户姓名\"},........]）")
//			@RequestParam(value = "titles", required = false) String titles,
//			HttpServletRequest request)
//			throws IOException {
//		return export(fileName, id, page, parameters,titles, request, (map,bos)->{
//			try {
//				TableToExcelUtil.tableToCsv(map,bos);
//				return ".csv";
//			}catch (Exception e) {
//				e.printStackTrace();
//				log.error(e.getMessage(), e);
//			}
//			return null;
//		});		
//	}
	

	@ApiOperation("自定义报表查询(返回XML)")
	@RequestMapping(value = "/api/user/query/table/queryXml", method = {RequestMethod.GET,RequestMethod.POST})
	@ResponseBody
	public String queryXml(@ApiParam(value = "自定义查询ID") @RequestParam("id") String id, Page page,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）")
			@RequestParam(value = "parameters", required = false) String parameters,
			HttpServletRequest request) {
	    log.info("queryXml param id:{},parameters:{}",id,parameters);
		DataSetVo<Map<String, Object>> obj = query(id, page, parameters, request);
        Map<String,String> alias = new HashMap<String,String>();
        alias.put("rows","row");
        log.info("queryXml beanToXml obj:{},alias:{}",JSON.toJSONString(obj),JSON.toJSONString(alias));
        String xml = XmlUtils.beanToXml(DataSetVo.class,obj,"result",alias);
        log.info("queryXml beanToXml xml:{}",xml);
        return xml;
	}
	
	
	@ApiOperation("自定义存储过程、函数查询（body参数方式）")
	@RequestMapping( value = "/api/user/query/table/bodyQuery",method = {RequestMethod.GET,RequestMethod.POST})
	public DataSetVo<Map<String, Object>> query(HttpServletRequest request) {
		byte[] bodys = RequestBodyUtils.readBody(request);
		Page page = new Page();
		if(bodys == null) {
			log.error("没有请求体内容");
			page.setTotalCount(0);
			page.setTotalPages(0);
			return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
					new ArrayList<>());
		}
		String bodyString = new String(bodys,Charset.forName("UTF-8"));
		JSONObject object = null;
		try {
			object = JSONObject.parseObject(bodyString);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			log.error("请求的JSON格式错误："+bodyString);
			page.setTotalCount(0);
			page.setTotalPages(0);
			return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
					new ArrayList<>());
		}
		if(!object.containsKey("id")) {
			log.error("没有请求体内容必须提供存储过程ID");
			page.setTotalCount(0);
			page.setTotalPages(0);
			return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
					new ArrayList<>());
		}
		if(object.containsKey("pageNo")) {
			page.setPageNo(Integer.parseInt(object.get("pageNo").toString()));
			object.remove("pageNo");
		}
		if(object.containsKey("pageSize")) {
			page.setPageSize(Integer.parseInt(object.get("pageSize").toString()));
			object.remove("pageSize");
		}
		String[] idAndPara = new String[2];
		CommonSqlUtil.parseIdAndParasFromJson(object, (i,p)->{idAndPara[0]=i;idAndPara[1]=p;});
		return query(idAndPara[0],page,idAndPara[1],request);
	}
	
	@ApiOperation("自定义报表查询（并发查询）")
	@RequestMapping(value = "/api/user/query/table/multiQuery", method = {RequestMethod.GET,RequestMethod.POST})
	public PlatformResult<List<DataSetVo<Map<String, Object>>>> multiQuery(@ApiParam(value = "自定义查询ID（多个用逗号分隔）") @RequestParam("id") String id, Page page,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
			@RequestParam(value = "parameters", required = false) String parameters,
			HttpServletRequest request){
		if(StringUtils.hasText(id)) {
			String[] ids = id.split(",");
			int num = ids.length;
			if(num > 10) {
				//最多10个线程
				num = 10;
			}
			PlatformResult<List<DataSetVo<Map<String, Object>>>> result = 
					PlatformResult.success(new ArrayList<DataSetVo<Map<String, Object>>>());
			int[] idx = new int[] {0};
			int[] count = new int[] {0};
			Object obj = new Object();
			for(int i=0;i<num;i++) {
				Thread t = new Thread(()->{
					while(true) {
						int index = -1;
						synchronized(idx) {
							index = idx[0]++;
						}
						if(index < ids.length) {
							String qid = ids[index];
							log.debug("查询线程将执行-{}-{}",index,qid);
							DataSetVo<Map<String, Object>> rs = null;
							try {
								rs = query(qid,page,parameters,request);								
							}catch (Exception e) {
								log.error(e.getMessage(),e);
								rs = new DataSetVo<Map<String, Object>>();
								rs.setErrMsg(e.getMessage());
								rs.setSuccess(false);								
							}
							rs.setId(qid);
							result.getObject().add(rs);
							int cc = 0;
							synchronized(count) {
								count[0]++;
								cc = count[0];
							}
							if(cc == ids.length) {
								synchronized (obj) {
									obj.notify();
								}
							}
						}else {
							log.debug("查询线程将退出-{}-{}",index,ids.length);
							break;
						}						
					}
				});
				t.setName("MULTI-QUERY#"+i+"#"+t.getId());
				t.start();
			}
			log.debug("将等待结束通知...");
			synchronized (obj) {
				try {
					obj.wait();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
			log.debug("结束通知已到，将返回数据...");
			return result;
		}else {
			return PlatformResult.failure("缺少ID参数");
		}
	}
	
	@ApiOperation("自定义报表查询（id为路径变量）")
	@RequestMapping(value = "/api/user/query/table/query/{id}", method = {RequestMethod.GET,RequestMethod.POST})
	public DataSetVo<Map<String, Object>> queryPath(@ApiParam(value = "自定义查询ID") @PathVariable("id") String id, Page page,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
			@RequestParam(value = "parameters", required = false) String parameters,
			HttpServletRequest request){
		return query(id, page, parameters, request);
	}
	
	@ApiOperation("自定义报表查询")
	@RequestMapping(value = "/api/user/query/table/query", method = {RequestMethod.GET,RequestMethod.POST})
	public DataSetVo<Map<String, Object>> query(@ApiParam(value = "自定义查询ID") @RequestParam("id") String id, Page page,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
			@RequestParam(value = "parameters", required = false) String parameters,
			HttpServletRequest request) {
		log.info("parameters-----{}", parameters);
		if(!StringUtils.isEmpty(parameters)){
			parameters = parameters.replaceAll("&amp;quot;","\"");
			log.info("parameters-----{}", parameters);
		}
		if(!ObjectUtils.isEmpty(id) && id.contains("script") ) {
			return DataSetVo.fail("ID入参异常.");
		}
		if(!ObjectUtils.isEmpty(id)) {
			Pattern p = Pattern.compile("<img[^>].*?>");
			Matcher m = p.matcher(id);
			if(m.find()) {
				return DataSetVo.fail("ID入参异常.");
			}
		}
		try {
			return userDefineQueryTableService.tableQuery(id, page, parameters, request);
		} catch (Exception e) {
//			e.printStackTrace();
			if(e instanceof QueryException) {
				String msg = e.getMessage();
				if(msg != null) {
					if(msg.contains("ORA-01476")) {
						//oracle除数为零
						return DataSetVo.fail("查询错误（除数为零），请检查数据是否存在");
					}
				}
				return DataSetVo.fail("执行查询出现了错误，请检查SQL语句和参数配置");
			}else if(e instanceof SQLException || e.getCause() instanceof SQLException) {
				return DataSetVo.fail("执行查询出现了错误，请检查SQL语句和参数配置");
			}
			return DataSetVo.fail(e.getMessage());
		}
	}

	private List<DateInit> transDateValue(List<QueryConditionVo> qcs, List<QueryParamVo> queryParamVo) {
		List<DateInit> dateValues = new ArrayList<>();
		for(QueryConditionVo qvo:qcs) {
			if(qvo.getColumnType().equals(ColumnType.DATE) || qvo.getColumnType().equals(ColumnType.DATEUNIT)) {
				String name = qvo.getName();
				QueryParamVo pvo = queryParamVo.stream().filter(p -> p.getName().equals(name)).findFirst().orElse(null);
				if(pvo != null) {
					dateValues.add(new DateInit(name, pvo.getValue()));
				}else {
					log.warn("执行查询找不到时间参数{}值", name);
				}
			}
		}
		return dateValues;
	}

	@ApiOperation("试查询")
	@PostMapping("/api/user/query/table/tryQuery")
//	public DataSetVo<Map<String, Object>> tryQuery(UserDefineService query,
//			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") @RequestParam("parameters") String parameters) {
	public DataSetVo<Map<String, Object>> tryQuery(HttpServletRequest request) {
		try {
			UserDefineServiceVo query = readFromBody(request);
			UserDefineQueryVo queryVo = userDefineQueryTableService.copyFrom(query);
			Assert.hasText(query.getDsId(), "必需选择数据源");
			List<QueryParamVo> queryParamVo = userDefineQueryTableService.transParam(query.getParameters());
			String checkResult = userDefineQueryTableService.check(queryVo, queryParamVo);
			if (checkResult != null) {
				throw new IllegalArgumentException(checkResult);
			}
			// 因为校验过程可能会纠正字段大小写，所以对请求参数进行转换
			String paraStr = transJsonName(query.getParameters(), queryVo.getQueryConditions(), QueryConditionVo::getName);
			queryParamVo = userDefineQueryTableService.transParam(paraStr);

			Page page = new Page(1, 20);
			List<Map<String, Object>> rows = userDefineQueryTableService.query(queryVo, queryParamVo, page);
			return new DataSetVo<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
					rows);
		} catch (Exception e) {
			e.printStackTrace();
			return DataSetVo.fail(e.getMessage());
		}
	}

	private <T> String transJsonName(String json, List<T> list, Function<T, String> nameFun) {
		String jsonStr = json;
		if (list != null) {
			for (T obj : list) {
				String name = nameFun.apply(obj);
				if (name != null) {
					String replace = "\"name\":\"" + name + "\"";
					int idx = jsonStr.indexOf(replace);
					if (idx == -1) {
						// 完全匹配不上时，才用小写匹配
						String idxStr = "\"name\":\"" + name.toLowerCase() + "\"";
						idx = jsonStr.toLowerCase().indexOf(idxStr);
						if (idx != -1) {
							// 小写匹配上了再替换
							jsonStr = jsonStr.substring(0, idx) + replace + jsonStr.substring(idx + idxStr.length());
						}
					}
				}
			}
		}
		return jsonStr;
	}

	/**
	 * 
	 * @param query
	 * @param otherSave 是否是列表上更新数据，默认为false
	 * @return
	 */
	@ApiOperation("添加或修改自定义查询")
	@PostMapping("/api/user/query/table/saveOrUpdate")
//	public PlatformResult<UserDefineQueryVo> saveOrUpdate(UserDefineService query, boolean otherSave) {
	public PlatformResult<UserDefineQueryVo> saveOrUpdate(HttpServletRequest request) {
		try {
			UserDefineServiceVo query = readFromBody(request);
			log.info("添加或修改自定义查询入参：{}",JSONObject.toJSONString(query));
			//判断是否是列表更新，是则将数据库替换取出来替换当前传递过来的tableColumns
			if(query.isOtherSave() && StringUtils.hasText(query.getId())){
				UserDefineQueryVo oldVo = userDefineQueryTableService.selectById(query.getId());
				if(null != oldVo){
					query.setColumnDefine(oldVo.getColumnDefine());
					query.setConditionDefine(oldVo.getConditionDefine());
				}
			}
			UserDefineQueryVo vo = userDefineQueryTableService.copyFrom(query);
			Assert.hasText(vo.getDsId(), "必需选择数据源");
			log.info("添加或修改自定义查询UserDefineQueryVo值：{}",JSONObject.toJSONString(vo));
			String checkResult = userDefineQueryTableService.check(vo, null);
			log.info("添加或修改自定义查询checkResult值：{}",checkResult);

			Assert.isNull(checkResult, checkResult);

			// 因为此时已经指定了数据源类型，校验时会纠正错误的字段名，所以反过来修改对应的json串
			String columnStr = query.getColumnDefine();
			List<TableColumnVo> list = new ArrayList<>();
			CommonSqlUtil.treeToList(vo.getTableColumns(), list);
			query.setColumnDefine(transJsonName(columnStr, list, TableColumnVo::getName));
			String conditionStr = query.getConditionDefine();
			query.setConditionDefine(transJsonName(conditionStr, vo.getQueryConditions(), QueryConditionVo::getName));

			return PlatformResult.success(userDefineQueryTableService.saveOrUpdate(query));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("saveOrUpdate exception:{}",e);
			return exception(e);
		}
	}
    /**
     * 保存自定义报表数据BASE64处理
     * @param req
     * @return
     * @date 2022年01月07日 15:29:05
     * <AUTHOR>
     */
    private UserDefineServiceVo readFromBody(HttpServletRequest req) {
        String body = null;
        Charset utf8 = Charset.forName("UTF-8");
        try {
            ServletInputStream reader = req.getInputStream();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] bs = new byte[4096];
            int len = reader.read(bs);
            while (len > 0) {
                bos.write(bs, 0, len);
                len = reader.read(bs);
            }

            if (bos.size() > 0) {
                body = new String(bos.toByteArray(),utf8);
            }
        } catch (IOException e) {
            log.error(e.getMessage(),e);;
        }
        Assert.notNull(body,"没有提交数据");
        JSONObject obj = JSONObject.parseObject(body);
        if(obj.containsKey("base64")){
            String str = obj.getString("base64");
            str = new String(Base64.getMimeDecoder().decode(str),utf8);
            obj = JSONObject.parseObject(str);
        }
        return obj.toJavaObject(UserDefineServiceVo.class);
    }

	@ApiOperation("获取自定义查询")
	@GetMapping("/api/user/query/table/select/{id}")
	public PlatformResult<UserDefineQueryVo> selectById(@ApiParam(value = "自定义查询ID") @PathVariable("id") String id,
			@ApiParam(value = "是否为数据查询") @RequestParam(name = "query", required = false) Boolean query) {
		UserDefineQueryVo vo = userDefineQueryTableService.selectById(id);
		if(vo.getQuerySql().toLowerCase().indexOf("select")!=-1) {
			UserDefineQueryVo pvo = userDefineQueryTableService.parseCondition(vo.getQuerySql(), vo.getDsType(),vo);
			vo.setSavedConditions(vo.getQueryConditions());
			vo.setQueryConditions(pvo.getQueryConditions());			
		}else {
			vo.setSavedConditions(vo.getQueryConditions());			
		}
		vo.getSavedConditions().forEach(c -> c.setId(IdGeneraterUtils.nextId()));
		if(query != null && query.booleanValue()) {
			String paras = CommonSqlUtil.loadDefaultParas("",vo.getSavedConditions(), dateCfg);
			 List<QueryParamVo> qps = JSONObject.parseArray(paras, QueryParamVo.class);
			 //对begin和end特殊处理
			 String date = "";
			 for(QueryParamVo qpo:qps) {
				 if(qpo.getName().equals("begin")) {
					 date = qpo.getValue()+" ~ ";
				 }else if(qpo.getName().equals("end")) {
					 date = date + qpo.getValue();
				 }
			 }
			 int idx = date.indexOf(" ~ ");
			 if(idx != -1 && idx+3 < date.length()) {
				 for(QueryParamVo qpo:qps) {
					 if(qpo.getName().equals("begin")) {
						 QueryConditionVo cvo = vo.getSavedConditions().stream().filter(p -> p.getName().equals("begin")).findFirst().get();
						 if(cvo.isRange() || beginEnd) {
							 //如果配置了范围，或者参数为true（默认）
							 cvo.setRange(true);
							 qpo.setValue(date);
						 }
					 }
				 }
			 }
			vo.setDateInitValues(transDateValue(vo.getSavedConditions(),  qps));
		}
		return PlatformResult.success(vo);
	}

	@ApiOperation("自定义查询列表（可以按照名称模糊检索）")
	@PostMapping("/api/user/query/table/list")
	public DataSet<UserDefineQueryVo> list(Page page,
			@ApiParam(value = "查询条件，模糊查询匹配名称") @RequestParam(name = "condition", required = false) String condition) {
		List<UserDefineQueryVo> list = userDefineQueryTableService.list(condition, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	@ApiOperation("删除自定义查询")
	@PostMapping("/api/user/query/table/delete/{id}")
	public PlatformResult<Integer> deleteById(@ApiParam(value = "自定义查询ID") @PathVariable("id") String id) {
		return PlatformResult.success(userDefineQueryTableService.delete(id));
	}
}
