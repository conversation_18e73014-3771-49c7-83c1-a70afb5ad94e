package cn.trasen.homs.SQLToAPI.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import cn.trasen.homs.SQLToAPI.model.UserDefineService;
import cn.trasen.homs.SQLToAPI.service.UserDefineServiceService;
import cn.trasen.homs.SQLToAPI.vo.ExportReportVo;
import cn.trasen.homs.SQLToAPI.vo.report.UserDefineQueryVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 ********************************************** 
 * @Description: 用户自定义表格Controller
 * @Author:huangkui
 * @Date: 2020年09月16日
 * @Copyright: Copyright (c) 2006 - 2019
 * @Company: 湖南创星科技股份有限公司
 * @Version: 1.0.0
 ***********************************************
 */
@RestController
@Api(tags = "用户自定义服务Controller")
@Slf4j
public class UserDefineServiceController {

	@Autowired
	private UserDefineServiceService userDefineServiceService;
	
	@Autowired
	private UserDefineQueryTableController tableController;
	
	@Autowired
	private UserDefineQueryProcFunController procFunController;
	
	@ApiOperation("查询SQL或存储过程")
	@PostMapping("/api/user/define/service/query")
	public Object query(@ApiParam(value = "自定义查询ID") @RequestParam("id") String id, Page page,
			@ApiParam(value = "查询参数（json示例：[{name:\"n1\",value:\"123\"},{name:\"n2\",list=[\"2018-01\",\"2018-02\"]},{...]）") 
			@RequestParam(value = "parameters", required = false) String parameters,
			HttpServletRequest request){
		UserDefineQueryVo vo = userDefineServiceService.selectById(id);
		Integer type = vo.getServiceType();
		if(type == 1) {
			//SQL
			return tableController.query(id, page, parameters, request);
		}else {
			//存储过程
			return procFunController.query(id, parameters, request);
		}
	}

	@ApiOperation("自定义查询列表（可以按照名称模糊检索）")
	@PostMapping("/api/user/define/service/list")
	public DataSet<UserDefineQueryVo> list(Page page,
			@ApiParam(value = "类别（1、SQL，2、存储过程）") @RequestParam(name = "categoryCode", required = false) String categoryCode,
			@ApiParam(value = "主题分类") @RequestParam(name = "topicCateCode", required = false) String topicCateCode,
			@ApiParam(value = "查询条件，模糊查询匹配名称") @RequestParam(name = "condition", required = false) String condition,
			@ApiParam(value = "系统服务：01-是,02-否") @RequestParam(name = "systemService", required = false) String systemService,
			@ApiParam(value = "排除字段") @RequestParam(name = "excludeColumns", required = false) String excludeColumns) {
		String[] excludeColumnsArr = !ObjectUtils.isEmpty(excludeColumns) ? excludeColumns.split(",") : null;
		List<UserDefineQueryVo> list = userDefineServiceService.list(categoryCode, topicCateCode, condition, systemService, page, excludeColumnsArr);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	/**
	 *
	 * @Title selectBiUserSelectConfigById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<BiUserSelectConfig>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/user/define/service/{id}")
	public PlatformResult<UserDefineQueryVo> selectBiUserDefineServiceById(@PathVariable String id) {
		try {
			UserDefineQueryVo userDefineQueryVo = userDefineServiceService.selectById(id);
			return PlatformResult.success(userDefineQueryVo);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title doUserDefineServiceImportReport
	 * @Description 自定义服务导入
	 * @param file
	 * @param dsId 数据源ID
	 * @throws Exception 
	 * @return PlatformResult<String>
	 * @date 2021年8月22日 下午3:17:03
	 * <AUTHOR>
	 */
    @ApiOperation("自定义服务导入")
	@PostMapping(value = "/api/user/define/service/import")
    public PlatformResult<String> doUserDefineServiceImportReport(@RequestParam("file") MultipartFile file, String dsId) throws Exception{
        String content ="";
        try{
            content = new String(Base64.decodeBase64(new String(file.getBytes(),StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
            ArrayList<UserDefineService> recordList = new ArrayList<>();
            recordList.add(JSON.parseObject(content, UserDefineService.class));
            //设置自定义服务的数据源
            for(UserDefineService defineService : recordList){
            	defineService.setDsId(dsId);
            }
        	userDefineServiceService.doBatchSave(recordList);
            return PlatformResult.success();
        }catch (Exception e){
            log.error("importReport content:{},exception:{}",content,e);
            return PlatformResult.failure("报表导入失败,exception:"+e.getMessage());
        }
    }
    
    /**
     * @Title doUserDefineServiceExport
     * @Description 自定义服务导出
     * @param param
     * @param res
     * @throws Exception 
     * @return void
     * @date 2021年8月22日 下午3:17:39
     * <AUTHOR>
     */
	@ApiOperation("自定义服务导出")
	@PostMapping(value = "/api/user/define/service/export")
	public void doUserDefineServiceExport(ExportReportVo param, HttpServletResponse res) throws Exception {
		if(ObjectUtils.isEmpty(param.getId())) {
			throw new RuntimeException("服务ID不能为空！");
		}
		// 响应头的设置
		res.reset();
		res.setCharacterEncoding("utf-8");
		res.setContentType("multipart/form-data");
		SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMddHH24mmss");
		String now = fmt.format(new Date());

		String downloadName = String.format("ExportReport_%s.zip", now);
		res.setHeader("Content-Disposition", "attachment;fileName=\"" + downloadName + "\"");

		String pathStr = String.format("/tmp/check_%s/", now);
		File path = new File(pathStr);
		if (!path.exists()) {
			path.mkdirs();
		}
		
		// 获取ID列表
		List<String> ids = Arrays.asList(param.getId().split(","));
		List<UserDefineService> resultList = userDefineServiceService.selectByIds(ids);
		for (UserDefineService temp : resultList) {
			File file = null;
			FileOutputStream fos = null;
			try {
				String fileName = String.format("/tmp/check_%s/%s.txt", now, temp.getQueryName()+ "_" + new Date().getTime());
				file = new File(fileName);
				if (!file.exists()) {
					file.createNewFile();
				}
				fos = new FileOutputStream(file);
				fos.write(Base64.encodeBase64(JSON.toJSONString(temp).getBytes(StandardCharsets.UTF_8)));
				fos.flush();
			} catch (Exception e) {
				log.error("exportReport write file exception:{}", e);
				log.error(e.getMessage(), e);
			} finally {
				if (fos != null) {
					try {
						fos.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
		// 设置压缩流：直接写入response，实现边压缩边下载
		ZipOutputStream zipos = null;
		try {
			zipos = new ZipOutputStream(new BufferedOutputStream(res.getOutputStream()));
			zipos.setMethod(ZipOutputStream.DEFLATED); // 设置压缩方法
		} catch (Exception e) {
		}
		// 循环将文件写入压缩流
		DataOutputStream os = null;

		File[] files = path.listFiles();
		for (File file : files) {
			try {
				String filename = file.getName();
				// 添加ZipEntry，并ZipEntry中写入文件流
				zipos.putNextEntry(new ZipEntry(filename));
				os = new DataOutputStream(zipos);
				FileInputStream fis = new FileInputStream(file);
				InputStream is = new BufferedInputStream(fis);
				byte[] b = new byte[100];
				int length = 0;
				while ((length = is.read(b)) != -1) {
					os.write(b, 0, length);
				}
				fis.close();
				is.close();
				zipos.closeEntry();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		for (File file : files) {
			if (file.exists()) {
				file.delete();
			}
		}

		if (path != null && path.exists()) {
			path.delete();
		}

		// 关闭流
		try {
			os.flush();
			os.close();
			zipos.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
