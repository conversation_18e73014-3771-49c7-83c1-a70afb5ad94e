package cn.trasen.homs.SQLToAPI.vo.report;

/**
**********************************************   
* @Description: 存储过程、函数结果位置
* @Author:huangkui  
* @Date: 2019年5月27日
* @Copyright: Copyright (c)  2006 - 2019
* @Company: 湖南创星科技股份有限公司
* @Version: 1.0.0
***********************************************
 */
public enum ProcedureResultPlace {
	ORDERLEFT(0, "顺序靠左"), ORDERCENTER(1, "顺序居中"),ORDERRIGHT(2, "顺序靠右"),
	HEADLEFT(10, "页头靠左"), HEADCENTER(11, "页头居中"),HEADRIGHT(12, "页头靠右"),
	FOOTERLEFT(20, "页脚靠左"), FOOTERCENTER(21, "页脚居中"),FOOTERRIGHT(22, "页脚靠右");

	public String getPlaceName() {
		return placeName;
	}

	public void setPlaceName(String placeName) {
		this.placeName = placeName;
	}

	public Integer getPlaceCode() {
		return placeCode;
	}

	public void setPlaceCode(Integer placeCode) {
		this.placeCode = placeCode;
	}

	private String placeName;
	private Integer placeCode;

	private ProcedureResultPlace(Integer code, String name) {
		this.placeName = name;
		this.placeCode = code;
	}
	
	public static ProcedureResultPlace fromCode(Integer code) {
		switch (code) {
		case 0:
			return ORDERLEFT;
		case 1:
			return ORDERCENTER;
		case 2:
			return ORDERRIGHT;

		case 10:
			return HEADLEFT;
		case 11:
			return HEADCENTER;
		case 12:
			return HEADRIGHT;

		case 20:
			return FOOTERLEFT;
		case 21:
			return FOOTERCENTER;
		case 22:
			return FOOTERRIGHT;

		default:
			break;
		}
		return null;
	}
}
