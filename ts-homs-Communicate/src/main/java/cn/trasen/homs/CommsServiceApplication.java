package cn.trasen.homs;

import java.util.Properties;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

import cn.trasen.homs.core.DynamicDS.DynamicDataSourceRegister;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import tk.mybatis.spring.annotation.MapperScan;

@ServletComponentScan
@ImportResource(locations = { "classpath:druid-config.xml" })
@MapperScan({"cn.trasen.homs.*.dao", "cn.trasen.homs.*.mapper", "cn.trasen.homs.*.*.mapper"})
@ComponentScan({"cn.trasen.*"})
//导入自定义多数据源配置
@Import(DynamicDataSourceRegister.class)
@EnableTransactionManagement
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableSwagger2
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableAutoConfiguration
@EnableDiscoveryClient
@EnableFeignClients({"cn.trasen.homs.feign.*","cn.trasen.homs.core.service"})
@EnableWebSocket
@EnableScheduling
public class CommsServiceApplication extends SpringBootServletInitializer {

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(CommsServiceApplication.class);
	}

	public static void main(String[] args) {
		SpringApplication.run(CommsServiceApplication.class, args);
	}
}
