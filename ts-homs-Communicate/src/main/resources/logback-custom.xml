<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<property resource="application.yml"/>
	<springProperty scope="context" name="projectName" source="spring.application.name"/>
	<!-- 控制台输出 -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%-5level] [%logger{80}] - %msg%n
			</pattern>
		</encoder>
	</appender>
	<!-- 文件输出日志 (文件大小策略进行文件输出，超过指定大小对文件备份) -->
	<appender name="FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>logs/${projectName}.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<FileNamePattern>logs/${projectName}-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
			<maxHistory>2</maxHistory>
            <maxFileSize>50MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" charset="UTF-8">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80} - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="FILE-ERROR"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>logs/${projectName}-err.log</File>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<OnMismatch>DENY</OnMismatch>
			<OnMatch>ACCEPT</OnMatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<FileNamePattern>logs/${projectName}-error-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
			<maxHistory>2</maxHistory>
            <maxFileSize>30MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" charset="UTF-8">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80} - %msg%n</pattern>
		</encoder>
	</appender>

	<root level="DEBUG">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="FILE" />
		<appender-ref ref="FILE-ERROR" />
	</root>

	<logger name="cn.trasen" level="DEBUG" />
	<logger name="springfox.documentation" level="INFO" />
	<logger name="com.hzg.core" level="INFO" />
	<logger name="org.springframework" level="INFO" />
	<logger name="org.mybatis" level="INFO" />
	<logger name="org.apache.ibatis" level="INFO" />
	<logger name="org.apache" level="INFO" />
	<logger name="ch.qos.logback" level="INFO" />
	<logger name="druid.sql" level="INFO" />
	<logger name="com.netflix" level="INFO"/>
	<logger name="org.hibernate" level="INFO"/>
	<logger name="com.alibaba.druid" level="INFO" />
	<logger name="freemarker" level="INFO" />
	<logger name="io.netty" level="INFO"/>
	<logger name="io.lettuce" level="INFO"/>
	<logger name="org.eclipse.jetty" level="INFO"/>
</configuration>