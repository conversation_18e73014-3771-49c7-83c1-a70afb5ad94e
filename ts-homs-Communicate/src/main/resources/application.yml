appconfig:
  login: http://testxtbg.trasen.cn/mobile-container/login.html
  wxLoginUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-oa/
  whiteUrlList:
      - "/static/"
      - "/favicon.ico"   
      - "/webjars"         
      - "/messageInternal/sendVerifyCode"   
      - "/messageInternal/verifyCode"   
      - "/notice/sendNotice"             
      - "/messagewebsocket/"        
      - "/oa/bindUser"     
      - "/cp/weixin"
      - "/wxcore"
      - "/sysUsage/save"
      - "/messageLogin/sendVerifyCode"
      - "/messageLogin/SMSlogin"
      - "/api/CommErrorLogs/pushSave"
      - "/PisZbCollection/pushInsetBase64Data"
      - "/PisZbMonitor/pushInsetBase64Data"
      - "/api/DruidMonitoringRecords/pushInsetBase64Data"
      - "/druid/"
  sso:
      defaultPrikey: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAseG6INcVyA1vr4PpSr7tojShqTPdq86NnHTjU6QEqpLhKH8hQJdwDRW1tlG4svFPf7kOvy8c3+x4vZc0xywdEwIDAQABAkAT4QIIAYFxpe7BUqCTtdqgsfkPC7jOJns07Osqwb2zwh6pU9KUyk0O5yp6CNfHTGcH3THnYbI1nTyzKqqJnfQBAiEA2uUSg22bat6O5UIlEjVQrsB6hcecwRC63rwRSV7JUYECIQDQCOUjvq44CDWsesT8DVmpIGhHGCmVBHt7gyc0qRpQkwIgHnbdIb+Cbtg0qQGQqT0UUo3lP3MthM0wRMmF2mE/wYECIEg7Pmw50b3sw84eVnT5oa8CbJJ6xj1ScBmDgUJckKF3AiBQeY8dS/XSvcETlq/KVA4nF8ksV2Yx1sFdA4K+1F8+rg==

sso:
  defaultPrikey: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAseG6INcVyA1vr4PpSr7tojShqTPdq86NnHTjU6QEqpLhKH8hQJdwDRW1tlG4svFPf7kOvy8c3+x4vZc0xywdEwIDAQABAkAT4QIIAYFxpe7BUqCTtdqgsfkPC7jOJns07Osqwb2zwh6pU9KUyk0O5yp6CNfHTGcH3THnYbI1nTyzKqqJnfQBAiEA2uUSg22bat6O5UIlEjVQrsB6hcecwRC63rwRSV7JUYECIQDQCOUjvq44CDWsesT8DVmpIGhHGCmVBHt7gyc0qRpQkwIgHnbdIb+Cbtg0qQGQqT0UUo3lP3MthM0wRMmF2mE/wYECIEg7Pmw50b3sw84eVnT5oa8CbJJ6xj1ScBmDgUJckKF3AiBQeY8dS/XSvcETlq/KVA4nF8ksV2Yx1sFdA4K+1F8+rg==
  defaultPubkey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALHhuiDXFcgNb6+D6Uq+7aI0oakz3avOjZx041OkBKqS4Sh/IUCXcA0VtbZRuLLxT3+5Dr8vHN/seL2XNMcsHRMCAwEAAQ==

    
logging:
 config: classpath:logback-custom.xml
 
pagehelper:
  helperDialect: mysql
  params: count=countSql
  reasonable: true
  supportMethodsArguments: true

server:
  port: 9005
  servlet:
    context-path: /ts-message
registry:
 type: eureka
eureka:
# server:
#    enable-self-preservation: false
 client:
#  register-with-eureka: false
#  fetch-registry: false
  service-url:
   defaultZone: http://*************:8761/eureka
   enabled: true
 instance:
  ip-address: ${spring.cloud.client.ip-address}
  instance-id: ${spring.cloud.client.ip-address}:${server.port}
  prefer-ip-address: true

feign:
  client:
    config:
      default:
        connectTimeout: 10000 # 连接超时时间，单位为毫秒
        readTimeout: 10000 # 读取超时时间，单位为毫秒

spring:
  application:
    name: ts-homs-message
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848 #配置中心地址
        file-extension: yml  #指定yaml格式的配置
        auto-refresh: false # 是否启用动态刷新配置
        encode: utf-8 # 编码
        enabled: false
      discovery:
        server-addr: 127.0.0.1:8848 #服务注册中心地址
        enabled: false
        register-enabled: false

  datasource:
   type: com.alibaba.druid.pool.DruidDataSource
#   driver-class-name: com.mysql.cj.jdbc.Driver
#   url: ****************************************************************************************************************************************************************************************************************************
#   username: root
#   password: 123456
   driver-class-name: dm.jdbc.driver.DmDriver
   url: jdbc:dm://*************:5237/ts_base_oa
   username: sysdba
   password: Trasen_123
   druid:
    initialSize: 1
    min-idle: 1
    max-active: 20
    keep-alive: true
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 60000
    validation-query: select 1 
    validation-query-timeout: 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    use-global-data-source-stat: true
    remove-abandoned: true
    remove-abandoned-timeout: 1800
    pool-prepared-statements: true
    max-open-prepared-statements: 50
    filters: stat,slf4j
    stat-view-servlet:
     enabled: true
     login-username: xtbg
     login-password: 123456@Xtbg
    web-stat-filter: #web监控
     enabled: true
     url-pattern: /*
     exclusions: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
    filter:
     stat: #sql监控
      slow-sql-millis: 1000
      log-slow-sql: true
      enabled: true
      db-type: mysql
     wall: #防火墙
      enabled: false
      db-type: mysql
      multi-statement-allow: true
     config:
        drop-table-allow: false

  http:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
  redis:
    database: 0
    host: *************
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1ms
        min-idle: 0
    password: trasen@123.
    port: 6379
    timeout: 5000
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      location: /home/<USER>/data/tmp
      
springfox:
  base-package: cn.trasen
  description: TS Message Interface Manager
  enable: true
  service-url: http://localhost:8787/ts-message
  title: 数字化运营平台公用消息服务
  version: 1.0.1


mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: 
    - classpath*:mapper/*.xml
    - classpath*:/**/mapper/*.xml


notify-url-header: http://127.0.0.1:9001

smsProxySwitch: 0  #短信开关
wechat:
  cp:
    aesKey: 
    agentId: 1000022
    corpId: ww2c74fea7da3630e9
    secret: MNC1SI20eYxropwbsXDqh3aUQGgZiasA31oWaTtmvd0
    token: 
    url: http://testxtbg.trasen.cn/ts-information
    wxUrl: http://testxtbg.trasen.cn/mobile-container/workbench

wxSwitch: 1   #消息推送开关
wx:
  loginPage: http://testxtbg.trasen.cn/mobile-container/login
  loginUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-oa/
  

wxOrDingtalk: 0
dingtalk :
  redisTokenKey:   #钉钉assessToken key
  app_key: 
  app_secret: 
  agent_id: 
  corp_id: 
  url: 


#流程日报提醒开关
dailyReminder: 1

information-workflow-no: L_00008

pc:
  indexPage: https://*************:9088/container/index
  loginPage: https://*************:9088/container/login
  emailPage: https://*************:9088/container/email/emailManagement
  informationPage: https://*************:9088/container/information/messageRead

verifyurl: 