package cn.trasen.ams.material.bean.returnOrder;

import cn.trasen.ams.material.model.Return;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.returnOrder
 * @className: ReturnDetailResp
 * @author: chenbin
 * @description: 退货单详情响应体
 * @date: 2025/8/7 17:30
 * @version: 1.0
 */
@Data
public class ReturnDetailResp {

    @ApiModelProperty(value = "退货单主数据")
    private Return returnOrder;

    @ApiModelProperty(value = "退货单明细列表")
    private List<ReturnDtlResp> returnDtlList;
}
