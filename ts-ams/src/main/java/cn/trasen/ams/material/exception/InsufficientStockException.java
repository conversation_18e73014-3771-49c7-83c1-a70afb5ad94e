package cn.trasen.ams.material.exception;

import cn.trasen.ams.material.service.MSkuService;
import org.springframework.context.annotation.Bean;

/**
 * 库存不足异常
 *
 * <AUTHOR>
 * @date 2025-08-07
 */

public class InsufficientStockException extends RuntimeException {


    private MSkuService mSkuService;

    private static final long serialVersionUID = 1L;

    /**
     * 物资ID
     */
    private String skuId;

    /**
     * 仓库ID
     */
    private String whId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 当前库存
     */
    private Integer currentStock;

    /**
     * 需要的数量
     */
    private Integer requiredQuantity;

    public InsufficientStockException(String message) {
        super(message);
    }

    public InsufficientStockException(String message, Throwable cause) {
        super(message, cause);
    }

    public InsufficientStockException(String skuId, String whId, String batchNo,
                                      Integer currentStock, Integer requiredQuantity) {
        super(String.format("库存不足：物资[%s], 仓库[%s], 批次[%s], 当前库存[%d], 需要数量[%d]",
                skuId, whId, batchNo, currentStock, Math.abs(requiredQuantity)));
        this.skuId = skuId;
        this.whId = whId;
        this.batchNo = batchNo;
        this.currentStock = currentStock;
        this.requiredQuantity = requiredQuantity;
    }

    public String getSkuId() {
        return skuId;
    }

    public String getWhId() {
        return whId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public Integer getCurrentStock() {
        return currentStock;
    }

    public Integer getRequiredQuantity() {
        return requiredQuantity;
    }
}
