package cn.trasen.ams.material.bean.sku;

import cn.trasen.ams.material.model.MSku;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.sku
 * @className: MSkuStock
 * @author: chenbin
 * @description: TODO
 * @date: 2025/8/6 10:48
 * @version: 1.0
 */

@Data
public class MSkuStock extends MSku {

    @ApiModelProperty("查询库存的方式 stock - 库存，batch - 批次")
    private String queryType;

    @ApiModelProperty("按照md5忽略")
    private List<String> ignoreMd5;

    private List<String> ignoreBatchNoList;

    @ApiModelProperty("库存行指纹")
    private String md5;

    @ApiModelProperty(value = "批次价格")
    private BigDecimal batchPrice;

    @Transient
    @ApiModelProperty(value = "批次历史数量")
    private String originNum;

    @Transient
    @ApiModelProperty(value = "批次剩余库存")
    private String latestNum;

    /**
     * 批次号
     */
    @Column(name = "batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 生产批号
     */
    @Column(name = "prod_no")
    @ApiModelProperty(value = "生产批号")
    private String prodNo;

    /**
     * 生产日期
     */
    @Column(name = "prod_date")
    @ApiModelProperty(value = "生产日期")
    private Date prodDate;

    /**
     * 失效日期
     */
    @Column(name = "expire_date")
    @ApiModelProperty(value = "失效日期")
    private Date expireDate;


}
