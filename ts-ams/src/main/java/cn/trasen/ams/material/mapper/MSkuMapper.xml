<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.MSkuMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.MSku">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="manufacturer_id" jdbcType="VARCHAR" property="manufacturerId"/>
        <result column="supply_id" jdbcType="VARCHAR" property="supplyId"/>
        <result column="category_id" jdbcType="VARCHAR" property="categoryId"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="level" jdbcType="CHAR" property="level"/>
        <result column="flow_no" jdbcType="VARCHAR" property="flowNo"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="min_unit" jdbcType="VARCHAR" property="minUnit"/>
        <result column="unit_coefficient" jdbcType="INTEGER" property="unitCoefficient"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="account_subject" jdbcType="VARCHAR" property="accountSubject"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="reg_no" jdbcType="VARCHAR" property="regNo"/>
        <result column="reg_file" jdbcType="VARCHAR" property="regFile"/>
        <result column="img_file" jdbcType="VARCHAR" property="imgFile"/>
        <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId"/>
        <result column="purchase_type" jdbcType="CHAR" property="purchaseType"/>
        <result column="is_high_value" jdbcType="CHAR" property="isHighValue"/>
        <result column="high_value_cate" jdbcType="VARCHAR" property="highValueCate"/>
        <result column="qgp" jdbcType="DECIMAL" property="qgp"/>
        <result column="is_charge" jdbcType="CHAR" property="isCharge"/>
        <result column="mi_type" jdbcType="CHAR" property="miType"/>
        <result column="mi_no" jdbcType="VARCHAR" property="miNo"/>
        <result column="his_charge_no" jdbcType="VARCHAR" property="hisChargeNo"/>
        <result column="his_charge_name" jdbcType="VARCHAR" property="hisChargeName"/>
        <result column="sp" jdbcType="VARCHAR" property="sp"/>
        <result column="qp" jdbcType="VARCHAR" property="qp"/>
        <result column="origin" jdbcType="CHAR" property="origin"/>
        <result column="use_type" jdbcType="CHAR" property="useType"/>
        <result column="sl_no" jdbcType="VARCHAR" property="slNo"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <sql id="select">
        select
        t1.*,
        t2.`name` as manufacturer_name,
        t3.`name` as supply_name,
        t4.`name` as category_name,
        t5.`name` as warehouse_name,
        COALESCE(t6.num, 0) AS stock
        from m_sku t1
        left join c_manufacturer t2 on t1.`manufacturer_id` = t2.`id`
        left join c_supplier t3 on t1.`supply_id` = t3.`id`
        left join c_category t4 on t1.`category_id` = t4.`id`
        left join c_warehouse t5 on t1.`warehouse_id` = t5.`id`
        <choose>
            <when test="warehouseId != null and warehouseId != ''">
                left join (
                select sku_id, COALESCE(SUM(num), 0) as num
                from m_stock_cur
                where wh_id = #{warehouseId} and is_deleted = 'N'
                group by sku_id
                ) t6 on t1.id = t6.sku_id
            </when>
            <otherwise>
                left join (
                select sku_id, COALESCE(SUM(num), 0) as num
                from m_stock_cur
                where is_deleted = 'N'
                group by sku_id
                ) t6 on t1.id = t6.sku_id
            </otherwise>
        </choose>
        where t1.is_deleted = 'N'
        <if test="name != null and name != ''">
            and( t1.name like concat('%', #{name}, '%') or t1.sp like concat('%', #{name}, '%') or t1.qp like
            concat('%', #{name}, '%') or t1.flow_no like concat('%', #{name}, '%') )
        </if>
        <if test="warehouseId != null and warehouseId != ''">
            and ( t1.warehouse_id like concat('%', #{warehouseId}, '%') or t1.warehouse_id is null )
        </if>
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="categoryId != null and categoryId != ''">
            and t1.category_id = #{categoryId}
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            and t1.category_id in
            <foreach collection="categoryIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idList != null and idList.size() > 0">
            and t1.id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ignoreIdList != null and ignoreIdList.size() > 0">
            and t1.id not in
            <foreach collection="ignoreIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by t1.flow_no asc
    </sql>
    <select id="getList" resultType="cn.trasen.ams.material.model.MSku"
            parameterType="cn.trasen.ams.material.model.MSku">
        <include refid="select"/>
    </select>
    <select id="getListNoPage" resultType="cn.trasen.ams.material.model.MSku"
            parameterType="cn.trasen.ams.material.model.MSku">
        <include refid="select"/>
    </select>
    <select id="getStockList" resultType="cn.trasen.ams.material.bean.sku.MSkuStock"
            parameterType="cn.trasen.ams.material.bean.sku.MSkuStock">
        select
        t1.*,
        t2.`name` as manufacturer_name,
        t3.`name` as supply_name,
        t4.`name` as category_name,
        t5.`name` as warehouse_name,
        COALESCE(t6.num, 0) AS stock,
        COALESCE(t6.price, 0) AS batch_price,
        t6.batch_no,
        t6.prod_no,
        t6.prod_date,
        t6.expire_date
        from m_sku t1
        left join c_manufacturer t2 on t1.`manufacturer_id` = t2.`id`
        left join c_supplier t3 on t1.`supply_id` = t3.`id`
        left join c_category t4 on t1.`category_id` = t4.`id`
        left join c_warehouse t5 on t1.`warehouse_id` = t5.`id`
        left join (
        <choose>
            <when test="queryType != null and queryType == 'batch'">
                select
                t1.sku_id,
                t1.wh_id,
                t1.num,
                t1.batch_no,
                t2.price,
                t2.prod_no,
                t2.prod_date,
                t2.expire_date
                from m_stock_cur t1
                inner join m_batch t2 on t1.`batch_no` = t2.`batch_no` and t2.is_deleted = 'N'
                where t1.is_deleted = 'N' and t1.num > 0
            </when>
            <otherwise>
                select
                t1.sku_id,
                t1.wh_id,
                sum(COALESCE(t1.`num`,0)) as num,
                null as batch_no,
                0 as price,
                null as prod_no,
                null as prod_date,
                null as expire_date
                from m_stock_cur t1
                where t1.is_deleted = 'N' and t1.num > 0
                group by t1.sku_id, t1.wh_id
            </otherwise>
        </choose>
        ) t6 on t1.id = t6.sku_id
        where t1.is_deleted = 'N'
        <if test="name != null and name != ''">
            and( t1.name like concat('%', #{name}, '%') or t1.sp like concat('%', #{name}, '%') or t1.qp like
            concat('%', #{name}, '%') or t1.flow_no like concat('%', #{name}, '%') )
        </if>
        <if test="warehouseId != null and warehouseId != ''">
            and ( t1.warehouse_id like concat('%', #{warehouseId}, '%') or t1.warehouse_id is null )
            and t6.wh_id = #{warehouseId}
        </if>
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="categoryId != null and categoryId != ''">
            and t1.category_id = #{categoryId}
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            and t1.category_id in
            <foreach collection="categoryIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idList != null and idList.size() > 0">
            and t1.id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ignoreIdList != null and ignoreIdList.size() > 0">
            and t1.id not in
            <foreach collection="ignoreIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ignoreMd5 != null and ignoreMd5.size() > 0">
            and t6.`md5` not in
            <foreach collection="ignoreMd5" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ignoreBatchNoList != null and ignoreBatchNoList.size() > 0">
            and t6.`batch_no` not in
            <foreach collection="ignoreBatchNoList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by t1.flow_no asc
    </select>
</mapper>