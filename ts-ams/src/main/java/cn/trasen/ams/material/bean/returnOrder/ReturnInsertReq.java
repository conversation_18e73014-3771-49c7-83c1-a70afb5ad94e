package cn.trasen.ams.material.bean.returnOrder;

import cn.trasen.ams.material.model.Return;
import cn.trasen.ams.material.model.ReturnDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.returnOrder
 * @className: ReturnInsertReq
 * @author: chenbin
 * @description: 物资退货单创建编辑请求体
 * @date: 2025/8/7 16:30
 * @version: 1.0
 */
@Data
@Validated
public class ReturnInsertReq {

    @Valid
    @NotNull(message = "退货单主数据不能为空")
    @ApiModelProperty(value = "退货单主数据")
    private Return returnOrder;

    @Valid
    @NotNull(message = "退货单明细数据不能为空")
    @ApiModelProperty(value = "退货单明细数据")
    private List<ReturnDtl> returnDtlList;
}
