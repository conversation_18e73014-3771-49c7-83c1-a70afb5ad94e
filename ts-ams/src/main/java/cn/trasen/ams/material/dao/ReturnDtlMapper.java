package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.returnOrder.ReturnDtlResp;
import cn.trasen.ams.material.model.ReturnDtl;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ReturnDtlMapper extends Mapper<ReturnDtl> {
    void batchInsert(@Param("returnDtlList") List<ReturnDtl> returnDtlList);

    List<ReturnDtlResp> getReturnDtlExtListByReturnId(@Param("returnId") String returnId, @Param("name") String name);
}