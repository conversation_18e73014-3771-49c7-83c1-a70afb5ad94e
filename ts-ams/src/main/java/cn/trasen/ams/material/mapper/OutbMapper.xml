<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.OutbMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.Outb">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inb_id" jdbcType="VARCHAR" property="inbId" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="mtd_code_id" jdbcType="VARCHAR" property="mtdCodeId" />
    <result column="wh_id" jdbcType="VARCHAR" property="whId" />
    <result column="out_dept_id" jdbcType="VARCHAR" property="outDeptId" />
    <result column="out_dept_name" jdbcType="VARCHAR" property="outDeptName" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="out_date" jdbcType="DATE" property="outDate" />
    <result column="outer_id" jdbcType="VARCHAR" property="outerId" />
    <result column="outer_name" jdbcType="VARCHAR" property="outerName" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="do_time" jdbcType="TIMESTAMP" property="doTime" />
    <result column="stat" jdbcType="VARCHAR" property="stat" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <select id="getList" parameterType="cn.trasen.ams.material.model.Outb" resultType="cn.trasen.ams.material.model.Outb">
      SELECT
      t1.*,
      t2.name as wh_name,
      t5.id as mtd_code_id,
      t5.name as mtd_code_name
      FROM m_outb t1
      LEFT JOIN c_warehouse t2 ON t1.wh_id = t2.id AND t2.is_deleted = 'N'
      LEFT JOIN m_mtd_code_rela t4 ON t1.id = t4.model_id and t4.model_type = '2' AND t4.is_deleted = 'N'
      LEFT JOIN m_mtd_code t5 ON t5.id = t4.mtd_code_id
      WHERE t1.is_deleted = 'N'
      <!-- 动态查询条件 -->
      <if test="whId != null and whId != ''">
          AND t1.wh_id = #{whId}
      </if>
      <if test="flowNo != null and flowNo != ''">
          AND t1.flow_no LIKE CONCAT('%', #{flowNo}, '%')
      </if>

      <if test="mtdCodeId != null and mtdCodeId != ''">
          AND t5.id = #{mtdCodeId}
      </if>

      <if test="whName != null and whName != ''">
          AND t2.name = #{whName}
      </if>
      <if test="outDeptId != null and outDeptId != ''">
          AND t1.out_dept_id = #{outDeptId}
      </if>
      <if test="outDeptName != null and outDeptName != ''">
          AND t1.out_dept_name LIKE CONCAT('%', #{outDeptName}, '%')
      </if>
      <if test="outerName != null and outerName != ''">
          AND t1.outer_name LIKE CONCAT('%', #{outerName}, '%')
      </if>
      <if test="stat != null and stat != ''">
          AND t1.stat = #{stat}
      </if>
      <if test="applyUserName != null and applyUserName != ''">
          AND t1.apply_user_name = #{applyUserName}
      </if>
      <if test="printStat != null and printStat != ''">
          AND t1.print_stat = #{printStat}
      </if>

      <!-- 日期范围查询 -->
      <if test="outDateQuery">
          AND t1.out_date BETWEEN
          SUBSTRING_INDEX(#{outDateQuery}, ',', 1)
          AND
          SUBSTRING_INDEX(#{outDateQuery}, ',', -1)
      </if>

      ORDER BY t1.create_date DESC
  </select>

  <!-- 获取上一条出库单ID -->
  <select id="getPrevId" parameterType="string" resultType="string">
    SELECT id
    FROM m_outb
    WHERE is_deleted = 'N'
      AND create_date &lt; (
        SELECT create_date
        FROM m_outb
        WHERE id = #{currentId} AND is_deleted = 'N'
      )
    ORDER BY create_date DESC
    LIMIT 1
  </select>

  <!-- 获取下一条出库单ID -->
  <select id="getNextId" parameterType="string" resultType="string">
    SELECT id
    FROM m_outb
    WHERE is_deleted = 'N'
      AND create_date &gt; (
        SELECT create_date
        FROM m_outb
        WHERE id = #{currentId} AND is_deleted = 'N'
      )
    ORDER BY create_date ASC
    LIMIT 1
  </select>
</mapper>